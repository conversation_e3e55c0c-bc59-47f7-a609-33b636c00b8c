const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  chatId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Chat',
    required: [true, 'Chat ID is required']
  },
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Sender is required']
  },
  content: {
    type: String,
    required: function() {
      return this.type === 'text' || this.type === 'system';
    },
    maxlength: [2000, 'Message content cannot exceed 2000 characters']
  },
  type: {
    type: String,
    enum: ['text', 'image', 'file', 'system'],
    default: 'text'
  },
  metadata: {
    url: String, // File/image URL
    filename: String,
    size: Number, // File size in bytes
    mimeType: String,
    thumbnailUrl: String // For images
  },
  status: {
    type: String,
    enum: ['sent', 'delivered', 'read'],
    default: 'sent'
  },
  readBy: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    readAt: {
      type: Date,
      default: Date.now
    }
  }],
  replyTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Message',
    default: null
  },
  isRevoked: {
    type: Boolean,
    default: false
  },
  revokedAt: {
    type: Date,
    default: null
  },
  editedAt: {
    type: Date,
    default: null
  },
  reactions: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    emoji: {
      type: String,
      required: true
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true
});

// Index for better query performance
messageSchema.index({ chatId: 1, createdAt: -1 });
messageSchema.index({ sender: 1 });
messageSchema.index({ status: 1 });
messageSchema.index({ isRevoked: 1 });

// Pre-save middleware
messageSchema.pre('save', function(next) {
  // Set editedAt if content is modified and not a new document
  if (this.isModified('content') && !this.isNew) {
    this.editedAt = new Date();
  }
  next();
});

// Static method to get chat messages with pagination
messageSchema.statics.getChatMessages = async function(chatId, page = 1, limit = 50) {
  const skip = (page - 1) * limit;
  
  return await this.find({ 
    chatId, 
    isRevoked: false 
  })
    .populate('sender', 'username avatarUrl')
    .populate('replyTo', 'content sender type')
    .populate('replyTo.sender', 'username')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);
};

// Static method to search messages
messageSchema.statics.searchMessages = async function(chatId, searchTerm, userId) {
  const regex = new RegExp(searchTerm, 'i');
  
  return await this.find({
    chatId,
    content: { $regex: regex },
    isRevoked: false,
    sender: userId // Only search user's own messages for privacy
  })
    .populate('sender', 'username avatarUrl')
    .sort({ createdAt: -1 })
    .limit(100);
};

// Method to mark message as read
messageSchema.methods.markAsRead = function(userId) {
  // Check if already read by this user
  const alreadyRead = this.readBy.some(read => 
    read.user.toString() === userId.toString()
  );
  
  if (!alreadyRead) {
    this.readBy.push({
      user: userId,
      readAt: new Date()
    });
    
    // Update status to 'read' if all chat members have read it
    return this.save();
  }
  
  return Promise.resolve(this);
};

// Method to add reaction
messageSchema.methods.addReaction = function(userId, emoji) {
  // Remove existing reaction from this user
  this.reactions = this.reactions.filter(
    reaction => reaction.user.toString() !== userId.toString()
  );
  
  // Add new reaction
  this.reactions.push({
    user: userId,
    emoji: emoji
  });
  
  return this.save();
};

// Method to remove reaction
messageSchema.methods.removeReaction = function(userId, emoji) {
  this.reactions = this.reactions.filter(
    reaction => !(reaction.user.toString() === userId.toString() && reaction.emoji === emoji)
  );
  
  return this.save();
};

// Method to revoke message
messageSchema.methods.revoke = function() {
  this.isRevoked = true;
  this.revokedAt = new Date();
  this.content = 'This message has been revoked';
  return this.save();
};

// Method to check if message can be revoked (within 2 minutes)
messageSchema.methods.canRevoke = function() {
  const twoMinutesAgo = new Date(Date.now() - 2 * 60 * 1000);
  return this.createdAt > twoMinutesAgo;
};

// Transform JSON output
messageSchema.set('toJSON', {
  transform: function(doc, ret) {
    delete ret.__v;
    return ret;
  }
});

module.exports = mongoose.model('Message', messageSchema);
