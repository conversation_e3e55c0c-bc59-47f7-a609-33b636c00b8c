import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Chat } from '@/types'
import { useAuthStore } from '@/store/authStore'
import { useChatStore } from '@/store/chatStore'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { ChatList } from './ChatList'
import { FriendsList } from './FriendsList'
import { UserProfile as UserProfileComponent } from './UserProfile'
import { NewChatModal } from '@/components/modals/NewChatModal'
import { CreateGroupModal } from '@/components/modals/CreateGroupModal'
import {
  MessageCircle,
  Users,
  User,
  Settings,
  Menu,
  Search,
  LogOut
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface SidebarProps {
  chats: Chat[]
  currentChat: Chat | null
  onToggleSidebar: () => void
}

type SidebarTab = 'chats' | 'friends' | 'profile'

export function Sidebar({ chats, currentChat, onToggleSidebar }: SidebarProps) {
  const { user, logout } = useAuthStore()
  const { setCurrentChat } = useChatStore()
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState<SidebarTab>('chats')
  const [searchQuery, setSearchQuery] = useState('')
  const [showNewChatModal, setShowNewChatModal] = useState(false)
  const [showCreateGroupModal, setShowCreateGroupModal] = useState(false)

  const handleLogout = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  const filteredChats = chats.filter(chat => {
    if (!searchQuery) return true
    
    const searchLower = searchQuery.toLowerCase()
    if (chat.type === 'private') {
      const otherUser = chat.members.find(member => member._id !== user?._id)
      return otherUser?.username.toLowerCase().includes(searchLower) || false
    } else {
      return chat.name?.toLowerCase().includes(searchLower) || false
    }
  })

  return (
    <div className="h-full flex flex-col bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <div className="bg-primary-600 p-2 rounded-lg">
              <MessageCircle className="h-5 w-5 text-white" />
            </div>
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">
              KingChat
            </h1>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleSidebar}
            className="lg:hidden"
          >
            <Menu className="h-4 w-4" />
          </Button>
        </div>

        {/* Search */}
        <div className="relative">
          <Input
            type="text"
            placeholder="Search chats..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            leftIcon={<Search className="h-4 w-4" />}
            className="pr-10"
          />
        </div>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200 dark:border-gray-700">
        <button
          onClick={() => setActiveTab('chats')}
          className={cn(
            'flex-1 px-4 py-3 text-sm font-medium transition-colors',
            activeTab === 'chats'
              ? 'text-primary-600 border-b-2 border-primary-600 bg-primary-50 dark:bg-primary-900/20'
              : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
          )}
        >
          <div className="flex items-center justify-center space-x-2">
            <MessageCircle className="h-4 w-4" />
            <span>Chats</span>
          </div>
        </button>
        <button
          onClick={() => setActiveTab('friends')}
          className={cn(
            'flex-1 px-4 py-3 text-sm font-medium transition-colors',
            activeTab === 'friends'
              ? 'text-primary-600 border-b-2 border-primary-600 bg-primary-50 dark:bg-primary-900/20'
              : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
          )}
        >
          <div className="flex items-center justify-center space-x-2">
            <Users className="h-4 w-4" />
            <span>Friends</span>
          </div>
        </button>
        <button
          onClick={() => setActiveTab('profile')}
          className={cn(
            'flex-1 px-4 py-3 text-sm font-medium transition-colors',
            activeTab === 'profile'
              ? 'text-primary-600 border-b-2 border-primary-600 bg-primary-50 dark:bg-primary-900/20'
              : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
          )}
        >
          <div className="flex items-center justify-center space-x-2">
            <User className="h-4 w-4" />
            <span>Profile</span>
          </div>
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {activeTab === 'chats' && (
          <ChatList 
            chats={filteredChats}
            currentChat={currentChat}
            onSelectChat={setCurrentChat}
            onCreateChat={() => setShowNewChatModal(true)}
          />
        )}
        {activeTab === 'friends' && (
          <FriendsList />
        )}
        {activeTab === 'profile' && (
          <UserProfileComponent />
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-medium">
                {user?.username?.charAt(0).toUpperCase()}
              </span>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {user?.username}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {user?.status}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/settings')}
              title="Settings"
            >
              <Settings className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLogout}
            >
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Modals */}
      <NewChatModal
        isOpen={showNewChatModal}
        onClose={() => setShowNewChatModal(false)}
        onCreateChat={() => setShowNewChatModal(false)}
      />
      
      <CreateGroupModal
        isOpen={showCreateGroupModal}
        onClose={() => setShowCreateGroupModal(false)}
        onCreateGroup={() => setShowCreateGroupModal(false)}
      />
    </div>
  )
}
