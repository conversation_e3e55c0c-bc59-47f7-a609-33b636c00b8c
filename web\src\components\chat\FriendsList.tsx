import { useState, useEffect } from 'react'
import { useChatStore } from '@/store/chatStore'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { UserProfile } from '@/types'
import { cn, getInitials, getStatusColor, getStatusText, formatLastSeen } from '@/lib/utils'
import { AddFriendModal } from '@/components/modals/AddFriendModal'
import { FriendRequestsModal } from '@/components/modals/FriendRequestsModal'
import {
  Search,
  MessageCircle,
  UserPlus,
  MoreVertical,
  UserMinus,
  Phone,
  Video,
  Info,
  Bell
} from 'lucide-react'
import toast from 'react-hot-toast'

export function FriendsList() {
  const { friends, loadFriends, createPrivateChat } = useChatStore()
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [sortBy, setSortBy] = useState<'status' | 'name' | 'recent'>('status')
  const [showActions, setShowActions] = useState<string | null>(null)
  const [showAddFriendModal, setShowAddFriendModal] = useState(false)
  const [showFriendRequestsModal, setShowFriendRequestsModal] = useState(false)

  useEffect(() => {
    loadFriends()
  }, [loadFriends])

  const filteredAndSortedFriends = friends
    .filter(friend =>
      friend.username.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'status':
          const statusOrder = { online: 0, away: 1, dnd: 2, offline: 3 }
          return statusOrder[a.status] - statusOrder[b.status]
        case 'name':
          return a.username.localeCompare(b.username)
        case 'recent':
          return new Date(b.lastSeen).getTime() - new Date(a.lastSeen).getTime()
        default:
          return 0
      }
    })

  const handleStartChat = async (friend: UserProfile) => {
    setIsLoading(true)
    try {
      await createPrivateChat(friend._id)
      toast.success(`Started chat with ${friend.username}`)
    } catch (error: any) {
      toast.error(error.message || 'Failed to start chat')
    } finally {
      setIsLoading(false)
    }
  }

  const handleRemoveFriend = async (friend: UserProfile) => {
    if (window.confirm(`Are you sure you want to remove ${friend.username} from your friends?`)) {
      try {
        // This would call an API to remove friend
        toast.success(`Removed ${friend.username} from friends`)
      } catch (error: any) {
        toast.error(error.message || 'Failed to remove friend')
      }
    }
  }

  const handleCall = (friend: UserProfile) => {
    // This would initiate a voice call
    toast(`Calling ${friend.username}...`, { icon: '📞' })
  }

  const handleVideoCall = (friend: UserProfile) => {
    // This would initiate a video call
    toast(`Starting video call with ${friend.username}...`, { icon: '📹' })
  }

  const handleViewProfile = (friend: UserProfile) => {
    // This would open a profile modal or navigate to profile page
    toast(`Viewing ${friend.username}'s profile`, { icon: '👤' })
  }

  if (friends.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
            <UserPlus className="w-8 h-8 text-gray-400" />
          </div>
          <p className="text-gray-500 dark:text-gray-400 text-sm">
            No friends yet
          </p>
          <p className="text-gray-400 dark:text-gray-500 text-xs mt-1">
            Add friends to start chatting
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* Search and controls */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="space-y-3">
          {/* Search input */}
          <div className="relative">
            <Input
              type="text"
              placeholder="Search friends..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftIcon={<Search className="h-4 w-4" />}
            />
          </div>

          {/* Sort options and Add Friend button */}
          <div className="flex items-center justify-between">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'status' | 'name' | 'recent')}
              className="text-xs bg-gray-100 dark:bg-gray-700 border-0 rounded px-2 py-1"
            >
              <option value="status">Status</option>
              <option value="name">Name</option>
              <option value="recent">Recent</option>
            </select>

            <div className="flex space-x-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setShowFriendRequestsModal(true)}
                className="flex items-center space-x-1"
              >
                <Bell className="h-4 w-4" />
                <span>Requests</span>
              </Button>

              <Button
                variant="primary"
                size="sm"
                onClick={() => setShowAddFriendModal(true)}
                className="flex items-center space-x-1"
              >
                <UserPlus className="h-4 w-4" />
                <span>Add Friend</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Friends list */}
      <div className="flex-1 overflow-y-auto">
        {filteredAndSortedFriends.length === 0 ? (
          <div className="flex items-center justify-center p-4">
            <p className="text-gray-500 dark:text-gray-400 text-sm">
              {searchQuery ? 'No friends found' : 'No friends yet'}
            </p>
          </div>
        ) : (
          filteredAndSortedFriends.map((friend) => (
            <div
              key={friend._id}
              className="p-4 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors group"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {/* Avatar */}
                  <div className="relative">
                    {friend.avatarUrl ? (
                      <img
                        src={friend.avatarUrl}
                        alt={friend.username}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center">
                        <span className="text-white font-medium text-sm">
                          {getInitials(friend.username)}
                        </span>
                      </div>
                    )}
                    
                    {/* Status indicator */}
                    <div className={cn(
                      'absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-gray-800',
                      getStatusColor(friend.status)
                    )} />
                  </div>

                  {/* Friend info */}
                  <div className="flex-1 min-w-0">
                    <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {friend.username}
                    </h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {friend.status === 'offline' 
                        ? formatLastSeen(friend.lastSeen)
                        : getStatusText(friend.status)
                      }
                    </p>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleStartChat(friend)}
                    disabled={isLoading}
                    className="text-primary-600 hover:text-primary-700"
                    title="Start chat"
                  >
                    <MessageCircle className="h-4 w-4" />
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleCall(friend)}
                    className="text-green-600 hover:text-green-700"
                    title="Voice call"
                  >
                    <Phone className="h-4 w-4" />
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleVideoCall(friend)}
                    className="text-blue-600 hover:text-blue-700"
                    title="Video call"
                  >
                    <Video className="h-4 w-4" />
                  </Button>
                  
                  <div className="relative">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowActions(showActions === friend._id ? null : friend._id)}
                      className="text-gray-600 hover:text-gray-700"
                      title="More options"
                    >
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                    
                    {/* Dropdown menu */}
                    {showActions === friend._id && (
                      <div className="absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-10">
                        <button
                          onClick={() => {
                            handleViewProfile(friend)
                            setShowActions(null)
                          }}
                          className="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2"
                        >
                          <Info className="h-4 w-4" />
                          <span>View Profile</span>
                        </button>
                        <button
                          onClick={() => {
                            handleRemoveFriend(friend)
                            setShowActions(null)
                          }}
                          className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2"
                        >
                          <UserMinus className="h-4 w-4" />
                          <span>Remove Friend</span>
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

    </div>
  )
}
