var py=Object.defineProperty;var my=(e,t,r)=>t in e?py(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var an=(e,t,r)=>(my(e,typeof t!="symbol"?t+"":t,r),r);function hy(e,t){for(var r=0;r<t.length;r++){const n=t[r];if(typeof n!="string"&&!Array.isArray(n)){for(const i in n)if(i!=="default"&&!(i in e)){const a=Object.getOwnPropertyDescriptor(n,i);a&&Object.defineProperty(e,i,a.get?a:{enumerable:!0,get:()=>n[i]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))n(i);new MutationObserver(i=>{for(const a of i)if(a.type==="childList")for(const s of a.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&n(s)}).observe(document,{childList:!0,subtree:!0});function r(i){const a={};return i.integrity&&(a.integrity=i.integrity),i.referrerPolicy&&(a.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?a.credentials="include":i.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function n(i){if(i.ep)return;i.ep=!0;const a=r(i);fetch(i.href,a)}})();function iu(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Zp={exports:{}},yo={},em={exports:{}},ee={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Na=Symbol.for("react.element"),gy=Symbol.for("react.portal"),vy=Symbol.for("react.fragment"),yy=Symbol.for("react.strict_mode"),xy=Symbol.for("react.profiler"),wy=Symbol.for("react.provider"),by=Symbol.for("react.context"),ky=Symbol.for("react.forward_ref"),Sy=Symbol.for("react.suspense"),Ey=Symbol.for("react.memo"),jy=Symbol.for("react.lazy"),Nd=Symbol.iterator;function Ny(e){return e===null||typeof e!="object"?null:(e=Nd&&e[Nd]||e["@@iterator"],typeof e=="function"?e:null)}var tm={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},rm=Object.assign,nm={};function hi(e,t,r){this.props=e,this.context=t,this.refs=nm,this.updater=r||tm}hi.prototype.isReactComponent={};hi.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};hi.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function im(){}im.prototype=hi.prototype;function au(e,t,r){this.props=e,this.context=t,this.refs=nm,this.updater=r||tm}var su=au.prototype=new im;su.constructor=au;rm(su,hi.prototype);su.isPureReactComponent=!0;var Cd=Array.isArray,am=Object.prototype.hasOwnProperty,ou={current:null},sm={key:!0,ref:!0,__self:!0,__source:!0};function om(e,t,r){var n,i={},a=null,s=null;if(t!=null)for(n in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(a=""+t.key),t)am.call(t,n)&&!sm.hasOwnProperty(n)&&(i[n]=t[n]);var o=arguments.length-2;if(o===1)i.children=r;else if(1<o){for(var c=Array(o),u=0;u<o;u++)c[u]=arguments[u+2];i.children=c}if(e&&e.defaultProps)for(n in o=e.defaultProps,o)i[n]===void 0&&(i[n]=o[n]);return{$$typeof:Na,type:e,key:a,ref:s,props:i,_owner:ou.current}}function Cy(e,t){return{$$typeof:Na,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function lu(e){return typeof e=="object"&&e!==null&&e.$$typeof===Na}function _y(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(r){return t[r]})}var _d=/\/+/g;function el(e,t){return typeof e=="object"&&e!==null&&e.key!=null?_y(""+e.key):t.toString(36)}function ds(e,t,r,n,i){var a=typeof e;(a==="undefined"||a==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(a){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Na:case gy:s=!0}}if(s)return s=e,i=i(s),e=n===""?"."+el(s,0):n,Cd(i)?(r="",e!=null&&(r=e.replace(_d,"$&/")+"/"),ds(i,t,r,"",function(u){return u})):i!=null&&(lu(i)&&(i=Cy(i,r+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(_d,"$&/")+"/")+e)),t.push(i)),1;if(s=0,n=n===""?".":n+":",Cd(e))for(var o=0;o<e.length;o++){a=e[o];var c=n+el(a,o);s+=ds(a,t,r,c,i)}else if(c=Ny(e),typeof c=="function")for(e=c.call(e),o=0;!(a=e.next()).done;)a=a.value,c=n+el(a,o++),s+=ds(a,t,r,c,i);else if(a==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Va(e,t,r){if(e==null)return e;var n=[],i=0;return ds(e,n,"","",function(a){return t.call(r,a,i++)}),n}function Ty(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(r){(e._status===0||e._status===-1)&&(e._status=1,e._result=r)},function(r){(e._status===0||e._status===-1)&&(e._status=2,e._result=r)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var nt={current:null},fs={transition:null},Py={ReactCurrentDispatcher:nt,ReactCurrentBatchConfig:fs,ReactCurrentOwner:ou};function lm(){throw Error("act(...) is not supported in production builds of React.")}ee.Children={map:Va,forEach:function(e,t,r){Va(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return Va(e,function(){t++}),t},toArray:function(e){return Va(e,function(t){return t})||[]},only:function(e){if(!lu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};ee.Component=hi;ee.Fragment=vy;ee.Profiler=xy;ee.PureComponent=au;ee.StrictMode=yy;ee.Suspense=Sy;ee.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Py;ee.act=lm;ee.cloneElement=function(e,t,r){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var n=rm({},e.props),i=e.key,a=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(a=t.ref,s=ou.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var o=e.type.defaultProps;for(c in t)am.call(t,c)&&!sm.hasOwnProperty(c)&&(n[c]=t[c]===void 0&&o!==void 0?o[c]:t[c])}var c=arguments.length-2;if(c===1)n.children=r;else if(1<c){o=Array(c);for(var u=0;u<c;u++)o[u]=arguments[u+2];n.children=o}return{$$typeof:Na,type:e.type,key:i,ref:a,props:n,_owner:s}};ee.createContext=function(e){return e={$$typeof:by,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:wy,_context:e},e.Consumer=e};ee.createElement=om;ee.createFactory=function(e){var t=om.bind(null,e);return t.type=e,t};ee.createRef=function(){return{current:null}};ee.forwardRef=function(e){return{$$typeof:ky,render:e}};ee.isValidElement=lu;ee.lazy=function(e){return{$$typeof:jy,_payload:{_status:-1,_result:e},_init:Ty}};ee.memo=function(e,t){return{$$typeof:Ey,type:e,compare:t===void 0?null:t}};ee.startTransition=function(e){var t=fs.transition;fs.transition={};try{e()}finally{fs.transition=t}};ee.unstable_act=lm;ee.useCallback=function(e,t){return nt.current.useCallback(e,t)};ee.useContext=function(e){return nt.current.useContext(e)};ee.useDebugValue=function(){};ee.useDeferredValue=function(e){return nt.current.useDeferredValue(e)};ee.useEffect=function(e,t){return nt.current.useEffect(e,t)};ee.useId=function(){return nt.current.useId()};ee.useImperativeHandle=function(e,t,r){return nt.current.useImperativeHandle(e,t,r)};ee.useInsertionEffect=function(e,t){return nt.current.useInsertionEffect(e,t)};ee.useLayoutEffect=function(e,t){return nt.current.useLayoutEffect(e,t)};ee.useMemo=function(e,t){return nt.current.useMemo(e,t)};ee.useReducer=function(e,t,r){return nt.current.useReducer(e,t,r)};ee.useRef=function(e){return nt.current.useRef(e)};ee.useState=function(e){return nt.current.useState(e)};ee.useSyncExternalStore=function(e,t,r){return nt.current.useSyncExternalStore(e,t,r)};ee.useTransition=function(){return nt.current.useTransition()};ee.version="18.3.1";em.exports=ee;var S=em.exports;const Ve=iu(S),Oy=hy({__proto__:null,default:Ve},[S]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ry=S,Ay=Symbol.for("react.element"),Dy=Symbol.for("react.fragment"),Ly=Object.prototype.hasOwnProperty,My=Ry.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Fy={key:!0,ref:!0,__self:!0,__source:!0};function cm(e,t,r){var n,i={},a=null,s=null;r!==void 0&&(a=""+r),t.key!==void 0&&(a=""+t.key),t.ref!==void 0&&(s=t.ref);for(n in t)Ly.call(t,n)&&!Fy.hasOwnProperty(n)&&(i[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps,t)i[n]===void 0&&(i[n]=t[n]);return{$$typeof:Ay,type:e,key:a,ref:s,props:i,_owner:My.current}}yo.Fragment=Dy;yo.jsx=cm;yo.jsxs=cm;Zp.exports=yo;var l=Zp.exports,Il={},um={exports:{}},yt={},dm={exports:{}},fm={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(D,z){var H=D.length;D.push(z);e:for(;0<H;){var K=H-1>>>1,ae=D[K];if(0<i(ae,z))D[K]=z,D[H]=ae,H=K;else break e}}function r(D){return D.length===0?null:D[0]}function n(D){if(D.length===0)return null;var z=D[0],H=D.pop();if(H!==z){D[0]=H;e:for(var K=0,ae=D.length,at=ae>>>1;K<at;){var je=2*(K+1)-1,wt=D[je],$e=je+1,Tt=D[$e];if(0>i(wt,H))$e<ae&&0>i(Tt,wt)?(D[K]=Tt,D[$e]=H,K=$e):(D[K]=wt,D[je]=H,K=je);else if($e<ae&&0>i(Tt,H))D[K]=Tt,D[$e]=H,K=$e;else break e}}return z}function i(D,z){var H=D.sortIndex-z.sortIndex;return H!==0?H:D.id-z.id}if(typeof performance=="object"&&typeof performance.now=="function"){var a=performance;e.unstable_now=function(){return a.now()}}else{var s=Date,o=s.now();e.unstable_now=function(){return s.now()-o}}var c=[],u=[],d=1,f=null,h=3,w=!1,g=!1,y=!1,x=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(D){for(var z=r(u);z!==null;){if(z.callback===null)n(u);else if(z.startTime<=D)n(u),z.sortIndex=z.expirationTime,t(c,z);else break;z=r(u)}}function k(D){if(y=!1,v(D),!g)if(r(c)!==null)g=!0,Q(_);else{var z=r(u);z!==null&&de(k,z.startTime-D)}}function _(D,z){g=!1,y&&(y=!1,m(O),O=-1),w=!0;var H=h;try{for(v(z),f=r(c);f!==null&&(!(f.expirationTime>z)||D&&!Z());){var K=f.callback;if(typeof K=="function"){f.callback=null,h=f.priorityLevel;var ae=K(f.expirationTime<=z);z=e.unstable_now(),typeof ae=="function"?f.callback=ae:f===r(c)&&n(c),v(z)}else n(c);f=r(c)}if(f!==null)var at=!0;else{var je=r(u);je!==null&&de(k,je.startTime-z),at=!1}return at}finally{f=null,h=H,w=!1}}var A=!1,P=null,O=-1,L=5,U=-1;function Z(){return!(e.unstable_now()-U<L)}function M(){if(P!==null){var D=e.unstable_now();U=D;var z=!0;try{z=P(!0,D)}finally{z?W():(A=!1,P=null)}}else A=!1}var W;if(typeof p=="function")W=function(){p(M)};else if(typeof MessageChannel<"u"){var V=new MessageChannel,q=V.port2;V.port1.onmessage=M,W=function(){q.postMessage(null)}}else W=function(){x(M,0)};function Q(D){P=D,A||(A=!0,W())}function de(D,z){O=x(function(){D(e.unstable_now())},z)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(D){D.callback=null},e.unstable_continueExecution=function(){g||w||(g=!0,Q(_))},e.unstable_forceFrameRate=function(D){0>D||125<D?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):L=0<D?Math.floor(1e3/D):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return r(c)},e.unstable_next=function(D){switch(h){case 1:case 2:case 3:var z=3;break;default:z=h}var H=h;h=z;try{return D()}finally{h=H}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(D,z){switch(D){case 1:case 2:case 3:case 4:case 5:break;default:D=3}var H=h;h=D;try{return z()}finally{h=H}},e.unstable_scheduleCallback=function(D,z,H){var K=e.unstable_now();switch(typeof H=="object"&&H!==null?(H=H.delay,H=typeof H=="number"&&0<H?K+H:K):H=K,D){case 1:var ae=-1;break;case 2:ae=250;break;case 5:ae=**********;break;case 4:ae=1e4;break;default:ae=5e3}return ae=H+ae,D={id:d++,callback:z,priorityLevel:D,startTime:H,expirationTime:ae,sortIndex:-1},H>K?(D.sortIndex=H,t(u,D),r(c)===null&&D===r(u)&&(y?(m(O),O=-1):y=!0,de(k,H-K))):(D.sortIndex=ae,t(c,D),g||w||(g=!0,Q(_))),D},e.unstable_shouldYield=Z,e.unstable_wrapCallback=function(D){var z=h;return function(){var H=h;h=z;try{return D.apply(this,arguments)}finally{h=H}}}})(fm);dm.exports=fm;var zy=dm.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Iy=S,vt=zy;function R(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var pm=new Set,aa={};function jn(e,t){ni(e,t),ni(e+"Capture",t)}function ni(e,t){for(aa[e]=t,e=0;e<t.length;e++)pm.add(t[e])}var pr=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ul=Object.prototype.hasOwnProperty,Uy=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Td={},Pd={};function $y(e){return Ul.call(Pd,e)?!0:Ul.call(Td,e)?!1:Uy.test(e)?Pd[e]=!0:(Td[e]=!0,!1)}function By(e,t,r,n){if(r!==null&&r.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return n?!1:r!==null?!r.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Vy(e,t,r,n){if(t===null||typeof t>"u"||By(e,t,r,n))return!0;if(n)return!1;if(r!==null)switch(r.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function it(e,t,r,n,i,a,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=n,this.attributeNamespace=i,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=s}var Ye={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ye[e]=new it(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ye[t]=new it(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ye[e]=new it(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ye[e]=new it(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ye[e]=new it(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ye[e]=new it(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ye[e]=new it(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ye[e]=new it(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ye[e]=new it(e,5,!1,e.toLowerCase(),null,!1,!1)});var cu=/[\-:]([a-z])/g;function uu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(cu,uu);Ye[t]=new it(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(cu,uu);Ye[t]=new it(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(cu,uu);Ye[t]=new it(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ye[e]=new it(e,1,!1,e.toLowerCase(),null,!1,!1)});Ye.xlinkHref=new it("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ye[e]=new it(e,1,!1,e.toLowerCase(),null,!0,!0)});function du(e,t,r,n){var i=Ye.hasOwnProperty(t)?Ye[t]:null;(i!==null?i.type!==0:n||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Vy(t,r,i,n)&&(r=null),n||i===null?$y(t)&&(r===null?e.removeAttribute(t):e.setAttribute(t,""+r)):i.mustUseProperty?e[i.propertyName]=r===null?i.type===3?!1:"":r:(t=i.attributeName,n=i.attributeNamespace,r===null?e.removeAttribute(t):(i=i.type,r=i===3||i===4&&r===!0?"":""+r,n?e.setAttributeNS(n,t,r):e.setAttribute(t,r))))}var wr=Iy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Wa=Symbol.for("react.element"),Fn=Symbol.for("react.portal"),zn=Symbol.for("react.fragment"),fu=Symbol.for("react.strict_mode"),$l=Symbol.for("react.profiler"),mm=Symbol.for("react.provider"),hm=Symbol.for("react.context"),pu=Symbol.for("react.forward_ref"),Bl=Symbol.for("react.suspense"),Vl=Symbol.for("react.suspense_list"),mu=Symbol.for("react.memo"),_r=Symbol.for("react.lazy"),gm=Symbol.for("react.offscreen"),Od=Symbol.iterator;function Pi(e){return e===null||typeof e!="object"?null:(e=Od&&e[Od]||e["@@iterator"],typeof e=="function"?e:null)}var Se=Object.assign,tl;function Vi(e){if(tl===void 0)try{throw Error()}catch(r){var t=r.stack.trim().match(/\n( *(at )?)/);tl=t&&t[1]||""}return`
`+tl+e}var rl=!1;function nl(e,t){if(!e||rl)return"";rl=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var n=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){n=u}e.call(t.prototype)}else{try{throw Error()}catch(u){n=u}e()}}catch(u){if(u&&n&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),a=n.stack.split(`
`),s=i.length-1,o=a.length-1;1<=s&&0<=o&&i[s]!==a[o];)o--;for(;1<=s&&0<=o;s--,o--)if(i[s]!==a[o]){if(s!==1||o!==1)do if(s--,o--,0>o||i[s]!==a[o]){var c=`
`+i[s].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=s&&0<=o);break}}}finally{rl=!1,Error.prepareStackTrace=r}return(e=e?e.displayName||e.name:"")?Vi(e):""}function Wy(e){switch(e.tag){case 5:return Vi(e.type);case 16:return Vi("Lazy");case 13:return Vi("Suspense");case 19:return Vi("SuspenseList");case 0:case 2:case 15:return e=nl(e.type,!1),e;case 11:return e=nl(e.type.render,!1),e;case 1:return e=nl(e.type,!0),e;default:return""}}function Wl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case zn:return"Fragment";case Fn:return"Portal";case $l:return"Profiler";case fu:return"StrictMode";case Bl:return"Suspense";case Vl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case hm:return(e.displayName||"Context")+".Consumer";case mm:return(e._context.displayName||"Context")+".Provider";case pu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case mu:return t=e.displayName||null,t!==null?t:Wl(e.type)||"Memo";case _r:t=e._payload,e=e._init;try{return Wl(e(t))}catch{}}return null}function qy(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Wl(t);case 8:return t===fu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Yr(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function vm(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Hy(e){var t=vm(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),n=""+e[t];if(!e.hasOwnProperty(t)&&typeof r<"u"&&typeof r.get=="function"&&typeof r.set=="function"){var i=r.get,a=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){n=""+s,a.call(this,s)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return n},setValue:function(s){n=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function qa(e){e._valueTracker||(e._valueTracker=Hy(e))}function ym(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),n="";return e&&(n=vm(e)?e.checked?"true":"false":e.value),e=n,e!==r?(t.setValue(e),!0):!1}function As(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function ql(e,t){var r=t.checked;return Se({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:r??e._wrapperState.initialChecked})}function Rd(e,t){var r=t.defaultValue==null?"":t.defaultValue,n=t.checked!=null?t.checked:t.defaultChecked;r=Yr(t.value!=null?t.value:r),e._wrapperState={initialChecked:n,initialValue:r,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function xm(e,t){t=t.checked,t!=null&&du(e,"checked",t,!1)}function Hl(e,t){xm(e,t);var r=Yr(t.value),n=t.type;if(r!=null)n==="number"?(r===0&&e.value===""||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if(n==="submit"||n==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Yl(e,t.type,r):t.hasOwnProperty("defaultValue")&&Yl(e,t.type,Yr(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ad(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var n=t.type;if(!(n!=="submit"&&n!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}r=e.name,r!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,r!==""&&(e.name=r)}function Yl(e,t,r){(t!=="number"||As(e.ownerDocument)!==e)&&(r==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}var Wi=Array.isArray;function Qn(e,t,r,n){if(e=e.options,t){t={};for(var i=0;i<r.length;i++)t["$"+r[i]]=!0;for(r=0;r<e.length;r++)i=t.hasOwnProperty("$"+e[r].value),e[r].selected!==i&&(e[r].selected=i),i&&n&&(e[r].defaultSelected=!0)}else{for(r=""+Yr(r),t=null,i=0;i<e.length;i++){if(e[i].value===r){e[i].selected=!0,n&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Kl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(R(91));return Se({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Dd(e,t){var r=t.value;if(r==null){if(r=t.children,t=t.defaultValue,r!=null){if(t!=null)throw Error(R(92));if(Wi(r)){if(1<r.length)throw Error(R(93));r=r[0]}t=r}t==null&&(t=""),r=t}e._wrapperState={initialValue:Yr(r)}}function wm(e,t){var r=Yr(t.value),n=Yr(t.defaultValue);r!=null&&(r=""+r,r!==e.value&&(e.value=r),t.defaultValue==null&&e.defaultValue!==r&&(e.defaultValue=r)),n!=null&&(e.defaultValue=""+n)}function Ld(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function bm(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ql(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?bm(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ha,km=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,r,n,i){MSApp.execUnsafeLocalFunction(function(){return e(t,r,n,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ha=Ha||document.createElement("div"),Ha.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ha.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function sa(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&r.nodeType===3){r.nodeValue=t;return}}e.textContent=t}var Ki={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Yy=["Webkit","ms","Moz","O"];Object.keys(Ki).forEach(function(e){Yy.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Ki[t]=Ki[e]})});function Sm(e,t,r){return t==null||typeof t=="boolean"||t===""?"":r||typeof t!="number"||t===0||Ki.hasOwnProperty(e)&&Ki[e]?(""+t).trim():t+"px"}function Em(e,t){e=e.style;for(var r in t)if(t.hasOwnProperty(r)){var n=r.indexOf("--")===0,i=Sm(r,t[r],n);r==="float"&&(r="cssFloat"),n?e.setProperty(r,i):e[r]=i}}var Ky=Se({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Gl(e,t){if(t){if(Ky[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(R(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(R(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(R(61))}if(t.style!=null&&typeof t.style!="object")throw Error(R(62))}}function Xl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Jl=null;function hu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Zl=null,Gn=null,Xn=null;function Md(e){if(e=Ta(e)){if(typeof Zl!="function")throw Error(R(280));var t=e.stateNode;t&&(t=So(t),Zl(e.stateNode,e.type,t))}}function jm(e){Gn?Xn?Xn.push(e):Xn=[e]:Gn=e}function Nm(){if(Gn){var e=Gn,t=Xn;if(Xn=Gn=null,Md(e),t)for(e=0;e<t.length;e++)Md(t[e])}}function Cm(e,t){return e(t)}function _m(){}var il=!1;function Tm(e,t,r){if(il)return e(t,r);il=!0;try{return Cm(e,t,r)}finally{il=!1,(Gn!==null||Xn!==null)&&(_m(),Nm())}}function oa(e,t){var r=e.stateNode;if(r===null)return null;var n=So(r);if(n===null)return null;r=n[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(e=e.type,n=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!n;break e;default:e=!1}if(e)return null;if(r&&typeof r!="function")throw Error(R(231,t,typeof r));return r}var ec=!1;if(pr)try{var Oi={};Object.defineProperty(Oi,"passive",{get:function(){ec=!0}}),window.addEventListener("test",Oi,Oi),window.removeEventListener("test",Oi,Oi)}catch{ec=!1}function Qy(e,t,r,n,i,a,s,o,c){var u=Array.prototype.slice.call(arguments,3);try{t.apply(r,u)}catch(d){this.onError(d)}}var Qi=!1,Ds=null,Ls=!1,tc=null,Gy={onError:function(e){Qi=!0,Ds=e}};function Xy(e,t,r,n,i,a,s,o,c){Qi=!1,Ds=null,Qy.apply(Gy,arguments)}function Jy(e,t,r,n,i,a,s,o,c){if(Xy.apply(this,arguments),Qi){if(Qi){var u=Ds;Qi=!1,Ds=null}else throw Error(R(198));Ls||(Ls=!0,tc=u)}}function Nn(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(r=t.return),e=t.return;while(e)}return t.tag===3?r:null}function Pm(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Fd(e){if(Nn(e)!==e)throw Error(R(188))}function Zy(e){var t=e.alternate;if(!t){if(t=Nn(e),t===null)throw Error(R(188));return t!==e?null:e}for(var r=e,n=t;;){var i=r.return;if(i===null)break;var a=i.alternate;if(a===null){if(n=i.return,n!==null){r=n;continue}break}if(i.child===a.child){for(a=i.child;a;){if(a===r)return Fd(i),e;if(a===n)return Fd(i),t;a=a.sibling}throw Error(R(188))}if(r.return!==n.return)r=i,n=a;else{for(var s=!1,o=i.child;o;){if(o===r){s=!0,r=i,n=a;break}if(o===n){s=!0,n=i,r=a;break}o=o.sibling}if(!s){for(o=a.child;o;){if(o===r){s=!0,r=a,n=i;break}if(o===n){s=!0,n=a,r=i;break}o=o.sibling}if(!s)throw Error(R(189))}}if(r.alternate!==n)throw Error(R(190))}if(r.tag!==3)throw Error(R(188));return r.stateNode.current===r?e:t}function Om(e){return e=Zy(e),e!==null?Rm(e):null}function Rm(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Rm(e);if(t!==null)return t;e=e.sibling}return null}var Am=vt.unstable_scheduleCallback,zd=vt.unstable_cancelCallback,e0=vt.unstable_shouldYield,t0=vt.unstable_requestPaint,Ne=vt.unstable_now,r0=vt.unstable_getCurrentPriorityLevel,gu=vt.unstable_ImmediatePriority,Dm=vt.unstable_UserBlockingPriority,Ms=vt.unstable_NormalPriority,n0=vt.unstable_LowPriority,Lm=vt.unstable_IdlePriority,xo=null,Zt=null;function i0(e){if(Zt&&typeof Zt.onCommitFiberRoot=="function")try{Zt.onCommitFiberRoot(xo,e,void 0,(e.current.flags&128)===128)}catch{}}var It=Math.clz32?Math.clz32:o0,a0=Math.log,s0=Math.LN2;function o0(e){return e>>>=0,e===0?32:31-(a0(e)/s0|0)|0}var Ya=64,Ka=4194304;function qi(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Fs(e,t){var r=e.pendingLanes;if(r===0)return 0;var n=0,i=e.suspendedLanes,a=e.pingedLanes,s=r&268435455;if(s!==0){var o=s&~i;o!==0?n=qi(o):(a&=s,a!==0&&(n=qi(a)))}else s=r&~i,s!==0?n=qi(s):a!==0&&(n=qi(a));if(n===0)return 0;if(t!==0&&t!==n&&!(t&i)&&(i=n&-n,a=t&-t,i>=a||i===16&&(a&4194240)!==0))return t;if(n&4&&(n|=r&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=n;0<t;)r=31-It(t),i=1<<r,n|=e[r],t&=~i;return n}function l0(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function c0(e,t){for(var r=e.suspendedLanes,n=e.pingedLanes,i=e.expirationTimes,a=e.pendingLanes;0<a;){var s=31-It(a),o=1<<s,c=i[s];c===-1?(!(o&r)||o&n)&&(i[s]=l0(o,t)):c<=t&&(e.expiredLanes|=o),a&=~o}}function rc(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Mm(){var e=Ya;return Ya<<=1,!(Ya&4194240)&&(Ya=64),e}function al(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function Ca(e,t,r){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-It(t),e[t]=r}function u0(e,t){var r=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var n=e.eventTimes;for(e=e.expirationTimes;0<r;){var i=31-It(r),a=1<<i;t[i]=0,n[i]=-1,e[i]=-1,r&=~a}}function vu(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var n=31-It(r),i=1<<n;i&t|e[n]&t&&(e[n]|=t),r&=~i}}var ue=0;function Fm(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var zm,yu,Im,Um,$m,nc=!1,Qa=[],Fr=null,zr=null,Ir=null,la=new Map,ca=new Map,Pr=[],d0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Id(e,t){switch(e){case"focusin":case"focusout":Fr=null;break;case"dragenter":case"dragleave":zr=null;break;case"mouseover":case"mouseout":Ir=null;break;case"pointerover":case"pointerout":la.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ca.delete(t.pointerId)}}function Ri(e,t,r,n,i,a){return e===null||e.nativeEvent!==a?(e={blockedOn:t,domEventName:r,eventSystemFlags:n,nativeEvent:a,targetContainers:[i]},t!==null&&(t=Ta(t),t!==null&&yu(t)),e):(e.eventSystemFlags|=n,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function f0(e,t,r,n,i){switch(t){case"focusin":return Fr=Ri(Fr,e,t,r,n,i),!0;case"dragenter":return zr=Ri(zr,e,t,r,n,i),!0;case"mouseover":return Ir=Ri(Ir,e,t,r,n,i),!0;case"pointerover":var a=i.pointerId;return la.set(a,Ri(la.get(a)||null,e,t,r,n,i)),!0;case"gotpointercapture":return a=i.pointerId,ca.set(a,Ri(ca.get(a)||null,e,t,r,n,i)),!0}return!1}function Bm(e){var t=dn(e.target);if(t!==null){var r=Nn(t);if(r!==null){if(t=r.tag,t===13){if(t=Pm(r),t!==null){e.blockedOn=t,$m(e.priority,function(){Im(r)});return}}else if(t===3&&r.stateNode.current.memoizedState.isDehydrated){e.blockedOn=r.tag===3?r.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ps(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var r=ic(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(r===null){r=e.nativeEvent;var n=new r.constructor(r.type,r);Jl=n,r.target.dispatchEvent(n),Jl=null}else return t=Ta(r),t!==null&&yu(t),e.blockedOn=r,!1;t.shift()}return!0}function Ud(e,t,r){ps(e)&&r.delete(t)}function p0(){nc=!1,Fr!==null&&ps(Fr)&&(Fr=null),zr!==null&&ps(zr)&&(zr=null),Ir!==null&&ps(Ir)&&(Ir=null),la.forEach(Ud),ca.forEach(Ud)}function Ai(e,t){e.blockedOn===t&&(e.blockedOn=null,nc||(nc=!0,vt.unstable_scheduleCallback(vt.unstable_NormalPriority,p0)))}function ua(e){function t(i){return Ai(i,e)}if(0<Qa.length){Ai(Qa[0],e);for(var r=1;r<Qa.length;r++){var n=Qa[r];n.blockedOn===e&&(n.blockedOn=null)}}for(Fr!==null&&Ai(Fr,e),zr!==null&&Ai(zr,e),Ir!==null&&Ai(Ir,e),la.forEach(t),ca.forEach(t),r=0;r<Pr.length;r++)n=Pr[r],n.blockedOn===e&&(n.blockedOn=null);for(;0<Pr.length&&(r=Pr[0],r.blockedOn===null);)Bm(r),r.blockedOn===null&&Pr.shift()}var Jn=wr.ReactCurrentBatchConfig,zs=!0;function m0(e,t,r,n){var i=ue,a=Jn.transition;Jn.transition=null;try{ue=1,xu(e,t,r,n)}finally{ue=i,Jn.transition=a}}function h0(e,t,r,n){var i=ue,a=Jn.transition;Jn.transition=null;try{ue=4,xu(e,t,r,n)}finally{ue=i,Jn.transition=a}}function xu(e,t,r,n){if(zs){var i=ic(e,t,r,n);if(i===null)hl(e,t,n,Is,r),Id(e,n);else if(f0(i,e,t,r,n))n.stopPropagation();else if(Id(e,n),t&4&&-1<d0.indexOf(e)){for(;i!==null;){var a=Ta(i);if(a!==null&&zm(a),a=ic(e,t,r,n),a===null&&hl(e,t,n,Is,r),a===i)break;i=a}i!==null&&n.stopPropagation()}else hl(e,t,n,null,r)}}var Is=null;function ic(e,t,r,n){if(Is=null,e=hu(n),e=dn(e),e!==null)if(t=Nn(e),t===null)e=null;else if(r=t.tag,r===13){if(e=Pm(t),e!==null)return e;e=null}else if(r===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Is=e,null}function Vm(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(r0()){case gu:return 1;case Dm:return 4;case Ms:case n0:return 16;case Lm:return 536870912;default:return 16}default:return 16}}var Dr=null,wu=null,ms=null;function Wm(){if(ms)return ms;var e,t=wu,r=t.length,n,i="value"in Dr?Dr.value:Dr.textContent,a=i.length;for(e=0;e<r&&t[e]===i[e];e++);var s=r-e;for(n=1;n<=s&&t[r-n]===i[a-n];n++);return ms=i.slice(e,1<n?1-n:void 0)}function hs(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ga(){return!0}function $d(){return!1}function xt(e){function t(r,n,i,a,s){this._reactName=r,this._targetInst=i,this.type=n,this.nativeEvent=a,this.target=s,this.currentTarget=null;for(var o in e)e.hasOwnProperty(o)&&(r=e[o],this[o]=r?r(a):a[o]);return this.isDefaultPrevented=(a.defaultPrevented!=null?a.defaultPrevented:a.returnValue===!1)?Ga:$d,this.isPropagationStopped=$d,this}return Se(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var r=this.nativeEvent;r&&(r.preventDefault?r.preventDefault():typeof r.returnValue!="unknown"&&(r.returnValue=!1),this.isDefaultPrevented=Ga)},stopPropagation:function(){var r=this.nativeEvent;r&&(r.stopPropagation?r.stopPropagation():typeof r.cancelBubble!="unknown"&&(r.cancelBubble=!0),this.isPropagationStopped=Ga)},persist:function(){},isPersistent:Ga}),t}var gi={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},bu=xt(gi),_a=Se({},gi,{view:0,detail:0}),g0=xt(_a),sl,ol,Di,wo=Se({},_a,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ku,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Di&&(Di&&e.type==="mousemove"?(sl=e.screenX-Di.screenX,ol=e.screenY-Di.screenY):ol=sl=0,Di=e),sl)},movementY:function(e){return"movementY"in e?e.movementY:ol}}),Bd=xt(wo),v0=Se({},wo,{dataTransfer:0}),y0=xt(v0),x0=Se({},_a,{relatedTarget:0}),ll=xt(x0),w0=Se({},gi,{animationName:0,elapsedTime:0,pseudoElement:0}),b0=xt(w0),k0=Se({},gi,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),S0=xt(k0),E0=Se({},gi,{data:0}),Vd=xt(E0),j0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},N0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},C0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function _0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=C0[e])?!!t[e]:!1}function ku(){return _0}var T0=Se({},_a,{key:function(e){if(e.key){var t=j0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=hs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?N0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ku,charCode:function(e){return e.type==="keypress"?hs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?hs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),P0=xt(T0),O0=Se({},wo,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Wd=xt(O0),R0=Se({},_a,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ku}),A0=xt(R0),D0=Se({},gi,{propertyName:0,elapsedTime:0,pseudoElement:0}),L0=xt(D0),M0=Se({},wo,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),F0=xt(M0),z0=[9,13,27,32],Su=pr&&"CompositionEvent"in window,Gi=null;pr&&"documentMode"in document&&(Gi=document.documentMode);var I0=pr&&"TextEvent"in window&&!Gi,qm=pr&&(!Su||Gi&&8<Gi&&11>=Gi),qd=String.fromCharCode(32),Hd=!1;function Hm(e,t){switch(e){case"keyup":return z0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ym(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var In=!1;function U0(e,t){switch(e){case"compositionend":return Ym(t);case"keypress":return t.which!==32?null:(Hd=!0,qd);case"textInput":return e=t.data,e===qd&&Hd?null:e;default:return null}}function $0(e,t){if(In)return e==="compositionend"||!Su&&Hm(e,t)?(e=Wm(),ms=wu=Dr=null,In=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return qm&&t.locale!=="ko"?null:t.data;default:return null}}var B0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Yd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!B0[e.type]:t==="textarea"}function Km(e,t,r,n){jm(n),t=Us(t,"onChange"),0<t.length&&(r=new bu("onChange","change",null,r,n),e.push({event:r,listeners:t}))}var Xi=null,da=null;function V0(e){ah(e,0)}function bo(e){var t=Bn(e);if(ym(t))return e}function W0(e,t){if(e==="change")return t}var Qm=!1;if(pr){var cl;if(pr){var ul="oninput"in document;if(!ul){var Kd=document.createElement("div");Kd.setAttribute("oninput","return;"),ul=typeof Kd.oninput=="function"}cl=ul}else cl=!1;Qm=cl&&(!document.documentMode||9<document.documentMode)}function Qd(){Xi&&(Xi.detachEvent("onpropertychange",Gm),da=Xi=null)}function Gm(e){if(e.propertyName==="value"&&bo(da)){var t=[];Km(t,da,e,hu(e)),Tm(V0,t)}}function q0(e,t,r){e==="focusin"?(Qd(),Xi=t,da=r,Xi.attachEvent("onpropertychange",Gm)):e==="focusout"&&Qd()}function H0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return bo(da)}function Y0(e,t){if(e==="click")return bo(t)}function K0(e,t){if(e==="input"||e==="change")return bo(t)}function Q0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Bt=typeof Object.is=="function"?Object.is:Q0;function fa(e,t){if(Bt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(n=0;n<r.length;n++){var i=r[n];if(!Ul.call(t,i)||!Bt(e[i],t[i]))return!1}return!0}function Gd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Xd(e,t){var r=Gd(e);e=0;for(var n;r;){if(r.nodeType===3){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Gd(r)}}function Xm(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Xm(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Jm(){for(var e=window,t=As();t instanceof e.HTMLIFrameElement;){try{var r=typeof t.contentWindow.location.href=="string"}catch{r=!1}if(r)e=t.contentWindow;else break;t=As(e.document)}return t}function Eu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function G0(e){var t=Jm(),r=e.focusedElem,n=e.selectionRange;if(t!==r&&r&&r.ownerDocument&&Xm(r.ownerDocument.documentElement,r)){if(n!==null&&Eu(r)){if(t=n.start,e=n.end,e===void 0&&(e=t),"selectionStart"in r)r.selectionStart=t,r.selectionEnd=Math.min(e,r.value.length);else if(e=(t=r.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=r.textContent.length,a=Math.min(n.start,i);n=n.end===void 0?a:Math.min(n.end,i),!e.extend&&a>n&&(i=n,n=a,a=i),i=Xd(r,a);var s=Xd(r,n);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),a>n?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=r;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<t.length;r++)e=t[r],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var X0=pr&&"documentMode"in document&&11>=document.documentMode,Un=null,ac=null,Ji=null,sc=!1;function Jd(e,t,r){var n=r.window===r?r.document:r.nodeType===9?r:r.ownerDocument;sc||Un==null||Un!==As(n)||(n=Un,"selectionStart"in n&&Eu(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),Ji&&fa(Ji,n)||(Ji=n,n=Us(ac,"onSelect"),0<n.length&&(t=new bu("onSelect","select",null,t,r),e.push({event:t,listeners:n}),t.target=Un)))}function Xa(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var $n={animationend:Xa("Animation","AnimationEnd"),animationiteration:Xa("Animation","AnimationIteration"),animationstart:Xa("Animation","AnimationStart"),transitionend:Xa("Transition","TransitionEnd")},dl={},Zm={};pr&&(Zm=document.createElement("div").style,"AnimationEvent"in window||(delete $n.animationend.animation,delete $n.animationiteration.animation,delete $n.animationstart.animation),"TransitionEvent"in window||delete $n.transitionend.transition);function ko(e){if(dl[e])return dl[e];if(!$n[e])return e;var t=$n[e],r;for(r in t)if(t.hasOwnProperty(r)&&r in Zm)return dl[e]=t[r];return e}var eh=ko("animationend"),th=ko("animationiteration"),rh=ko("animationstart"),nh=ko("transitionend"),ih=new Map,Zd="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Qr(e,t){ih.set(e,t),jn(t,[e])}for(var fl=0;fl<Zd.length;fl++){var pl=Zd[fl],J0=pl.toLowerCase(),Z0=pl[0].toUpperCase()+pl.slice(1);Qr(J0,"on"+Z0)}Qr(eh,"onAnimationEnd");Qr(th,"onAnimationIteration");Qr(rh,"onAnimationStart");Qr("dblclick","onDoubleClick");Qr("focusin","onFocus");Qr("focusout","onBlur");Qr(nh,"onTransitionEnd");ni("onMouseEnter",["mouseout","mouseover"]);ni("onMouseLeave",["mouseout","mouseover"]);ni("onPointerEnter",["pointerout","pointerover"]);ni("onPointerLeave",["pointerout","pointerover"]);jn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));jn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));jn("onBeforeInput",["compositionend","keypress","textInput","paste"]);jn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));jn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));jn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Hi="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),ex=new Set("cancel close invalid load scroll toggle".split(" ").concat(Hi));function ef(e,t,r){var n=e.type||"unknown-event";e.currentTarget=r,Jy(n,t,void 0,e),e.currentTarget=null}function ah(e,t){t=(t&4)!==0;for(var r=0;r<e.length;r++){var n=e[r],i=n.event;n=n.listeners;e:{var a=void 0;if(t)for(var s=n.length-1;0<=s;s--){var o=n[s],c=o.instance,u=o.currentTarget;if(o=o.listener,c!==a&&i.isPropagationStopped())break e;ef(i,o,u),a=c}else for(s=0;s<n.length;s++){if(o=n[s],c=o.instance,u=o.currentTarget,o=o.listener,c!==a&&i.isPropagationStopped())break e;ef(i,o,u),a=c}}}if(Ls)throw e=tc,Ls=!1,tc=null,e}function ge(e,t){var r=t[dc];r===void 0&&(r=t[dc]=new Set);var n=e+"__bubble";r.has(n)||(sh(t,e,2,!1),r.add(n))}function ml(e,t,r){var n=0;t&&(n|=4),sh(r,e,n,t)}var Ja="_reactListening"+Math.random().toString(36).slice(2);function pa(e){if(!e[Ja]){e[Ja]=!0,pm.forEach(function(r){r!=="selectionchange"&&(ex.has(r)||ml(r,!1,e),ml(r,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ja]||(t[Ja]=!0,ml("selectionchange",!1,t))}}function sh(e,t,r,n){switch(Vm(t)){case 1:var i=m0;break;case 4:i=h0;break;default:i=xu}r=i.bind(null,t,r,e),i=void 0,!ec||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),n?i!==void 0?e.addEventListener(t,r,{capture:!0,passive:i}):e.addEventListener(t,r,!0):i!==void 0?e.addEventListener(t,r,{passive:i}):e.addEventListener(t,r,!1)}function hl(e,t,r,n,i){var a=n;if(!(t&1)&&!(t&2)&&n!==null)e:for(;;){if(n===null)return;var s=n.tag;if(s===3||s===4){var o=n.stateNode.containerInfo;if(o===i||o.nodeType===8&&o.parentNode===i)break;if(s===4)for(s=n.return;s!==null;){var c=s.tag;if((c===3||c===4)&&(c=s.stateNode.containerInfo,c===i||c.nodeType===8&&c.parentNode===i))return;s=s.return}for(;o!==null;){if(s=dn(o),s===null)return;if(c=s.tag,c===5||c===6){n=a=s;continue e}o=o.parentNode}}n=n.return}Tm(function(){var u=a,d=hu(r),f=[];e:{var h=ih.get(e);if(h!==void 0){var w=bu,g=e;switch(e){case"keypress":if(hs(r)===0)break e;case"keydown":case"keyup":w=P0;break;case"focusin":g="focus",w=ll;break;case"focusout":g="blur",w=ll;break;case"beforeblur":case"afterblur":w=ll;break;case"click":if(r.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=Bd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=y0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=A0;break;case eh:case th:case rh:w=b0;break;case nh:w=L0;break;case"scroll":w=g0;break;case"wheel":w=F0;break;case"copy":case"cut":case"paste":w=S0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=Wd}var y=(t&4)!==0,x=!y&&e==="scroll",m=y?h!==null?h+"Capture":null:h;y=[];for(var p=u,v;p!==null;){v=p;var k=v.stateNode;if(v.tag===5&&k!==null&&(v=k,m!==null&&(k=oa(p,m),k!=null&&y.push(ma(p,k,v)))),x)break;p=p.return}0<y.length&&(h=new w(h,g,null,r,d),f.push({event:h,listeners:y}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",w=e==="mouseout"||e==="pointerout",h&&r!==Jl&&(g=r.relatedTarget||r.fromElement)&&(dn(g)||g[mr]))break e;if((w||h)&&(h=d.window===d?d:(h=d.ownerDocument)?h.defaultView||h.parentWindow:window,w?(g=r.relatedTarget||r.toElement,w=u,g=g?dn(g):null,g!==null&&(x=Nn(g),g!==x||g.tag!==5&&g.tag!==6)&&(g=null)):(w=null,g=u),w!==g)){if(y=Bd,k="onMouseLeave",m="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(y=Wd,k="onPointerLeave",m="onPointerEnter",p="pointer"),x=w==null?h:Bn(w),v=g==null?h:Bn(g),h=new y(k,p+"leave",w,r,d),h.target=x,h.relatedTarget=v,k=null,dn(d)===u&&(y=new y(m,p+"enter",g,r,d),y.target=v,y.relatedTarget=x,k=y),x=k,w&&g)t:{for(y=w,m=g,p=0,v=y;v;v=Rn(v))p++;for(v=0,k=m;k;k=Rn(k))v++;for(;0<p-v;)y=Rn(y),p--;for(;0<v-p;)m=Rn(m),v--;for(;p--;){if(y===m||m!==null&&y===m.alternate)break t;y=Rn(y),m=Rn(m)}y=null}else y=null;w!==null&&tf(f,h,w,y,!1),g!==null&&x!==null&&tf(f,x,g,y,!0)}}e:{if(h=u?Bn(u):window,w=h.nodeName&&h.nodeName.toLowerCase(),w==="select"||w==="input"&&h.type==="file")var _=W0;else if(Yd(h))if(Qm)_=K0;else{_=H0;var A=q0}else(w=h.nodeName)&&w.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(_=Y0);if(_&&(_=_(e,u))){Km(f,_,r,d);break e}A&&A(e,h,u),e==="focusout"&&(A=h._wrapperState)&&A.controlled&&h.type==="number"&&Yl(h,"number",h.value)}switch(A=u?Bn(u):window,e){case"focusin":(Yd(A)||A.contentEditable==="true")&&(Un=A,ac=u,Ji=null);break;case"focusout":Ji=ac=Un=null;break;case"mousedown":sc=!0;break;case"contextmenu":case"mouseup":case"dragend":sc=!1,Jd(f,r,d);break;case"selectionchange":if(X0)break;case"keydown":case"keyup":Jd(f,r,d)}var P;if(Su)e:{switch(e){case"compositionstart":var O="onCompositionStart";break e;case"compositionend":O="onCompositionEnd";break e;case"compositionupdate":O="onCompositionUpdate";break e}O=void 0}else In?Hm(e,r)&&(O="onCompositionEnd"):e==="keydown"&&r.keyCode===229&&(O="onCompositionStart");O&&(qm&&r.locale!=="ko"&&(In||O!=="onCompositionStart"?O==="onCompositionEnd"&&In&&(P=Wm()):(Dr=d,wu="value"in Dr?Dr.value:Dr.textContent,In=!0)),A=Us(u,O),0<A.length&&(O=new Vd(O,e,null,r,d),f.push({event:O,listeners:A}),P?O.data=P:(P=Ym(r),P!==null&&(O.data=P)))),(P=I0?U0(e,r):$0(e,r))&&(u=Us(u,"onBeforeInput"),0<u.length&&(d=new Vd("onBeforeInput","beforeinput",null,r,d),f.push({event:d,listeners:u}),d.data=P))}ah(f,t)})}function ma(e,t,r){return{instance:e,listener:t,currentTarget:r}}function Us(e,t){for(var r=t+"Capture",n=[];e!==null;){var i=e,a=i.stateNode;i.tag===5&&a!==null&&(i=a,a=oa(e,r),a!=null&&n.unshift(ma(e,a,i)),a=oa(e,t),a!=null&&n.push(ma(e,a,i))),e=e.return}return n}function Rn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function tf(e,t,r,n,i){for(var a=t._reactName,s=[];r!==null&&r!==n;){var o=r,c=o.alternate,u=o.stateNode;if(c!==null&&c===n)break;o.tag===5&&u!==null&&(o=u,i?(c=oa(r,a),c!=null&&s.unshift(ma(r,c,o))):i||(c=oa(r,a),c!=null&&s.push(ma(r,c,o)))),r=r.return}s.length!==0&&e.push({event:t,listeners:s})}var tx=/\r\n?/g,rx=/\u0000|\uFFFD/g;function rf(e){return(typeof e=="string"?e:""+e).replace(tx,`
`).replace(rx,"")}function Za(e,t,r){if(t=rf(t),rf(e)!==t&&r)throw Error(R(425))}function $s(){}var oc=null,lc=null;function cc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var uc=typeof setTimeout=="function"?setTimeout:void 0,nx=typeof clearTimeout=="function"?clearTimeout:void 0,nf=typeof Promise=="function"?Promise:void 0,ix=typeof queueMicrotask=="function"?queueMicrotask:typeof nf<"u"?function(e){return nf.resolve(null).then(e).catch(ax)}:uc;function ax(e){setTimeout(function(){throw e})}function gl(e,t){var r=t,n=0;do{var i=r.nextSibling;if(e.removeChild(r),i&&i.nodeType===8)if(r=i.data,r==="/$"){if(n===0){e.removeChild(i),ua(t);return}n--}else r!=="$"&&r!=="$?"&&r!=="$!"||n++;r=i}while(r);ua(t)}function Ur(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function af(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="$"||r==="$!"||r==="$?"){if(t===0)return e;t--}else r==="/$"&&t++}e=e.previousSibling}return null}var vi=Math.random().toString(36).slice(2),Qt="__reactFiber$"+vi,ha="__reactProps$"+vi,mr="__reactContainer$"+vi,dc="__reactEvents$"+vi,sx="__reactListeners$"+vi,ox="__reactHandles$"+vi;function dn(e){var t=e[Qt];if(t)return t;for(var r=e.parentNode;r;){if(t=r[mr]||r[Qt]){if(r=t.alternate,t.child!==null||r!==null&&r.child!==null)for(e=af(e);e!==null;){if(r=e[Qt])return r;e=af(e)}return t}e=r,r=e.parentNode}return null}function Ta(e){return e=e[Qt]||e[mr],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Bn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(R(33))}function So(e){return e[ha]||null}var fc=[],Vn=-1;function Gr(e){return{current:e}}function ve(e){0>Vn||(e.current=fc[Vn],fc[Vn]=null,Vn--)}function me(e,t){Vn++,fc[Vn]=e.current,e.current=t}var Kr={},Je=Gr(Kr),ct=Gr(!1),yn=Kr;function ii(e,t){var r=e.type.contextTypes;if(!r)return Kr;var n=e.stateNode;if(n&&n.__reactInternalMemoizedUnmaskedChildContext===t)return n.__reactInternalMemoizedMaskedChildContext;var i={},a;for(a in r)i[a]=t[a];return n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function ut(e){return e=e.childContextTypes,e!=null}function Bs(){ve(ct),ve(Je)}function sf(e,t,r){if(Je.current!==Kr)throw Error(R(168));me(Je,t),me(ct,r)}function oh(e,t,r){var n=e.stateNode;if(t=t.childContextTypes,typeof n.getChildContext!="function")return r;n=n.getChildContext();for(var i in n)if(!(i in t))throw Error(R(108,qy(e)||"Unknown",i));return Se({},r,n)}function Vs(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Kr,yn=Je.current,me(Je,e),me(ct,ct.current),!0}function of(e,t,r){var n=e.stateNode;if(!n)throw Error(R(169));r?(e=oh(e,t,yn),n.__reactInternalMemoizedMergedChildContext=e,ve(ct),ve(Je),me(Je,e)):ve(ct),me(ct,r)}var cr=null,Eo=!1,vl=!1;function lh(e){cr===null?cr=[e]:cr.push(e)}function lx(e){Eo=!0,lh(e)}function Xr(){if(!vl&&cr!==null){vl=!0;var e=0,t=ue;try{var r=cr;for(ue=1;e<r.length;e++){var n=r[e];do n=n(!0);while(n!==null)}cr=null,Eo=!1}catch(i){throw cr!==null&&(cr=cr.slice(e+1)),Am(gu,Xr),i}finally{ue=t,vl=!1}}return null}var Wn=[],qn=0,Ws=null,qs=0,kt=[],St=0,xn=null,ur=1,dr="";function sn(e,t){Wn[qn++]=qs,Wn[qn++]=Ws,Ws=e,qs=t}function ch(e,t,r){kt[St++]=ur,kt[St++]=dr,kt[St++]=xn,xn=e;var n=ur;e=dr;var i=32-It(n)-1;n&=~(1<<i),r+=1;var a=32-It(t)+i;if(30<a){var s=i-i%5;a=(n&(1<<s)-1).toString(32),n>>=s,i-=s,ur=1<<32-It(t)+i|r<<i|n,dr=a+e}else ur=1<<a|r<<i|n,dr=e}function ju(e){e.return!==null&&(sn(e,1),ch(e,1,0))}function Nu(e){for(;e===Ws;)Ws=Wn[--qn],Wn[qn]=null,qs=Wn[--qn],Wn[qn]=null;for(;e===xn;)xn=kt[--St],kt[St]=null,dr=kt[--St],kt[St]=null,ur=kt[--St],kt[St]=null}var gt=null,ht=null,xe=!1,Lt=null;function uh(e,t){var r=jt(5,null,null,0);r.elementType="DELETED",r.stateNode=t,r.return=e,t=e.deletions,t===null?(e.deletions=[r],e.flags|=16):t.push(r)}function lf(e,t){switch(e.tag){case 5:var r=e.type;return t=t.nodeType!==1||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,gt=e,ht=Ur(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,gt=e,ht=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(r=xn!==null?{id:ur,overflow:dr}:null,e.memoizedState={dehydrated:t,treeContext:r,retryLane:1073741824},r=jt(18,null,null,0),r.stateNode=t,r.return=e,e.child=r,gt=e,ht=null,!0):!1;default:return!1}}function pc(e){return(e.mode&1)!==0&&(e.flags&128)===0}function mc(e){if(xe){var t=ht;if(t){var r=t;if(!lf(e,t)){if(pc(e))throw Error(R(418));t=Ur(r.nextSibling);var n=gt;t&&lf(e,t)?uh(n,r):(e.flags=e.flags&-4097|2,xe=!1,gt=e)}}else{if(pc(e))throw Error(R(418));e.flags=e.flags&-4097|2,xe=!1,gt=e}}}function cf(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;gt=e}function es(e){if(e!==gt)return!1;if(!xe)return cf(e),xe=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!cc(e.type,e.memoizedProps)),t&&(t=ht)){if(pc(e))throw dh(),Error(R(418));for(;t;)uh(e,t),t=Ur(t.nextSibling)}if(cf(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(R(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="/$"){if(t===0){ht=Ur(e.nextSibling);break e}t--}else r!=="$"&&r!=="$!"&&r!=="$?"||t++}e=e.nextSibling}ht=null}}else ht=gt?Ur(e.stateNode.nextSibling):null;return!0}function dh(){for(var e=ht;e;)e=Ur(e.nextSibling)}function ai(){ht=gt=null,xe=!1}function Cu(e){Lt===null?Lt=[e]:Lt.push(e)}var cx=wr.ReactCurrentBatchConfig;function Li(e,t,r){if(e=r.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(r._owner){if(r=r._owner,r){if(r.tag!==1)throw Error(R(309));var n=r.stateNode}if(!n)throw Error(R(147,e));var i=n,a=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===a?t.ref:(t=function(s){var o=i.refs;s===null?delete o[a]:o[a]=s},t._stringRef=a,t)}if(typeof e!="string")throw Error(R(284));if(!r._owner)throw Error(R(290,e))}return e}function ts(e,t){throw e=Object.prototype.toString.call(t),Error(R(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function uf(e){var t=e._init;return t(e._payload)}function fh(e){function t(m,p){if(e){var v=m.deletions;v===null?(m.deletions=[p],m.flags|=16):v.push(p)}}function r(m,p){if(!e)return null;for(;p!==null;)t(m,p),p=p.sibling;return null}function n(m,p){for(m=new Map;p!==null;)p.key!==null?m.set(p.key,p):m.set(p.index,p),p=p.sibling;return m}function i(m,p){return m=Wr(m,p),m.index=0,m.sibling=null,m}function a(m,p,v){return m.index=v,e?(v=m.alternate,v!==null?(v=v.index,v<p?(m.flags|=2,p):v):(m.flags|=2,p)):(m.flags|=1048576,p)}function s(m){return e&&m.alternate===null&&(m.flags|=2),m}function o(m,p,v,k){return p===null||p.tag!==6?(p=El(v,m.mode,k),p.return=m,p):(p=i(p,v),p.return=m,p)}function c(m,p,v,k){var _=v.type;return _===zn?d(m,p,v.props.children,k,v.key):p!==null&&(p.elementType===_||typeof _=="object"&&_!==null&&_.$$typeof===_r&&uf(_)===p.type)?(k=i(p,v.props),k.ref=Li(m,p,v),k.return=m,k):(k=ks(v.type,v.key,v.props,null,m.mode,k),k.ref=Li(m,p,v),k.return=m,k)}function u(m,p,v,k){return p===null||p.tag!==4||p.stateNode.containerInfo!==v.containerInfo||p.stateNode.implementation!==v.implementation?(p=jl(v,m.mode,k),p.return=m,p):(p=i(p,v.children||[]),p.return=m,p)}function d(m,p,v,k,_){return p===null||p.tag!==7?(p=vn(v,m.mode,k,_),p.return=m,p):(p=i(p,v),p.return=m,p)}function f(m,p,v){if(typeof p=="string"&&p!==""||typeof p=="number")return p=El(""+p,m.mode,v),p.return=m,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Wa:return v=ks(p.type,p.key,p.props,null,m.mode,v),v.ref=Li(m,null,p),v.return=m,v;case Fn:return p=jl(p,m.mode,v),p.return=m,p;case _r:var k=p._init;return f(m,k(p._payload),v)}if(Wi(p)||Pi(p))return p=vn(p,m.mode,v,null),p.return=m,p;ts(m,p)}return null}function h(m,p,v,k){var _=p!==null?p.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return _!==null?null:o(m,p,""+v,k);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case Wa:return v.key===_?c(m,p,v,k):null;case Fn:return v.key===_?u(m,p,v,k):null;case _r:return _=v._init,h(m,p,_(v._payload),k)}if(Wi(v)||Pi(v))return _!==null?null:d(m,p,v,k,null);ts(m,v)}return null}function w(m,p,v,k,_){if(typeof k=="string"&&k!==""||typeof k=="number")return m=m.get(v)||null,o(p,m,""+k,_);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case Wa:return m=m.get(k.key===null?v:k.key)||null,c(p,m,k,_);case Fn:return m=m.get(k.key===null?v:k.key)||null,u(p,m,k,_);case _r:var A=k._init;return w(m,p,v,A(k._payload),_)}if(Wi(k)||Pi(k))return m=m.get(v)||null,d(p,m,k,_,null);ts(p,k)}return null}function g(m,p,v,k){for(var _=null,A=null,P=p,O=p=0,L=null;P!==null&&O<v.length;O++){P.index>O?(L=P,P=null):L=P.sibling;var U=h(m,P,v[O],k);if(U===null){P===null&&(P=L);break}e&&P&&U.alternate===null&&t(m,P),p=a(U,p,O),A===null?_=U:A.sibling=U,A=U,P=L}if(O===v.length)return r(m,P),xe&&sn(m,O),_;if(P===null){for(;O<v.length;O++)P=f(m,v[O],k),P!==null&&(p=a(P,p,O),A===null?_=P:A.sibling=P,A=P);return xe&&sn(m,O),_}for(P=n(m,P);O<v.length;O++)L=w(P,m,O,v[O],k),L!==null&&(e&&L.alternate!==null&&P.delete(L.key===null?O:L.key),p=a(L,p,O),A===null?_=L:A.sibling=L,A=L);return e&&P.forEach(function(Z){return t(m,Z)}),xe&&sn(m,O),_}function y(m,p,v,k){var _=Pi(v);if(typeof _!="function")throw Error(R(150));if(v=_.call(v),v==null)throw Error(R(151));for(var A=_=null,P=p,O=p=0,L=null,U=v.next();P!==null&&!U.done;O++,U=v.next()){P.index>O?(L=P,P=null):L=P.sibling;var Z=h(m,P,U.value,k);if(Z===null){P===null&&(P=L);break}e&&P&&Z.alternate===null&&t(m,P),p=a(Z,p,O),A===null?_=Z:A.sibling=Z,A=Z,P=L}if(U.done)return r(m,P),xe&&sn(m,O),_;if(P===null){for(;!U.done;O++,U=v.next())U=f(m,U.value,k),U!==null&&(p=a(U,p,O),A===null?_=U:A.sibling=U,A=U);return xe&&sn(m,O),_}for(P=n(m,P);!U.done;O++,U=v.next())U=w(P,m,O,U.value,k),U!==null&&(e&&U.alternate!==null&&P.delete(U.key===null?O:U.key),p=a(U,p,O),A===null?_=U:A.sibling=U,A=U);return e&&P.forEach(function(M){return t(m,M)}),xe&&sn(m,O),_}function x(m,p,v,k){if(typeof v=="object"&&v!==null&&v.type===zn&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case Wa:e:{for(var _=v.key,A=p;A!==null;){if(A.key===_){if(_=v.type,_===zn){if(A.tag===7){r(m,A.sibling),p=i(A,v.props.children),p.return=m,m=p;break e}}else if(A.elementType===_||typeof _=="object"&&_!==null&&_.$$typeof===_r&&uf(_)===A.type){r(m,A.sibling),p=i(A,v.props),p.ref=Li(m,A,v),p.return=m,m=p;break e}r(m,A);break}else t(m,A);A=A.sibling}v.type===zn?(p=vn(v.props.children,m.mode,k,v.key),p.return=m,m=p):(k=ks(v.type,v.key,v.props,null,m.mode,k),k.ref=Li(m,p,v),k.return=m,m=k)}return s(m);case Fn:e:{for(A=v.key;p!==null;){if(p.key===A)if(p.tag===4&&p.stateNode.containerInfo===v.containerInfo&&p.stateNode.implementation===v.implementation){r(m,p.sibling),p=i(p,v.children||[]),p.return=m,m=p;break e}else{r(m,p);break}else t(m,p);p=p.sibling}p=jl(v,m.mode,k),p.return=m,m=p}return s(m);case _r:return A=v._init,x(m,p,A(v._payload),k)}if(Wi(v))return g(m,p,v,k);if(Pi(v))return y(m,p,v,k);ts(m,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,p!==null&&p.tag===6?(r(m,p.sibling),p=i(p,v),p.return=m,m=p):(r(m,p),p=El(v,m.mode,k),p.return=m,m=p),s(m)):r(m,p)}return x}var si=fh(!0),ph=fh(!1),Hs=Gr(null),Ys=null,Hn=null,_u=null;function Tu(){_u=Hn=Ys=null}function Pu(e){var t=Hs.current;ve(Hs),e._currentValue=t}function hc(e,t,r){for(;e!==null;){var n=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,n!==null&&(n.childLanes|=t)):n!==null&&(n.childLanes&t)!==t&&(n.childLanes|=t),e===r)break;e=e.return}}function Zn(e,t){Ys=e,_u=Hn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(lt=!0),e.firstContext=null)}function Ct(e){var t=e._currentValue;if(_u!==e)if(e={context:e,memoizedValue:t,next:null},Hn===null){if(Ys===null)throw Error(R(308));Hn=e,Ys.dependencies={lanes:0,firstContext:e}}else Hn=Hn.next=e;return t}var fn=null;function Ou(e){fn===null?fn=[e]:fn.push(e)}function mh(e,t,r,n){var i=t.interleaved;return i===null?(r.next=r,Ou(t)):(r.next=i.next,i.next=r),t.interleaved=r,hr(e,n)}function hr(e,t){e.lanes|=t;var r=e.alternate;for(r!==null&&(r.lanes|=t),r=e,e=e.return;e!==null;)e.childLanes|=t,r=e.alternate,r!==null&&(r.childLanes|=t),r=e,e=e.return;return r.tag===3?r.stateNode:null}var Tr=!1;function Ru(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function hh(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function fr(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function $r(e,t,r){var n=e.updateQueue;if(n===null)return null;if(n=n.shared,ie&2){var i=n.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),n.pending=t,hr(e,r)}return i=n.interleaved,i===null?(t.next=t,Ou(n)):(t.next=i.next,i.next=t),n.interleaved=t,hr(e,r)}function gs(e,t,r){if(t=t.updateQueue,t!==null&&(t=t.shared,(r&4194240)!==0)){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,vu(e,r)}}function df(e,t){var r=e.updateQueue,n=e.alternate;if(n!==null&&(n=n.updateQueue,r===n)){var i=null,a=null;if(r=r.firstBaseUpdate,r!==null){do{var s={eventTime:r.eventTime,lane:r.lane,tag:r.tag,payload:r.payload,callback:r.callback,next:null};a===null?i=a=s:a=a.next=s,r=r.next}while(r!==null);a===null?i=a=t:a=a.next=t}else i=a=t;r={baseState:n.baseState,firstBaseUpdate:i,lastBaseUpdate:a,shared:n.shared,effects:n.effects},e.updateQueue=r;return}e=r.lastBaseUpdate,e===null?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function Ks(e,t,r,n){var i=e.updateQueue;Tr=!1;var a=i.firstBaseUpdate,s=i.lastBaseUpdate,o=i.shared.pending;if(o!==null){i.shared.pending=null;var c=o,u=c.next;c.next=null,s===null?a=u:s.next=u,s=c;var d=e.alternate;d!==null&&(d=d.updateQueue,o=d.lastBaseUpdate,o!==s&&(o===null?d.firstBaseUpdate=u:o.next=u,d.lastBaseUpdate=c))}if(a!==null){var f=i.baseState;s=0,d=u=c=null,o=a;do{var h=o.lane,w=o.eventTime;if((n&h)===h){d!==null&&(d=d.next={eventTime:w,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var g=e,y=o;switch(h=t,w=r,y.tag){case 1:if(g=y.payload,typeof g=="function"){f=g.call(w,f,h);break e}f=g;break e;case 3:g.flags=g.flags&-65537|128;case 0:if(g=y.payload,h=typeof g=="function"?g.call(w,f,h):g,h==null)break e;f=Se({},f,h);break e;case 2:Tr=!0}}o.callback!==null&&o.lane!==0&&(e.flags|=64,h=i.effects,h===null?i.effects=[o]:h.push(o))}else w={eventTime:w,lane:h,tag:o.tag,payload:o.payload,callback:o.callback,next:null},d===null?(u=d=w,c=f):d=d.next=w,s|=h;if(o=o.next,o===null){if(o=i.shared.pending,o===null)break;h=o,o=h.next,h.next=null,i.lastBaseUpdate=h,i.shared.pending=null}}while(1);if(d===null&&(c=f),i.baseState=c,i.firstBaseUpdate=u,i.lastBaseUpdate=d,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else a===null&&(i.shared.lanes=0);bn|=s,e.lanes=s,e.memoizedState=f}}function ff(e,t,r){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var n=e[t],i=n.callback;if(i!==null){if(n.callback=null,n=r,typeof i!="function")throw Error(R(191,i));i.call(n)}}}var Pa={},er=Gr(Pa),ga=Gr(Pa),va=Gr(Pa);function pn(e){if(e===Pa)throw Error(R(174));return e}function Au(e,t){switch(me(va,t),me(ga,e),me(er,Pa),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Ql(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Ql(t,e)}ve(er),me(er,t)}function oi(){ve(er),ve(ga),ve(va)}function gh(e){pn(va.current);var t=pn(er.current),r=Ql(t,e.type);t!==r&&(me(ga,e),me(er,r))}function Du(e){ga.current===e&&(ve(er),ve(ga))}var be=Gr(0);function Qs(e){for(var t=e;t!==null;){if(t.tag===13){var r=t.memoizedState;if(r!==null&&(r=r.dehydrated,r===null||r.data==="$?"||r.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var yl=[];function Lu(){for(var e=0;e<yl.length;e++)yl[e]._workInProgressVersionPrimary=null;yl.length=0}var vs=wr.ReactCurrentDispatcher,xl=wr.ReactCurrentBatchConfig,wn=0,ke=null,De=null,Ie=null,Gs=!1,Zi=!1,ya=0,ux=0;function Ke(){throw Error(R(321))}function Mu(e,t){if(t===null)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!Bt(e[r],t[r]))return!1;return!0}function Fu(e,t,r,n,i,a){if(wn=a,ke=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,vs.current=e===null||e.memoizedState===null?mx:hx,e=r(n,i),Zi){a=0;do{if(Zi=!1,ya=0,25<=a)throw Error(R(301));a+=1,Ie=De=null,t.updateQueue=null,vs.current=gx,e=r(n,i)}while(Zi)}if(vs.current=Xs,t=De!==null&&De.next!==null,wn=0,Ie=De=ke=null,Gs=!1,t)throw Error(R(300));return e}function zu(){var e=ya!==0;return ya=0,e}function Yt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ie===null?ke.memoizedState=Ie=e:Ie=Ie.next=e,Ie}function _t(){if(De===null){var e=ke.alternate;e=e!==null?e.memoizedState:null}else e=De.next;var t=Ie===null?ke.memoizedState:Ie.next;if(t!==null)Ie=t,De=e;else{if(e===null)throw Error(R(310));De=e,e={memoizedState:De.memoizedState,baseState:De.baseState,baseQueue:De.baseQueue,queue:De.queue,next:null},Ie===null?ke.memoizedState=Ie=e:Ie=Ie.next=e}return Ie}function xa(e,t){return typeof t=="function"?t(e):t}function wl(e){var t=_t(),r=t.queue;if(r===null)throw Error(R(311));r.lastRenderedReducer=e;var n=De,i=n.baseQueue,a=r.pending;if(a!==null){if(i!==null){var s=i.next;i.next=a.next,a.next=s}n.baseQueue=i=a,r.pending=null}if(i!==null){a=i.next,n=n.baseState;var o=s=null,c=null,u=a;do{var d=u.lane;if((wn&d)===d)c!==null&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),n=u.hasEagerState?u.eagerState:e(n,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};c===null?(o=c=f,s=n):c=c.next=f,ke.lanes|=d,bn|=d}u=u.next}while(u!==null&&u!==a);c===null?s=n:c.next=o,Bt(n,t.memoizedState)||(lt=!0),t.memoizedState=n,t.baseState=s,t.baseQueue=c,r.lastRenderedState=n}if(e=r.interleaved,e!==null){i=e;do a=i.lane,ke.lanes|=a,bn|=a,i=i.next;while(i!==e)}else i===null&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function bl(e){var t=_t(),r=t.queue;if(r===null)throw Error(R(311));r.lastRenderedReducer=e;var n=r.dispatch,i=r.pending,a=t.memoizedState;if(i!==null){r.pending=null;var s=i=i.next;do a=e(a,s.action),s=s.next;while(s!==i);Bt(a,t.memoizedState)||(lt=!0),t.memoizedState=a,t.baseQueue===null&&(t.baseState=a),r.lastRenderedState=a}return[a,n]}function vh(){}function yh(e,t){var r=ke,n=_t(),i=t(),a=!Bt(n.memoizedState,i);if(a&&(n.memoizedState=i,lt=!0),n=n.queue,Iu(bh.bind(null,r,n,e),[e]),n.getSnapshot!==t||a||Ie!==null&&Ie.memoizedState.tag&1){if(r.flags|=2048,wa(9,wh.bind(null,r,n,i,t),void 0,null),Ue===null)throw Error(R(349));wn&30||xh(r,t,i)}return i}function xh(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},t=ke.updateQueue,t===null?(t={lastEffect:null,stores:null},ke.updateQueue=t,t.stores=[e]):(r=t.stores,r===null?t.stores=[e]:r.push(e))}function wh(e,t,r,n){t.value=r,t.getSnapshot=n,kh(t)&&Sh(e)}function bh(e,t,r){return r(function(){kh(t)&&Sh(e)})}function kh(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!Bt(e,r)}catch{return!0}}function Sh(e){var t=hr(e,1);t!==null&&Ut(t,e,1,-1)}function pf(e){var t=Yt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xa,lastRenderedState:e},t.queue=e,e=e.dispatch=px.bind(null,ke,e),[t.memoizedState,e]}function wa(e,t,r,n){return e={tag:e,create:t,destroy:r,deps:n,next:null},t=ke.updateQueue,t===null?(t={lastEffect:null,stores:null},ke.updateQueue=t,t.lastEffect=e.next=e):(r=t.lastEffect,r===null?t.lastEffect=e.next=e:(n=r.next,r.next=e,e.next=n,t.lastEffect=e)),e}function Eh(){return _t().memoizedState}function ys(e,t,r,n){var i=Yt();ke.flags|=e,i.memoizedState=wa(1|t,r,void 0,n===void 0?null:n)}function jo(e,t,r,n){var i=_t();n=n===void 0?null:n;var a=void 0;if(De!==null){var s=De.memoizedState;if(a=s.destroy,n!==null&&Mu(n,s.deps)){i.memoizedState=wa(t,r,a,n);return}}ke.flags|=e,i.memoizedState=wa(1|t,r,a,n)}function mf(e,t){return ys(8390656,8,e,t)}function Iu(e,t){return jo(2048,8,e,t)}function jh(e,t){return jo(4,2,e,t)}function Nh(e,t){return jo(4,4,e,t)}function Ch(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function _h(e,t,r){return r=r!=null?r.concat([e]):null,jo(4,4,Ch.bind(null,t,e),r)}function Uu(){}function Th(e,t){var r=_t();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&Mu(t,n[1])?n[0]:(r.memoizedState=[e,t],e)}function Ph(e,t){var r=_t();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&Mu(t,n[1])?n[0]:(e=e(),r.memoizedState=[e,t],e)}function Oh(e,t,r){return wn&21?(Bt(r,t)||(r=Mm(),ke.lanes|=r,bn|=r,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,lt=!0),e.memoizedState=r)}function dx(e,t){var r=ue;ue=r!==0&&4>r?r:4,e(!0);var n=xl.transition;xl.transition={};try{e(!1),t()}finally{ue=r,xl.transition=n}}function Rh(){return _t().memoizedState}function fx(e,t,r){var n=Vr(e);if(r={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null},Ah(e))Dh(t,r);else if(r=mh(e,t,r,n),r!==null){var i=rt();Ut(r,e,n,i),Lh(r,t,n)}}function px(e,t,r){var n=Vr(e),i={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null};if(Ah(e))Dh(t,i);else{var a=e.alternate;if(e.lanes===0&&(a===null||a.lanes===0)&&(a=t.lastRenderedReducer,a!==null))try{var s=t.lastRenderedState,o=a(s,r);if(i.hasEagerState=!0,i.eagerState=o,Bt(o,s)){var c=t.interleaved;c===null?(i.next=i,Ou(t)):(i.next=c.next,c.next=i),t.interleaved=i;return}}catch{}finally{}r=mh(e,t,i,n),r!==null&&(i=rt(),Ut(r,e,n,i),Lh(r,t,n))}}function Ah(e){var t=e.alternate;return e===ke||t!==null&&t===ke}function Dh(e,t){Zi=Gs=!0;var r=e.pending;r===null?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function Lh(e,t,r){if(r&4194240){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,vu(e,r)}}var Xs={readContext:Ct,useCallback:Ke,useContext:Ke,useEffect:Ke,useImperativeHandle:Ke,useInsertionEffect:Ke,useLayoutEffect:Ke,useMemo:Ke,useReducer:Ke,useRef:Ke,useState:Ke,useDebugValue:Ke,useDeferredValue:Ke,useTransition:Ke,useMutableSource:Ke,useSyncExternalStore:Ke,useId:Ke,unstable_isNewReconciler:!1},mx={readContext:Ct,useCallback:function(e,t){return Yt().memoizedState=[e,t===void 0?null:t],e},useContext:Ct,useEffect:mf,useImperativeHandle:function(e,t,r){return r=r!=null?r.concat([e]):null,ys(4194308,4,Ch.bind(null,t,e),r)},useLayoutEffect:function(e,t){return ys(4194308,4,e,t)},useInsertionEffect:function(e,t){return ys(4,2,e,t)},useMemo:function(e,t){var r=Yt();return t=t===void 0?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var n=Yt();return t=r!==void 0?r(t):t,n.memoizedState=n.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},n.queue=e,e=e.dispatch=fx.bind(null,ke,e),[n.memoizedState,e]},useRef:function(e){var t=Yt();return e={current:e},t.memoizedState=e},useState:pf,useDebugValue:Uu,useDeferredValue:function(e){return Yt().memoizedState=e},useTransition:function(){var e=pf(!1),t=e[0];return e=dx.bind(null,e[1]),Yt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var n=ke,i=Yt();if(xe){if(r===void 0)throw Error(R(407));r=r()}else{if(r=t(),Ue===null)throw Error(R(349));wn&30||xh(n,t,r)}i.memoizedState=r;var a={value:r,getSnapshot:t};return i.queue=a,mf(bh.bind(null,n,a,e),[e]),n.flags|=2048,wa(9,wh.bind(null,n,a,r,t),void 0,null),r},useId:function(){var e=Yt(),t=Ue.identifierPrefix;if(xe){var r=dr,n=ur;r=(n&~(1<<32-It(n)-1)).toString(32)+r,t=":"+t+"R"+r,r=ya++,0<r&&(t+="H"+r.toString(32)),t+=":"}else r=ux++,t=":"+t+"r"+r.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},hx={readContext:Ct,useCallback:Th,useContext:Ct,useEffect:Iu,useImperativeHandle:_h,useInsertionEffect:jh,useLayoutEffect:Nh,useMemo:Ph,useReducer:wl,useRef:Eh,useState:function(){return wl(xa)},useDebugValue:Uu,useDeferredValue:function(e){var t=_t();return Oh(t,De.memoizedState,e)},useTransition:function(){var e=wl(xa)[0],t=_t().memoizedState;return[e,t]},useMutableSource:vh,useSyncExternalStore:yh,useId:Rh,unstable_isNewReconciler:!1},gx={readContext:Ct,useCallback:Th,useContext:Ct,useEffect:Iu,useImperativeHandle:_h,useInsertionEffect:jh,useLayoutEffect:Nh,useMemo:Ph,useReducer:bl,useRef:Eh,useState:function(){return bl(xa)},useDebugValue:Uu,useDeferredValue:function(e){var t=_t();return De===null?t.memoizedState=e:Oh(t,De.memoizedState,e)},useTransition:function(){var e=bl(xa)[0],t=_t().memoizedState;return[e,t]},useMutableSource:vh,useSyncExternalStore:yh,useId:Rh,unstable_isNewReconciler:!1};function Rt(e,t){if(e&&e.defaultProps){t=Se({},t),e=e.defaultProps;for(var r in e)t[r]===void 0&&(t[r]=e[r]);return t}return t}function gc(e,t,r,n){t=e.memoizedState,r=r(n,t),r=r==null?t:Se({},t,r),e.memoizedState=r,e.lanes===0&&(e.updateQueue.baseState=r)}var No={isMounted:function(e){return(e=e._reactInternals)?Nn(e)===e:!1},enqueueSetState:function(e,t,r){e=e._reactInternals;var n=rt(),i=Vr(e),a=fr(n,i);a.payload=t,r!=null&&(a.callback=r),t=$r(e,a,i),t!==null&&(Ut(t,e,i,n),gs(t,e,i))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var n=rt(),i=Vr(e),a=fr(n,i);a.tag=1,a.payload=t,r!=null&&(a.callback=r),t=$r(e,a,i),t!==null&&(Ut(t,e,i,n),gs(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=rt(),n=Vr(e),i=fr(r,n);i.tag=2,t!=null&&(i.callback=t),t=$r(e,i,n),t!==null&&(Ut(t,e,n,r),gs(t,e,n))}};function hf(e,t,r,n,i,a,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(n,a,s):t.prototype&&t.prototype.isPureReactComponent?!fa(r,n)||!fa(i,a):!0}function Mh(e,t,r){var n=!1,i=Kr,a=t.contextType;return typeof a=="object"&&a!==null?a=Ct(a):(i=ut(t)?yn:Je.current,n=t.contextTypes,a=(n=n!=null)?ii(e,i):Kr),t=new t(r,a),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=No,e.stateNode=t,t._reactInternals=e,n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=a),t}function gf(e,t,r,n){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(r,n),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(r,n),t.state!==e&&No.enqueueReplaceState(t,t.state,null)}function vc(e,t,r,n){var i=e.stateNode;i.props=r,i.state=e.memoizedState,i.refs={},Ru(e);var a=t.contextType;typeof a=="object"&&a!==null?i.context=Ct(a):(a=ut(t)?yn:Je.current,i.context=ii(e,a)),i.state=e.memoizedState,a=t.getDerivedStateFromProps,typeof a=="function"&&(gc(e,t,a,r),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&No.enqueueReplaceState(i,i.state,null),Ks(e,r,i,n),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function li(e,t){try{var r="",n=t;do r+=Wy(n),n=n.return;while(n);var i=r}catch(a){i=`
Error generating stack: `+a.message+`
`+a.stack}return{value:e,source:t,stack:i,digest:null}}function kl(e,t,r){return{value:e,source:null,stack:r??null,digest:t??null}}function yc(e,t){try{console.error(t.value)}catch(r){setTimeout(function(){throw r})}}var vx=typeof WeakMap=="function"?WeakMap:Map;function Fh(e,t,r){r=fr(-1,r),r.tag=3,r.payload={element:null};var n=t.value;return r.callback=function(){Zs||(Zs=!0,_c=n),yc(e,t)},r}function zh(e,t,r){r=fr(-1,r),r.tag=3;var n=e.type.getDerivedStateFromError;if(typeof n=="function"){var i=t.value;r.payload=function(){return n(i)},r.callback=function(){yc(e,t)}}var a=e.stateNode;return a!==null&&typeof a.componentDidCatch=="function"&&(r.callback=function(){yc(e,t),typeof n!="function"&&(Br===null?Br=new Set([this]):Br.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),r}function vf(e,t,r){var n=e.pingCache;if(n===null){n=e.pingCache=new vx;var i=new Set;n.set(t,i)}else i=n.get(t),i===void 0&&(i=new Set,n.set(t,i));i.has(r)||(i.add(r),e=Ox.bind(null,e,t,r),t.then(e,e))}function yf(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function xf(e,t,r,n,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,r.flags|=131072,r.flags&=-52805,r.tag===1&&(r.alternate===null?r.tag=17:(t=fr(-1,1),t.tag=2,$r(r,t,1))),r.lanes|=1),e)}var yx=wr.ReactCurrentOwner,lt=!1;function Ze(e,t,r,n){t.child=e===null?ph(t,null,r,n):si(t,e.child,r,n)}function wf(e,t,r,n,i){r=r.render;var a=t.ref;return Zn(t,i),n=Fu(e,t,r,n,a,i),r=zu(),e!==null&&!lt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,gr(e,t,i)):(xe&&r&&ju(t),t.flags|=1,Ze(e,t,n,i),t.child)}function bf(e,t,r,n,i){if(e===null){var a=r.type;return typeof a=="function"&&!Ku(a)&&a.defaultProps===void 0&&r.compare===null&&r.defaultProps===void 0?(t.tag=15,t.type=a,Ih(e,t,a,n,i)):(e=ks(r.type,null,n,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(a=e.child,!(e.lanes&i)){var s=a.memoizedProps;if(r=r.compare,r=r!==null?r:fa,r(s,n)&&e.ref===t.ref)return gr(e,t,i)}return t.flags|=1,e=Wr(a,n),e.ref=t.ref,e.return=t,t.child=e}function Ih(e,t,r,n,i){if(e!==null){var a=e.memoizedProps;if(fa(a,n)&&e.ref===t.ref)if(lt=!1,t.pendingProps=n=a,(e.lanes&i)!==0)e.flags&131072&&(lt=!0);else return t.lanes=e.lanes,gr(e,t,i)}return xc(e,t,r,n,i)}function Uh(e,t,r){var n=t.pendingProps,i=n.children,a=e!==null?e.memoizedState:null;if(n.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},me(Kn,pt),pt|=r;else{if(!(r&1073741824))return e=a!==null?a.baseLanes|r:r,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,me(Kn,pt),pt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},n=a!==null?a.baseLanes:r,me(Kn,pt),pt|=n}else a!==null?(n=a.baseLanes|r,t.memoizedState=null):n=r,me(Kn,pt),pt|=n;return Ze(e,t,i,r),t.child}function $h(e,t){var r=t.ref;(e===null&&r!==null||e!==null&&e.ref!==r)&&(t.flags|=512,t.flags|=2097152)}function xc(e,t,r,n,i){var a=ut(r)?yn:Je.current;return a=ii(t,a),Zn(t,i),r=Fu(e,t,r,n,a,i),n=zu(),e!==null&&!lt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,gr(e,t,i)):(xe&&n&&ju(t),t.flags|=1,Ze(e,t,r,i),t.child)}function kf(e,t,r,n,i){if(ut(r)){var a=!0;Vs(t)}else a=!1;if(Zn(t,i),t.stateNode===null)xs(e,t),Mh(t,r,n),vc(t,r,n,i),n=!0;else if(e===null){var s=t.stateNode,o=t.memoizedProps;s.props=o;var c=s.context,u=r.contextType;typeof u=="object"&&u!==null?u=Ct(u):(u=ut(r)?yn:Je.current,u=ii(t,u));var d=r.getDerivedStateFromProps,f=typeof d=="function"||typeof s.getSnapshotBeforeUpdate=="function";f||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(o!==n||c!==u)&&gf(t,s,n,u),Tr=!1;var h=t.memoizedState;s.state=h,Ks(t,n,s,i),c=t.memoizedState,o!==n||h!==c||ct.current||Tr?(typeof d=="function"&&(gc(t,r,d,n),c=t.memoizedState),(o=Tr||hf(t,r,o,n,h,c,u))?(f||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=n,t.memoizedState=c),s.props=n,s.state=c,s.context=u,n=o):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),n=!1)}else{s=t.stateNode,hh(e,t),o=t.memoizedProps,u=t.type===t.elementType?o:Rt(t.type,o),s.props=u,f=t.pendingProps,h=s.context,c=r.contextType,typeof c=="object"&&c!==null?c=Ct(c):(c=ut(r)?yn:Je.current,c=ii(t,c));var w=r.getDerivedStateFromProps;(d=typeof w=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(o!==f||h!==c)&&gf(t,s,n,c),Tr=!1,h=t.memoizedState,s.state=h,Ks(t,n,s,i);var g=t.memoizedState;o!==f||h!==g||ct.current||Tr?(typeof w=="function"&&(gc(t,r,w,n),g=t.memoizedState),(u=Tr||hf(t,r,u,n,h,g,c)||!1)?(d||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(n,g,c),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(n,g,c)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=n,t.memoizedState=g),s.props=n,s.state=g,s.context=c,n=u):(typeof s.componentDidUpdate!="function"||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),n=!1)}return wc(e,t,r,n,a,i)}function wc(e,t,r,n,i,a){$h(e,t);var s=(t.flags&128)!==0;if(!n&&!s)return i&&of(t,r,!1),gr(e,t,a);n=t.stateNode,yx.current=t;var o=s&&typeof r.getDerivedStateFromError!="function"?null:n.render();return t.flags|=1,e!==null&&s?(t.child=si(t,e.child,null,a),t.child=si(t,null,o,a)):Ze(e,t,o,a),t.memoizedState=n.state,i&&of(t,r,!0),t.child}function Bh(e){var t=e.stateNode;t.pendingContext?sf(e,t.pendingContext,t.pendingContext!==t.context):t.context&&sf(e,t.context,!1),Au(e,t.containerInfo)}function Sf(e,t,r,n,i){return ai(),Cu(i),t.flags|=256,Ze(e,t,r,n),t.child}var bc={dehydrated:null,treeContext:null,retryLane:0};function kc(e){return{baseLanes:e,cachePool:null,transitions:null}}function Vh(e,t,r){var n=t.pendingProps,i=be.current,a=!1,s=(t.flags&128)!==0,o;if((o=s)||(o=e!==null&&e.memoizedState===null?!1:(i&2)!==0),o?(a=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),me(be,i&1),e===null)return mc(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=n.children,e=n.fallback,a?(n=t.mode,a=t.child,s={mode:"hidden",children:s},!(n&1)&&a!==null?(a.childLanes=0,a.pendingProps=s):a=To(s,n,0,null),e=vn(e,n,r,null),a.return=t,e.return=t,a.sibling=e,t.child=a,t.child.memoizedState=kc(r),t.memoizedState=bc,e):$u(t,s));if(i=e.memoizedState,i!==null&&(o=i.dehydrated,o!==null))return xx(e,t,s,n,o,i,r);if(a){a=n.fallback,s=t.mode,i=e.child,o=i.sibling;var c={mode:"hidden",children:n.children};return!(s&1)&&t.child!==i?(n=t.child,n.childLanes=0,n.pendingProps=c,t.deletions=null):(n=Wr(i,c),n.subtreeFlags=i.subtreeFlags&14680064),o!==null?a=Wr(o,a):(a=vn(a,s,r,null),a.flags|=2),a.return=t,n.return=t,n.sibling=a,t.child=n,n=a,a=t.child,s=e.child.memoizedState,s=s===null?kc(r):{baseLanes:s.baseLanes|r,cachePool:null,transitions:s.transitions},a.memoizedState=s,a.childLanes=e.childLanes&~r,t.memoizedState=bc,n}return a=e.child,e=a.sibling,n=Wr(a,{mode:"visible",children:n.children}),!(t.mode&1)&&(n.lanes=r),n.return=t,n.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n}function $u(e,t){return t=To({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function rs(e,t,r,n){return n!==null&&Cu(n),si(t,e.child,null,r),e=$u(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function xx(e,t,r,n,i,a,s){if(r)return t.flags&256?(t.flags&=-257,n=kl(Error(R(422))),rs(e,t,s,n)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(a=n.fallback,i=t.mode,n=To({mode:"visible",children:n.children},i,0,null),a=vn(a,i,s,null),a.flags|=2,n.return=t,a.return=t,n.sibling=a,t.child=n,t.mode&1&&si(t,e.child,null,s),t.child.memoizedState=kc(s),t.memoizedState=bc,a);if(!(t.mode&1))return rs(e,t,s,null);if(i.data==="$!"){if(n=i.nextSibling&&i.nextSibling.dataset,n)var o=n.dgst;return n=o,a=Error(R(419)),n=kl(a,n,void 0),rs(e,t,s,n)}if(o=(s&e.childLanes)!==0,lt||o){if(n=Ue,n!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(n.suspendedLanes|s)?0:i,i!==0&&i!==a.retryLane&&(a.retryLane=i,hr(e,i),Ut(n,e,i,-1))}return Yu(),n=kl(Error(R(421))),rs(e,t,s,n)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=Rx.bind(null,e),i._reactRetry=t,null):(e=a.treeContext,ht=Ur(i.nextSibling),gt=t,xe=!0,Lt=null,e!==null&&(kt[St++]=ur,kt[St++]=dr,kt[St++]=xn,ur=e.id,dr=e.overflow,xn=t),t=$u(t,n.children),t.flags|=4096,t)}function Ef(e,t,r){e.lanes|=t;var n=e.alternate;n!==null&&(n.lanes|=t),hc(e.return,t,r)}function Sl(e,t,r,n,i){var a=e.memoizedState;a===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:n,tail:r,tailMode:i}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=n,a.tail=r,a.tailMode=i)}function Wh(e,t,r){var n=t.pendingProps,i=n.revealOrder,a=n.tail;if(Ze(e,t,n.children,r),n=be.current,n&2)n=n&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ef(e,r,t);else if(e.tag===19)Ef(e,r,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}n&=1}if(me(be,n),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(r=t.child,i=null;r!==null;)e=r.alternate,e!==null&&Qs(e)===null&&(i=r),r=r.sibling;r=i,r===null?(i=t.child,t.child=null):(i=r.sibling,r.sibling=null),Sl(t,!1,i,r,a);break;case"backwards":for(r=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Qs(e)===null){t.child=i;break}e=i.sibling,i.sibling=r,r=i,i=e}Sl(t,!0,r,null,a);break;case"together":Sl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function xs(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function gr(e,t,r){if(e!==null&&(t.dependencies=e.dependencies),bn|=t.lanes,!(r&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(R(153));if(t.child!==null){for(e=t.child,r=Wr(e,e.pendingProps),t.child=r,r.return=t;e.sibling!==null;)e=e.sibling,r=r.sibling=Wr(e,e.pendingProps),r.return=t;r.sibling=null}return t.child}function wx(e,t,r){switch(t.tag){case 3:Bh(t),ai();break;case 5:gh(t);break;case 1:ut(t.type)&&Vs(t);break;case 4:Au(t,t.stateNode.containerInfo);break;case 10:var n=t.type._context,i=t.memoizedProps.value;me(Hs,n._currentValue),n._currentValue=i;break;case 13:if(n=t.memoizedState,n!==null)return n.dehydrated!==null?(me(be,be.current&1),t.flags|=128,null):r&t.child.childLanes?Vh(e,t,r):(me(be,be.current&1),e=gr(e,t,r),e!==null?e.sibling:null);me(be,be.current&1);break;case 19:if(n=(r&t.childLanes)!==0,e.flags&128){if(n)return Wh(e,t,r);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),me(be,be.current),n)break;return null;case 22:case 23:return t.lanes=0,Uh(e,t,r)}return gr(e,t,r)}var qh,Sc,Hh,Yh;qh=function(e,t){for(var r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}};Sc=function(){};Hh=function(e,t,r,n){var i=e.memoizedProps;if(i!==n){e=t.stateNode,pn(er.current);var a=null;switch(r){case"input":i=ql(e,i),n=ql(e,n),a=[];break;case"select":i=Se({},i,{value:void 0}),n=Se({},n,{value:void 0}),a=[];break;case"textarea":i=Kl(e,i),n=Kl(e,n),a=[];break;default:typeof i.onClick!="function"&&typeof n.onClick=="function"&&(e.onclick=$s)}Gl(r,n);var s;r=null;for(u in i)if(!n.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var o=i[u];for(s in o)o.hasOwnProperty(s)&&(r||(r={}),r[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(aa.hasOwnProperty(u)?a||(a=[]):(a=a||[]).push(u,null));for(u in n){var c=n[u];if(o=i!=null?i[u]:void 0,n.hasOwnProperty(u)&&c!==o&&(c!=null||o!=null))if(u==="style")if(o){for(s in o)!o.hasOwnProperty(s)||c&&c.hasOwnProperty(s)||(r||(r={}),r[s]="");for(s in c)c.hasOwnProperty(s)&&o[s]!==c[s]&&(r||(r={}),r[s]=c[s])}else r||(a||(a=[]),a.push(u,r)),r=c;else u==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,o=o?o.__html:void 0,c!=null&&o!==c&&(a=a||[]).push(u,c)):u==="children"?typeof c!="string"&&typeof c!="number"||(a=a||[]).push(u,""+c):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(aa.hasOwnProperty(u)?(c!=null&&u==="onScroll"&&ge("scroll",e),a||o===c||(a=[])):(a=a||[]).push(u,c))}r&&(a=a||[]).push("style",r);var u=a;(t.updateQueue=u)&&(t.flags|=4)}};Yh=function(e,t,r,n){r!==n&&(t.flags|=4)};function Mi(e,t){if(!xe)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;t!==null;)t.alternate!==null&&(r=t),t=t.sibling;r===null?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var n=null;r!==null;)r.alternate!==null&&(n=r),r=r.sibling;n===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:n.sibling=null}}function Qe(e){var t=e.alternate!==null&&e.alternate.child===e.child,r=0,n=0;if(t)for(var i=e.child;i!==null;)r|=i.lanes|i.childLanes,n|=i.subtreeFlags&14680064,n|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)r|=i.lanes|i.childLanes,n|=i.subtreeFlags,n|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=n,e.childLanes=r,t}function bx(e,t,r){var n=t.pendingProps;switch(Nu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Qe(t),null;case 1:return ut(t.type)&&Bs(),Qe(t),null;case 3:return n=t.stateNode,oi(),ve(ct),ve(Je),Lu(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(es(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Lt!==null&&(Oc(Lt),Lt=null))),Sc(e,t),Qe(t),null;case 5:Du(t);var i=pn(va.current);if(r=t.type,e!==null&&t.stateNode!=null)Hh(e,t,r,n,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!n){if(t.stateNode===null)throw Error(R(166));return Qe(t),null}if(e=pn(er.current),es(t)){n=t.stateNode,r=t.type;var a=t.memoizedProps;switch(n[Qt]=t,n[ha]=a,e=(t.mode&1)!==0,r){case"dialog":ge("cancel",n),ge("close",n);break;case"iframe":case"object":case"embed":ge("load",n);break;case"video":case"audio":for(i=0;i<Hi.length;i++)ge(Hi[i],n);break;case"source":ge("error",n);break;case"img":case"image":case"link":ge("error",n),ge("load",n);break;case"details":ge("toggle",n);break;case"input":Rd(n,a),ge("invalid",n);break;case"select":n._wrapperState={wasMultiple:!!a.multiple},ge("invalid",n);break;case"textarea":Dd(n,a),ge("invalid",n)}Gl(r,a),i=null;for(var s in a)if(a.hasOwnProperty(s)){var o=a[s];s==="children"?typeof o=="string"?n.textContent!==o&&(a.suppressHydrationWarning!==!0&&Za(n.textContent,o,e),i=["children",o]):typeof o=="number"&&n.textContent!==""+o&&(a.suppressHydrationWarning!==!0&&Za(n.textContent,o,e),i=["children",""+o]):aa.hasOwnProperty(s)&&o!=null&&s==="onScroll"&&ge("scroll",n)}switch(r){case"input":qa(n),Ad(n,a,!0);break;case"textarea":qa(n),Ld(n);break;case"select":case"option":break;default:typeof a.onClick=="function"&&(n.onclick=$s)}n=i,t.updateQueue=n,n!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=bm(r)),e==="http://www.w3.org/1999/xhtml"?r==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof n.is=="string"?e=s.createElement(r,{is:n.is}):(e=s.createElement(r),r==="select"&&(s=e,n.multiple?s.multiple=!0:n.size&&(s.size=n.size))):e=s.createElementNS(e,r),e[Qt]=t,e[ha]=n,qh(e,t,!1,!1),t.stateNode=e;e:{switch(s=Xl(r,n),r){case"dialog":ge("cancel",e),ge("close",e),i=n;break;case"iframe":case"object":case"embed":ge("load",e),i=n;break;case"video":case"audio":for(i=0;i<Hi.length;i++)ge(Hi[i],e);i=n;break;case"source":ge("error",e),i=n;break;case"img":case"image":case"link":ge("error",e),ge("load",e),i=n;break;case"details":ge("toggle",e),i=n;break;case"input":Rd(e,n),i=ql(e,n),ge("invalid",e);break;case"option":i=n;break;case"select":e._wrapperState={wasMultiple:!!n.multiple},i=Se({},n,{value:void 0}),ge("invalid",e);break;case"textarea":Dd(e,n),i=Kl(e,n),ge("invalid",e);break;default:i=n}Gl(r,i),o=i;for(a in o)if(o.hasOwnProperty(a)){var c=o[a];a==="style"?Em(e,c):a==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&km(e,c)):a==="children"?typeof c=="string"?(r!=="textarea"||c!=="")&&sa(e,c):typeof c=="number"&&sa(e,""+c):a!=="suppressContentEditableWarning"&&a!=="suppressHydrationWarning"&&a!=="autoFocus"&&(aa.hasOwnProperty(a)?c!=null&&a==="onScroll"&&ge("scroll",e):c!=null&&du(e,a,c,s))}switch(r){case"input":qa(e),Ad(e,n,!1);break;case"textarea":qa(e),Ld(e);break;case"option":n.value!=null&&e.setAttribute("value",""+Yr(n.value));break;case"select":e.multiple=!!n.multiple,a=n.value,a!=null?Qn(e,!!n.multiple,a,!1):n.defaultValue!=null&&Qn(e,!!n.multiple,n.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=$s)}switch(r){case"button":case"input":case"select":case"textarea":n=!!n.autoFocus;break e;case"img":n=!0;break e;default:n=!1}}n&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Qe(t),null;case 6:if(e&&t.stateNode!=null)Yh(e,t,e.memoizedProps,n);else{if(typeof n!="string"&&t.stateNode===null)throw Error(R(166));if(r=pn(va.current),pn(er.current),es(t)){if(n=t.stateNode,r=t.memoizedProps,n[Qt]=t,(a=n.nodeValue!==r)&&(e=gt,e!==null))switch(e.tag){case 3:Za(n.nodeValue,r,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Za(n.nodeValue,r,(e.mode&1)!==0)}a&&(t.flags|=4)}else n=(r.nodeType===9?r:r.ownerDocument).createTextNode(n),n[Qt]=t,t.stateNode=n}return Qe(t),null;case 13:if(ve(be),n=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(xe&&ht!==null&&t.mode&1&&!(t.flags&128))dh(),ai(),t.flags|=98560,a=!1;else if(a=es(t),n!==null&&n.dehydrated!==null){if(e===null){if(!a)throw Error(R(318));if(a=t.memoizedState,a=a!==null?a.dehydrated:null,!a)throw Error(R(317));a[Qt]=t}else ai(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Qe(t),a=!1}else Lt!==null&&(Oc(Lt),Lt=null),a=!0;if(!a)return t.flags&65536?t:null}return t.flags&128?(t.lanes=r,t):(n=n!==null,n!==(e!==null&&e.memoizedState!==null)&&n&&(t.child.flags|=8192,t.mode&1&&(e===null||be.current&1?Me===0&&(Me=3):Yu())),t.updateQueue!==null&&(t.flags|=4),Qe(t),null);case 4:return oi(),Sc(e,t),e===null&&pa(t.stateNode.containerInfo),Qe(t),null;case 10:return Pu(t.type._context),Qe(t),null;case 17:return ut(t.type)&&Bs(),Qe(t),null;case 19:if(ve(be),a=t.memoizedState,a===null)return Qe(t),null;if(n=(t.flags&128)!==0,s=a.rendering,s===null)if(n)Mi(a,!1);else{if(Me!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=Qs(e),s!==null){for(t.flags|=128,Mi(a,!1),n=s.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),t.subtreeFlags=0,n=r,r=t.child;r!==null;)a=r,e=n,a.flags&=14680066,s=a.alternate,s===null?(a.childLanes=0,a.lanes=e,a.child=null,a.subtreeFlags=0,a.memoizedProps=null,a.memoizedState=null,a.updateQueue=null,a.dependencies=null,a.stateNode=null):(a.childLanes=s.childLanes,a.lanes=s.lanes,a.child=s.child,a.subtreeFlags=0,a.deletions=null,a.memoizedProps=s.memoizedProps,a.memoizedState=s.memoizedState,a.updateQueue=s.updateQueue,a.type=s.type,e=s.dependencies,a.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return me(be,be.current&1|2),t.child}e=e.sibling}a.tail!==null&&Ne()>ci&&(t.flags|=128,n=!0,Mi(a,!1),t.lanes=4194304)}else{if(!n)if(e=Qs(s),e!==null){if(t.flags|=128,n=!0,r=e.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),Mi(a,!0),a.tail===null&&a.tailMode==="hidden"&&!s.alternate&&!xe)return Qe(t),null}else 2*Ne()-a.renderingStartTime>ci&&r!==1073741824&&(t.flags|=128,n=!0,Mi(a,!1),t.lanes=4194304);a.isBackwards?(s.sibling=t.child,t.child=s):(r=a.last,r!==null?r.sibling=s:t.child=s,a.last=s)}return a.tail!==null?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=Ne(),t.sibling=null,r=be.current,me(be,n?r&1|2:r&1),t):(Qe(t),null);case 22:case 23:return Hu(),n=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==n&&(t.flags|=8192),n&&t.mode&1?pt&1073741824&&(Qe(t),t.subtreeFlags&6&&(t.flags|=8192)):Qe(t),null;case 24:return null;case 25:return null}throw Error(R(156,t.tag))}function kx(e,t){switch(Nu(t),t.tag){case 1:return ut(t.type)&&Bs(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return oi(),ve(ct),ve(Je),Lu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Du(t),null;case 13:if(ve(be),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(R(340));ai()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ve(be),null;case 4:return oi(),null;case 10:return Pu(t.type._context),null;case 22:case 23:return Hu(),null;case 24:return null;default:return null}}var ns=!1,Ge=!1,Sx=typeof WeakSet=="function"?WeakSet:Set,$=null;function Yn(e,t){var r=e.ref;if(r!==null)if(typeof r=="function")try{r(null)}catch(n){Ee(e,t,n)}else r.current=null}function Ec(e,t,r){try{r()}catch(n){Ee(e,t,n)}}var jf=!1;function Ex(e,t){if(oc=zs,e=Jm(),Eu(e)){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{r=(r=e.ownerDocument)&&r.defaultView||window;var n=r.getSelection&&r.getSelection();if(n&&n.rangeCount!==0){r=n.anchorNode;var i=n.anchorOffset,a=n.focusNode;n=n.focusOffset;try{r.nodeType,a.nodeType}catch{r=null;break e}var s=0,o=-1,c=-1,u=0,d=0,f=e,h=null;t:for(;;){for(var w;f!==r||i!==0&&f.nodeType!==3||(o=s+i),f!==a||n!==0&&f.nodeType!==3||(c=s+n),f.nodeType===3&&(s+=f.nodeValue.length),(w=f.firstChild)!==null;)h=f,f=w;for(;;){if(f===e)break t;if(h===r&&++u===i&&(o=s),h===a&&++d===n&&(c=s),(w=f.nextSibling)!==null)break;f=h,h=f.parentNode}f=w}r=o===-1||c===-1?null:{start:o,end:c}}else r=null}r=r||{start:0,end:0}}else r=null;for(lc={focusedElem:e,selectionRange:r},zs=!1,$=t;$!==null;)if(t=$,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,$=e;else for(;$!==null;){t=$;try{var g=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(g!==null){var y=g.memoizedProps,x=g.memoizedState,m=t.stateNode,p=m.getSnapshotBeforeUpdate(t.elementType===t.type?y:Rt(t.type,y),x);m.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var v=t.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(R(163))}}catch(k){Ee(t,t.return,k)}if(e=t.sibling,e!==null){e.return=t.return,$=e;break}$=t.return}return g=jf,jf=!1,g}function ea(e,t,r){var n=t.updateQueue;if(n=n!==null?n.lastEffect:null,n!==null){var i=n=n.next;do{if((i.tag&e)===e){var a=i.destroy;i.destroy=void 0,a!==void 0&&Ec(t,r,a)}i=i.next}while(i!==n)}}function Co(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var r=t=t.next;do{if((r.tag&e)===e){var n=r.create;r.destroy=n()}r=r.next}while(r!==t)}}function jc(e){var t=e.ref;if(t!==null){var r=e.stateNode;switch(e.tag){case 5:e=r;break;default:e=r}typeof t=="function"?t(e):t.current=e}}function Kh(e){var t=e.alternate;t!==null&&(e.alternate=null,Kh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Qt],delete t[ha],delete t[dc],delete t[sx],delete t[ox])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Qh(e){return e.tag===5||e.tag===3||e.tag===4}function Nf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Qh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Nc(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.nodeType===8?r.parentNode.insertBefore(e,t):r.insertBefore(e,t):(r.nodeType===8?(t=r.parentNode,t.insertBefore(e,r)):(t=r,t.appendChild(e)),r=r._reactRootContainer,r!=null||t.onclick!==null||(t.onclick=$s));else if(n!==4&&(e=e.child,e!==null))for(Nc(e,t,r),e=e.sibling;e!==null;)Nc(e,t,r),e=e.sibling}function Cc(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.insertBefore(e,t):r.appendChild(e);else if(n!==4&&(e=e.child,e!==null))for(Cc(e,t,r),e=e.sibling;e!==null;)Cc(e,t,r),e=e.sibling}var Be=null,At=!1;function Er(e,t,r){for(r=r.child;r!==null;)Gh(e,t,r),r=r.sibling}function Gh(e,t,r){if(Zt&&typeof Zt.onCommitFiberUnmount=="function")try{Zt.onCommitFiberUnmount(xo,r)}catch{}switch(r.tag){case 5:Ge||Yn(r,t);case 6:var n=Be,i=At;Be=null,Er(e,t,r),Be=n,At=i,Be!==null&&(At?(e=Be,r=r.stateNode,e.nodeType===8?e.parentNode.removeChild(r):e.removeChild(r)):Be.removeChild(r.stateNode));break;case 18:Be!==null&&(At?(e=Be,r=r.stateNode,e.nodeType===8?gl(e.parentNode,r):e.nodeType===1&&gl(e,r),ua(e)):gl(Be,r.stateNode));break;case 4:n=Be,i=At,Be=r.stateNode.containerInfo,At=!0,Er(e,t,r),Be=n,At=i;break;case 0:case 11:case 14:case 15:if(!Ge&&(n=r.updateQueue,n!==null&&(n=n.lastEffect,n!==null))){i=n=n.next;do{var a=i,s=a.destroy;a=a.tag,s!==void 0&&(a&2||a&4)&&Ec(r,t,s),i=i.next}while(i!==n)}Er(e,t,r);break;case 1:if(!Ge&&(Yn(r,t),n=r.stateNode,typeof n.componentWillUnmount=="function"))try{n.props=r.memoizedProps,n.state=r.memoizedState,n.componentWillUnmount()}catch(o){Ee(r,t,o)}Er(e,t,r);break;case 21:Er(e,t,r);break;case 22:r.mode&1?(Ge=(n=Ge)||r.memoizedState!==null,Er(e,t,r),Ge=n):Er(e,t,r);break;default:Er(e,t,r)}}function Cf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var r=e.stateNode;r===null&&(r=e.stateNode=new Sx),t.forEach(function(n){var i=Ax.bind(null,e,n);r.has(n)||(r.add(n),n.then(i,i))})}}function Ot(e,t){var r=t.deletions;if(r!==null)for(var n=0;n<r.length;n++){var i=r[n];try{var a=e,s=t,o=s;e:for(;o!==null;){switch(o.tag){case 5:Be=o.stateNode,At=!1;break e;case 3:Be=o.stateNode.containerInfo,At=!0;break e;case 4:Be=o.stateNode.containerInfo,At=!0;break e}o=o.return}if(Be===null)throw Error(R(160));Gh(a,s,i),Be=null,At=!1;var c=i.alternate;c!==null&&(c.return=null),i.return=null}catch(u){Ee(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Xh(t,e),t=t.sibling}function Xh(e,t){var r=e.alternate,n=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ot(t,e),Wt(e),n&4){try{ea(3,e,e.return),Co(3,e)}catch(y){Ee(e,e.return,y)}try{ea(5,e,e.return)}catch(y){Ee(e,e.return,y)}}break;case 1:Ot(t,e),Wt(e),n&512&&r!==null&&Yn(r,r.return);break;case 5:if(Ot(t,e),Wt(e),n&512&&r!==null&&Yn(r,r.return),e.flags&32){var i=e.stateNode;try{sa(i,"")}catch(y){Ee(e,e.return,y)}}if(n&4&&(i=e.stateNode,i!=null)){var a=e.memoizedProps,s=r!==null?r.memoizedProps:a,o=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{o==="input"&&a.type==="radio"&&a.name!=null&&xm(i,a),Xl(o,s);var u=Xl(o,a);for(s=0;s<c.length;s+=2){var d=c[s],f=c[s+1];d==="style"?Em(i,f):d==="dangerouslySetInnerHTML"?km(i,f):d==="children"?sa(i,f):du(i,d,f,u)}switch(o){case"input":Hl(i,a);break;case"textarea":wm(i,a);break;case"select":var h=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!a.multiple;var w=a.value;w!=null?Qn(i,!!a.multiple,w,!1):h!==!!a.multiple&&(a.defaultValue!=null?Qn(i,!!a.multiple,a.defaultValue,!0):Qn(i,!!a.multiple,a.multiple?[]:"",!1))}i[ha]=a}catch(y){Ee(e,e.return,y)}}break;case 6:if(Ot(t,e),Wt(e),n&4){if(e.stateNode===null)throw Error(R(162));i=e.stateNode,a=e.memoizedProps;try{i.nodeValue=a}catch(y){Ee(e,e.return,y)}}break;case 3:if(Ot(t,e),Wt(e),n&4&&r!==null&&r.memoizedState.isDehydrated)try{ua(t.containerInfo)}catch(y){Ee(e,e.return,y)}break;case 4:Ot(t,e),Wt(e);break;case 13:Ot(t,e),Wt(e),i=e.child,i.flags&8192&&(a=i.memoizedState!==null,i.stateNode.isHidden=a,!a||i.alternate!==null&&i.alternate.memoizedState!==null||(Wu=Ne())),n&4&&Cf(e);break;case 22:if(d=r!==null&&r.memoizedState!==null,e.mode&1?(Ge=(u=Ge)||d,Ot(t,e),Ge=u):Ot(t,e),Wt(e),n&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for($=e,d=e.child;d!==null;){for(f=$=d;$!==null;){switch(h=$,w=h.child,h.tag){case 0:case 11:case 14:case 15:ea(4,h,h.return);break;case 1:Yn(h,h.return);var g=h.stateNode;if(typeof g.componentWillUnmount=="function"){n=h,r=h.return;try{t=n,g.props=t.memoizedProps,g.state=t.memoizedState,g.componentWillUnmount()}catch(y){Ee(n,r,y)}}break;case 5:Yn(h,h.return);break;case 22:if(h.memoizedState!==null){Tf(f);continue}}w!==null?(w.return=h,$=w):Tf(f)}d=d.sibling}e:for(d=null,f=e;;){if(f.tag===5){if(d===null){d=f;try{i=f.stateNode,u?(a=i.style,typeof a.setProperty=="function"?a.setProperty("display","none","important"):a.display="none"):(o=f.stateNode,c=f.memoizedProps.style,s=c!=null&&c.hasOwnProperty("display")?c.display:null,o.style.display=Sm("display",s))}catch(y){Ee(e,e.return,y)}}}else if(f.tag===6){if(d===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(y){Ee(e,e.return,y)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:Ot(t,e),Wt(e),n&4&&Cf(e);break;case 21:break;default:Ot(t,e),Wt(e)}}function Wt(e){var t=e.flags;if(t&2){try{e:{for(var r=e.return;r!==null;){if(Qh(r)){var n=r;break e}r=r.return}throw Error(R(160))}switch(n.tag){case 5:var i=n.stateNode;n.flags&32&&(sa(i,""),n.flags&=-33);var a=Nf(e);Cc(e,a,i);break;case 3:case 4:var s=n.stateNode.containerInfo,o=Nf(e);Nc(e,o,s);break;default:throw Error(R(161))}}catch(c){Ee(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function jx(e,t,r){$=e,Jh(e)}function Jh(e,t,r){for(var n=(e.mode&1)!==0;$!==null;){var i=$,a=i.child;if(i.tag===22&&n){var s=i.memoizedState!==null||ns;if(!s){var o=i.alternate,c=o!==null&&o.memoizedState!==null||Ge;o=ns;var u=Ge;if(ns=s,(Ge=c)&&!u)for($=i;$!==null;)s=$,c=s.child,s.tag===22&&s.memoizedState!==null?Pf(i):c!==null?(c.return=s,$=c):Pf(i);for(;a!==null;)$=a,Jh(a),a=a.sibling;$=i,ns=o,Ge=u}_f(e)}else i.subtreeFlags&8772&&a!==null?(a.return=i,$=a):_f(e)}}function _f(e){for(;$!==null;){var t=$;if(t.flags&8772){var r=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Ge||Co(5,t);break;case 1:var n=t.stateNode;if(t.flags&4&&!Ge)if(r===null)n.componentDidMount();else{var i=t.elementType===t.type?r.memoizedProps:Rt(t.type,r.memoizedProps);n.componentDidUpdate(i,r.memoizedState,n.__reactInternalSnapshotBeforeUpdate)}var a=t.updateQueue;a!==null&&ff(t,a,n);break;case 3:var s=t.updateQueue;if(s!==null){if(r=null,t.child!==null)switch(t.child.tag){case 5:r=t.child.stateNode;break;case 1:r=t.child.stateNode}ff(t,s,r)}break;case 5:var o=t.stateNode;if(r===null&&t.flags&4){r=o;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&r.focus();break;case"img":c.src&&(r.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var f=d.dehydrated;f!==null&&ua(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(R(163))}Ge||t.flags&512&&jc(t)}catch(h){Ee(t,t.return,h)}}if(t===e){$=null;break}if(r=t.sibling,r!==null){r.return=t.return,$=r;break}$=t.return}}function Tf(e){for(;$!==null;){var t=$;if(t===e){$=null;break}var r=t.sibling;if(r!==null){r.return=t.return,$=r;break}$=t.return}}function Pf(e){for(;$!==null;){var t=$;try{switch(t.tag){case 0:case 11:case 15:var r=t.return;try{Co(4,t)}catch(c){Ee(t,r,c)}break;case 1:var n=t.stateNode;if(typeof n.componentDidMount=="function"){var i=t.return;try{n.componentDidMount()}catch(c){Ee(t,i,c)}}var a=t.return;try{jc(t)}catch(c){Ee(t,a,c)}break;case 5:var s=t.return;try{jc(t)}catch(c){Ee(t,s,c)}}}catch(c){Ee(t,t.return,c)}if(t===e){$=null;break}var o=t.sibling;if(o!==null){o.return=t.return,$=o;break}$=t.return}}var Nx=Math.ceil,Js=wr.ReactCurrentDispatcher,Bu=wr.ReactCurrentOwner,Nt=wr.ReactCurrentBatchConfig,ie=0,Ue=null,Pe=null,He=0,pt=0,Kn=Gr(0),Me=0,ba=null,bn=0,_o=0,Vu=0,ta=null,ot=null,Wu=0,ci=1/0,lr=null,Zs=!1,_c=null,Br=null,is=!1,Lr=null,eo=0,ra=0,Tc=null,ws=-1,bs=0;function rt(){return ie&6?Ne():ws!==-1?ws:ws=Ne()}function Vr(e){return e.mode&1?ie&2&&He!==0?He&-He:cx.transition!==null?(bs===0&&(bs=Mm()),bs):(e=ue,e!==0||(e=window.event,e=e===void 0?16:Vm(e.type)),e):1}function Ut(e,t,r,n){if(50<ra)throw ra=0,Tc=null,Error(R(185));Ca(e,r,n),(!(ie&2)||e!==Ue)&&(e===Ue&&(!(ie&2)&&(_o|=r),Me===4&&Or(e,He)),dt(e,n),r===1&&ie===0&&!(t.mode&1)&&(ci=Ne()+500,Eo&&Xr()))}function dt(e,t){var r=e.callbackNode;c0(e,t);var n=Fs(e,e===Ue?He:0);if(n===0)r!==null&&zd(r),e.callbackNode=null,e.callbackPriority=0;else if(t=n&-n,e.callbackPriority!==t){if(r!=null&&zd(r),t===1)e.tag===0?lx(Of.bind(null,e)):lh(Of.bind(null,e)),ix(function(){!(ie&6)&&Xr()}),r=null;else{switch(Fm(n)){case 1:r=gu;break;case 4:r=Dm;break;case 16:r=Ms;break;case 536870912:r=Lm;break;default:r=Ms}r=sg(r,Zh.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function Zh(e,t){if(ws=-1,bs=0,ie&6)throw Error(R(327));var r=e.callbackNode;if(ei()&&e.callbackNode!==r)return null;var n=Fs(e,e===Ue?He:0);if(n===0)return null;if(n&30||n&e.expiredLanes||t)t=to(e,n);else{t=n;var i=ie;ie|=2;var a=tg();(Ue!==e||He!==t)&&(lr=null,ci=Ne()+500,gn(e,t));do try{Tx();break}catch(o){eg(e,o)}while(1);Tu(),Js.current=a,ie=i,Pe!==null?t=0:(Ue=null,He=0,t=Me)}if(t!==0){if(t===2&&(i=rc(e),i!==0&&(n=i,t=Pc(e,i))),t===1)throw r=ba,gn(e,0),Or(e,n),dt(e,Ne()),r;if(t===6)Or(e,n);else{if(i=e.current.alternate,!(n&30)&&!Cx(i)&&(t=to(e,n),t===2&&(a=rc(e),a!==0&&(n=a,t=Pc(e,a))),t===1))throw r=ba,gn(e,0),Or(e,n),dt(e,Ne()),r;switch(e.finishedWork=i,e.finishedLanes=n,t){case 0:case 1:throw Error(R(345));case 2:on(e,ot,lr);break;case 3:if(Or(e,n),(n&130023424)===n&&(t=Wu+500-Ne(),10<t)){if(Fs(e,0)!==0)break;if(i=e.suspendedLanes,(i&n)!==n){rt(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=uc(on.bind(null,e,ot,lr),t);break}on(e,ot,lr);break;case 4:if(Or(e,n),(n&4194240)===n)break;for(t=e.eventTimes,i=-1;0<n;){var s=31-It(n);a=1<<s,s=t[s],s>i&&(i=s),n&=~a}if(n=i,n=Ne()-n,n=(120>n?120:480>n?480:1080>n?1080:1920>n?1920:3e3>n?3e3:4320>n?4320:1960*Nx(n/1960))-n,10<n){e.timeoutHandle=uc(on.bind(null,e,ot,lr),n);break}on(e,ot,lr);break;case 5:on(e,ot,lr);break;default:throw Error(R(329))}}}return dt(e,Ne()),e.callbackNode===r?Zh.bind(null,e):null}function Pc(e,t){var r=ta;return e.current.memoizedState.isDehydrated&&(gn(e,t).flags|=256),e=to(e,t),e!==2&&(t=ot,ot=r,t!==null&&Oc(t)),e}function Oc(e){ot===null?ot=e:ot.push.apply(ot,e)}function Cx(e){for(var t=e;;){if(t.flags&16384){var r=t.updateQueue;if(r!==null&&(r=r.stores,r!==null))for(var n=0;n<r.length;n++){var i=r[n],a=i.getSnapshot;i=i.value;try{if(!Bt(a(),i))return!1}catch{return!1}}}if(r=t.child,t.subtreeFlags&16384&&r!==null)r.return=t,t=r;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Or(e,t){for(t&=~Vu,t&=~_o,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var r=31-It(t),n=1<<r;e[r]=-1,t&=~n}}function Of(e){if(ie&6)throw Error(R(327));ei();var t=Fs(e,0);if(!(t&1))return dt(e,Ne()),null;var r=to(e,t);if(e.tag!==0&&r===2){var n=rc(e);n!==0&&(t=n,r=Pc(e,n))}if(r===1)throw r=ba,gn(e,0),Or(e,t),dt(e,Ne()),r;if(r===6)throw Error(R(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,on(e,ot,lr),dt(e,Ne()),null}function qu(e,t){var r=ie;ie|=1;try{return e(t)}finally{ie=r,ie===0&&(ci=Ne()+500,Eo&&Xr())}}function kn(e){Lr!==null&&Lr.tag===0&&!(ie&6)&&ei();var t=ie;ie|=1;var r=Nt.transition,n=ue;try{if(Nt.transition=null,ue=1,e)return e()}finally{ue=n,Nt.transition=r,ie=t,!(ie&6)&&Xr()}}function Hu(){pt=Kn.current,ve(Kn)}function gn(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;if(r!==-1&&(e.timeoutHandle=-1,nx(r)),Pe!==null)for(r=Pe.return;r!==null;){var n=r;switch(Nu(n),n.tag){case 1:n=n.type.childContextTypes,n!=null&&Bs();break;case 3:oi(),ve(ct),ve(Je),Lu();break;case 5:Du(n);break;case 4:oi();break;case 13:ve(be);break;case 19:ve(be);break;case 10:Pu(n.type._context);break;case 22:case 23:Hu()}r=r.return}if(Ue=e,Pe=e=Wr(e.current,null),He=pt=t,Me=0,ba=null,Vu=_o=bn=0,ot=ta=null,fn!==null){for(t=0;t<fn.length;t++)if(r=fn[t],n=r.interleaved,n!==null){r.interleaved=null;var i=n.next,a=r.pending;if(a!==null){var s=a.next;a.next=i,n.next=s}r.pending=n}fn=null}return e}function eg(e,t){do{var r=Pe;try{if(Tu(),vs.current=Xs,Gs){for(var n=ke.memoizedState;n!==null;){var i=n.queue;i!==null&&(i.pending=null),n=n.next}Gs=!1}if(wn=0,Ie=De=ke=null,Zi=!1,ya=0,Bu.current=null,r===null||r.return===null){Me=1,ba=t,Pe=null;break}e:{var a=e,s=r.return,o=r,c=t;if(t=He,o.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var u=c,d=o,f=d.tag;if(!(d.mode&1)&&(f===0||f===11||f===15)){var h=d.alternate;h?(d.updateQueue=h.updateQueue,d.memoizedState=h.memoizedState,d.lanes=h.lanes):(d.updateQueue=null,d.memoizedState=null)}var w=yf(s);if(w!==null){w.flags&=-257,xf(w,s,o,a,t),w.mode&1&&vf(a,u,t),t=w,c=u;var g=t.updateQueue;if(g===null){var y=new Set;y.add(c),t.updateQueue=y}else g.add(c);break e}else{if(!(t&1)){vf(a,u,t),Yu();break e}c=Error(R(426))}}else if(xe&&o.mode&1){var x=yf(s);if(x!==null){!(x.flags&65536)&&(x.flags|=256),xf(x,s,o,a,t),Cu(li(c,o));break e}}a=c=li(c,o),Me!==4&&(Me=2),ta===null?ta=[a]:ta.push(a),a=s;do{switch(a.tag){case 3:a.flags|=65536,t&=-t,a.lanes|=t;var m=Fh(a,c,t);df(a,m);break e;case 1:o=c;var p=a.type,v=a.stateNode;if(!(a.flags&128)&&(typeof p.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(Br===null||!Br.has(v)))){a.flags|=65536,t&=-t,a.lanes|=t;var k=zh(a,o,t);df(a,k);break e}}a=a.return}while(a!==null)}ng(r)}catch(_){t=_,Pe===r&&r!==null&&(Pe=r=r.return);continue}break}while(1)}function tg(){var e=Js.current;return Js.current=Xs,e===null?Xs:e}function Yu(){(Me===0||Me===3||Me===2)&&(Me=4),Ue===null||!(bn&268435455)&&!(_o&268435455)||Or(Ue,He)}function to(e,t){var r=ie;ie|=2;var n=tg();(Ue!==e||He!==t)&&(lr=null,gn(e,t));do try{_x();break}catch(i){eg(e,i)}while(1);if(Tu(),ie=r,Js.current=n,Pe!==null)throw Error(R(261));return Ue=null,He=0,Me}function _x(){for(;Pe!==null;)rg(Pe)}function Tx(){for(;Pe!==null&&!e0();)rg(Pe)}function rg(e){var t=ag(e.alternate,e,pt);e.memoizedProps=e.pendingProps,t===null?ng(e):Pe=t,Bu.current=null}function ng(e){var t=e;do{var r=t.alternate;if(e=t.return,t.flags&32768){if(r=kx(r,t),r!==null){r.flags&=32767,Pe=r;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Me=6,Pe=null;return}}else if(r=bx(r,t,pt),r!==null){Pe=r;return}if(t=t.sibling,t!==null){Pe=t;return}Pe=t=e}while(t!==null);Me===0&&(Me=5)}function on(e,t,r){var n=ue,i=Nt.transition;try{Nt.transition=null,ue=1,Px(e,t,r,n)}finally{Nt.transition=i,ue=n}return null}function Px(e,t,r,n){do ei();while(Lr!==null);if(ie&6)throw Error(R(327));r=e.finishedWork;var i=e.finishedLanes;if(r===null)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(R(177));e.callbackNode=null,e.callbackPriority=0;var a=r.lanes|r.childLanes;if(u0(e,a),e===Ue&&(Pe=Ue=null,He=0),!(r.subtreeFlags&2064)&&!(r.flags&2064)||is||(is=!0,sg(Ms,function(){return ei(),null})),a=(r.flags&15990)!==0,r.subtreeFlags&15990||a){a=Nt.transition,Nt.transition=null;var s=ue;ue=1;var o=ie;ie|=4,Bu.current=null,Ex(e,r),Xh(r,e),G0(lc),zs=!!oc,lc=oc=null,e.current=r,jx(r),t0(),ie=o,ue=s,Nt.transition=a}else e.current=r;if(is&&(is=!1,Lr=e,eo=i),a=e.pendingLanes,a===0&&(Br=null),i0(r.stateNode),dt(e,Ne()),t!==null)for(n=e.onRecoverableError,r=0;r<t.length;r++)i=t[r],n(i.value,{componentStack:i.stack,digest:i.digest});if(Zs)throw Zs=!1,e=_c,_c=null,e;return eo&1&&e.tag!==0&&ei(),a=e.pendingLanes,a&1?e===Tc?ra++:(ra=0,Tc=e):ra=0,Xr(),null}function ei(){if(Lr!==null){var e=Fm(eo),t=Nt.transition,r=ue;try{if(Nt.transition=null,ue=16>e?16:e,Lr===null)var n=!1;else{if(e=Lr,Lr=null,eo=0,ie&6)throw Error(R(331));var i=ie;for(ie|=4,$=e.current;$!==null;){var a=$,s=a.child;if($.flags&16){var o=a.deletions;if(o!==null){for(var c=0;c<o.length;c++){var u=o[c];for($=u;$!==null;){var d=$;switch(d.tag){case 0:case 11:case 15:ea(8,d,a)}var f=d.child;if(f!==null)f.return=d,$=f;else for(;$!==null;){d=$;var h=d.sibling,w=d.return;if(Kh(d),d===u){$=null;break}if(h!==null){h.return=w,$=h;break}$=w}}}var g=a.alternate;if(g!==null){var y=g.child;if(y!==null){g.child=null;do{var x=y.sibling;y.sibling=null,y=x}while(y!==null)}}$=a}}if(a.subtreeFlags&2064&&s!==null)s.return=a,$=s;else e:for(;$!==null;){if(a=$,a.flags&2048)switch(a.tag){case 0:case 11:case 15:ea(9,a,a.return)}var m=a.sibling;if(m!==null){m.return=a.return,$=m;break e}$=a.return}}var p=e.current;for($=p;$!==null;){s=$;var v=s.child;if(s.subtreeFlags&2064&&v!==null)v.return=s,$=v;else e:for(s=p;$!==null;){if(o=$,o.flags&2048)try{switch(o.tag){case 0:case 11:case 15:Co(9,o)}}catch(_){Ee(o,o.return,_)}if(o===s){$=null;break e}var k=o.sibling;if(k!==null){k.return=o.return,$=k;break e}$=o.return}}if(ie=i,Xr(),Zt&&typeof Zt.onPostCommitFiberRoot=="function")try{Zt.onPostCommitFiberRoot(xo,e)}catch{}n=!0}return n}finally{ue=r,Nt.transition=t}}return!1}function Rf(e,t,r){t=li(r,t),t=Fh(e,t,1),e=$r(e,t,1),t=rt(),e!==null&&(Ca(e,1,t),dt(e,t))}function Ee(e,t,r){if(e.tag===3)Rf(e,e,r);else for(;t!==null;){if(t.tag===3){Rf(t,e,r);break}else if(t.tag===1){var n=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(Br===null||!Br.has(n))){e=li(r,e),e=zh(t,e,1),t=$r(t,e,1),e=rt(),t!==null&&(Ca(t,1,e),dt(t,e));break}}t=t.return}}function Ox(e,t,r){var n=e.pingCache;n!==null&&n.delete(t),t=rt(),e.pingedLanes|=e.suspendedLanes&r,Ue===e&&(He&r)===r&&(Me===4||Me===3&&(He&130023424)===He&&500>Ne()-Wu?gn(e,0):Vu|=r),dt(e,t)}function ig(e,t){t===0&&(e.mode&1?(t=Ka,Ka<<=1,!(Ka&130023424)&&(Ka=4194304)):t=1);var r=rt();e=hr(e,t),e!==null&&(Ca(e,t,r),dt(e,r))}function Rx(e){var t=e.memoizedState,r=0;t!==null&&(r=t.retryLane),ig(e,r)}function Ax(e,t){var r=0;switch(e.tag){case 13:var n=e.stateNode,i=e.memoizedState;i!==null&&(r=i.retryLane);break;case 19:n=e.stateNode;break;default:throw Error(R(314))}n!==null&&n.delete(t),ig(e,r)}var ag;ag=function(e,t,r){if(e!==null)if(e.memoizedProps!==t.pendingProps||ct.current)lt=!0;else{if(!(e.lanes&r)&&!(t.flags&128))return lt=!1,wx(e,t,r);lt=!!(e.flags&131072)}else lt=!1,xe&&t.flags&1048576&&ch(t,qs,t.index);switch(t.lanes=0,t.tag){case 2:var n=t.type;xs(e,t),e=t.pendingProps;var i=ii(t,Je.current);Zn(t,r),i=Fu(null,t,n,e,i,r);var a=zu();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ut(n)?(a=!0,Vs(t)):a=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Ru(t),i.updater=No,t.stateNode=i,i._reactInternals=t,vc(t,n,e,r),t=wc(null,t,n,!0,a,r)):(t.tag=0,xe&&a&&ju(t),Ze(null,t,i,r),t=t.child),t;case 16:n=t.elementType;e:{switch(xs(e,t),e=t.pendingProps,i=n._init,n=i(n._payload),t.type=n,i=t.tag=Lx(n),e=Rt(n,e),i){case 0:t=xc(null,t,n,e,r);break e;case 1:t=kf(null,t,n,e,r);break e;case 11:t=wf(null,t,n,e,r);break e;case 14:t=bf(null,t,n,Rt(n.type,e),r);break e}throw Error(R(306,n,""))}return t;case 0:return n=t.type,i=t.pendingProps,i=t.elementType===n?i:Rt(n,i),xc(e,t,n,i,r);case 1:return n=t.type,i=t.pendingProps,i=t.elementType===n?i:Rt(n,i),kf(e,t,n,i,r);case 3:e:{if(Bh(t),e===null)throw Error(R(387));n=t.pendingProps,a=t.memoizedState,i=a.element,hh(e,t),Ks(t,n,null,r);var s=t.memoizedState;if(n=s.element,a.isDehydrated)if(a={element:n,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=a,t.memoizedState=a,t.flags&256){i=li(Error(R(423)),t),t=Sf(e,t,n,r,i);break e}else if(n!==i){i=li(Error(R(424)),t),t=Sf(e,t,n,r,i);break e}else for(ht=Ur(t.stateNode.containerInfo.firstChild),gt=t,xe=!0,Lt=null,r=ph(t,null,n,r),t.child=r;r;)r.flags=r.flags&-3|4096,r=r.sibling;else{if(ai(),n===i){t=gr(e,t,r);break e}Ze(e,t,n,r)}t=t.child}return t;case 5:return gh(t),e===null&&mc(t),n=t.type,i=t.pendingProps,a=e!==null?e.memoizedProps:null,s=i.children,cc(n,i)?s=null:a!==null&&cc(n,a)&&(t.flags|=32),$h(e,t),Ze(e,t,s,r),t.child;case 6:return e===null&&mc(t),null;case 13:return Vh(e,t,r);case 4:return Au(t,t.stateNode.containerInfo),n=t.pendingProps,e===null?t.child=si(t,null,n,r):Ze(e,t,n,r),t.child;case 11:return n=t.type,i=t.pendingProps,i=t.elementType===n?i:Rt(n,i),wf(e,t,n,i,r);case 7:return Ze(e,t,t.pendingProps,r),t.child;case 8:return Ze(e,t,t.pendingProps.children,r),t.child;case 12:return Ze(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(n=t.type._context,i=t.pendingProps,a=t.memoizedProps,s=i.value,me(Hs,n._currentValue),n._currentValue=s,a!==null)if(Bt(a.value,s)){if(a.children===i.children&&!ct.current){t=gr(e,t,r);break e}}else for(a=t.child,a!==null&&(a.return=t);a!==null;){var o=a.dependencies;if(o!==null){s=a.child;for(var c=o.firstContext;c!==null;){if(c.context===n){if(a.tag===1){c=fr(-1,r&-r),c.tag=2;var u=a.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?c.next=c:(c.next=d.next,d.next=c),u.pending=c}}a.lanes|=r,c=a.alternate,c!==null&&(c.lanes|=r),hc(a.return,r,t),o.lanes|=r;break}c=c.next}}else if(a.tag===10)s=a.type===t.type?null:a.child;else if(a.tag===18){if(s=a.return,s===null)throw Error(R(341));s.lanes|=r,o=s.alternate,o!==null&&(o.lanes|=r),hc(s,r,t),s=a.sibling}else s=a.child;if(s!==null)s.return=a;else for(s=a;s!==null;){if(s===t){s=null;break}if(a=s.sibling,a!==null){a.return=s.return,s=a;break}s=s.return}a=s}Ze(e,t,i.children,r),t=t.child}return t;case 9:return i=t.type,n=t.pendingProps.children,Zn(t,r),i=Ct(i),n=n(i),t.flags|=1,Ze(e,t,n,r),t.child;case 14:return n=t.type,i=Rt(n,t.pendingProps),i=Rt(n.type,i),bf(e,t,n,i,r);case 15:return Ih(e,t,t.type,t.pendingProps,r);case 17:return n=t.type,i=t.pendingProps,i=t.elementType===n?i:Rt(n,i),xs(e,t),t.tag=1,ut(n)?(e=!0,Vs(t)):e=!1,Zn(t,r),Mh(t,n,i),vc(t,n,i,r),wc(null,t,n,!0,e,r);case 19:return Wh(e,t,r);case 22:return Uh(e,t,r)}throw Error(R(156,t.tag))};function sg(e,t){return Am(e,t)}function Dx(e,t,r,n){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function jt(e,t,r,n){return new Dx(e,t,r,n)}function Ku(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Lx(e){if(typeof e=="function")return Ku(e)?1:0;if(e!=null){if(e=e.$$typeof,e===pu)return 11;if(e===mu)return 14}return 2}function Wr(e,t){var r=e.alternate;return r===null?(r=jt(e.tag,t,e.key,e.mode),r.elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=e.flags&14680064,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function ks(e,t,r,n,i,a){var s=2;if(n=e,typeof e=="function")Ku(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case zn:return vn(r.children,i,a,t);case fu:s=8,i|=8;break;case $l:return e=jt(12,r,t,i|2),e.elementType=$l,e.lanes=a,e;case Bl:return e=jt(13,r,t,i),e.elementType=Bl,e.lanes=a,e;case Vl:return e=jt(19,r,t,i),e.elementType=Vl,e.lanes=a,e;case gm:return To(r,i,a,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case mm:s=10;break e;case hm:s=9;break e;case pu:s=11;break e;case mu:s=14;break e;case _r:s=16,n=null;break e}throw Error(R(130,e==null?e:typeof e,""))}return t=jt(s,r,t,i),t.elementType=e,t.type=n,t.lanes=a,t}function vn(e,t,r,n){return e=jt(7,e,n,t),e.lanes=r,e}function To(e,t,r,n){return e=jt(22,e,n,t),e.elementType=gm,e.lanes=r,e.stateNode={isHidden:!1},e}function El(e,t,r){return e=jt(6,e,null,t),e.lanes=r,e}function jl(e,t,r){return t=jt(4,e.children!==null?e.children:[],e.key,t),t.lanes=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Mx(e,t,r,n,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=al(0),this.expirationTimes=al(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=al(0),this.identifierPrefix=n,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Qu(e,t,r,n,i,a,s,o,c){return e=new Mx(e,t,r,o,c),t===1?(t=1,a===!0&&(t|=8)):t=0,a=jt(3,null,null,t),e.current=a,a.stateNode=e,a.memoizedState={element:n,isDehydrated:r,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ru(a),e}function Fx(e,t,r){var n=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Fn,key:n==null?null:""+n,children:e,containerInfo:t,implementation:r}}function og(e){if(!e)return Kr;e=e._reactInternals;e:{if(Nn(e)!==e||e.tag!==1)throw Error(R(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ut(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(R(171))}if(e.tag===1){var r=e.type;if(ut(r))return oh(e,r,t)}return t}function lg(e,t,r,n,i,a,s,o,c){return e=Qu(r,n,!0,e,i,a,s,o,c),e.context=og(null),r=e.current,n=rt(),i=Vr(r),a=fr(n,i),a.callback=t??null,$r(r,a,i),e.current.lanes=i,Ca(e,i,n),dt(e,n),e}function Po(e,t,r,n){var i=t.current,a=rt(),s=Vr(i);return r=og(r),t.context===null?t.context=r:t.pendingContext=r,t=fr(a,s),t.payload={element:e},n=n===void 0?null:n,n!==null&&(t.callback=n),e=$r(i,t,s),e!==null&&(Ut(e,i,s,a),gs(e,i,s)),s}function ro(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Af(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var r=e.retryLane;e.retryLane=r!==0&&r<t?r:t}}function Gu(e,t){Af(e,t),(e=e.alternate)&&Af(e,t)}function zx(){return null}var cg=typeof reportError=="function"?reportError:function(e){console.error(e)};function Xu(e){this._internalRoot=e}Oo.prototype.render=Xu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(R(409));Po(e,t,null,null)};Oo.prototype.unmount=Xu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;kn(function(){Po(null,e,null,null)}),t[mr]=null}};function Oo(e){this._internalRoot=e}Oo.prototype.unstable_scheduleHydration=function(e){if(e){var t=Um();e={blockedOn:null,target:e,priority:t};for(var r=0;r<Pr.length&&t!==0&&t<Pr[r].priority;r++);Pr.splice(r,0,e),r===0&&Bm(e)}};function Ju(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ro(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Df(){}function Ix(e,t,r,n,i){if(i){if(typeof n=="function"){var a=n;n=function(){var u=ro(s);a.call(u)}}var s=lg(t,n,e,0,null,!1,!1,"",Df);return e._reactRootContainer=s,e[mr]=s.current,pa(e.nodeType===8?e.parentNode:e),kn(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof n=="function"){var o=n;n=function(){var u=ro(c);o.call(u)}}var c=Qu(e,0,!1,null,null,!1,!1,"",Df);return e._reactRootContainer=c,e[mr]=c.current,pa(e.nodeType===8?e.parentNode:e),kn(function(){Po(t,c,r,n)}),c}function Ao(e,t,r,n,i){var a=r._reactRootContainer;if(a){var s=a;if(typeof i=="function"){var o=i;i=function(){var c=ro(s);o.call(c)}}Po(t,s,e,i)}else s=Ix(r,t,e,i,n);return ro(s)}zm=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var r=qi(t.pendingLanes);r!==0&&(vu(t,r|1),dt(t,Ne()),!(ie&6)&&(ci=Ne()+500,Xr()))}break;case 13:kn(function(){var n=hr(e,1);if(n!==null){var i=rt();Ut(n,e,1,i)}}),Gu(e,1)}};yu=function(e){if(e.tag===13){var t=hr(e,134217728);if(t!==null){var r=rt();Ut(t,e,134217728,r)}Gu(e,134217728)}};Im=function(e){if(e.tag===13){var t=Vr(e),r=hr(e,t);if(r!==null){var n=rt();Ut(r,e,t,n)}Gu(e,t)}};Um=function(){return ue};$m=function(e,t){var r=ue;try{return ue=e,t()}finally{ue=r}};Zl=function(e,t,r){switch(t){case"input":if(Hl(e,r),t=r.name,r.type==="radio"&&t!=null){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var n=r[t];if(n!==e&&n.form===e.form){var i=So(n);if(!i)throw Error(R(90));ym(n),Hl(n,i)}}}break;case"textarea":wm(e,r);break;case"select":t=r.value,t!=null&&Qn(e,!!r.multiple,t,!1)}};Cm=qu;_m=kn;var Ux={usingClientEntryPoint:!1,Events:[Ta,Bn,So,jm,Nm,qu]},Fi={findFiberByHostInstance:dn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},$x={bundleType:Fi.bundleType,version:Fi.version,rendererPackageName:Fi.rendererPackageName,rendererConfig:Fi.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:wr.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Om(e),e===null?null:e.stateNode},findFiberByHostInstance:Fi.findFiberByHostInstance||zx,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var as=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!as.isDisabled&&as.supportsFiber)try{xo=as.inject($x),Zt=as}catch{}}yt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Ux;yt.createPortal=function(e,t){var r=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ju(t))throw Error(R(200));return Fx(e,t,null,r)};yt.createRoot=function(e,t){if(!Ju(e))throw Error(R(299));var r=!1,n="",i=cg;return t!=null&&(t.unstable_strictMode===!0&&(r=!0),t.identifierPrefix!==void 0&&(n=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=Qu(e,1,!1,null,null,r,!1,n,i),e[mr]=t.current,pa(e.nodeType===8?e.parentNode:e),new Xu(t)};yt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(R(188)):(e=Object.keys(e).join(","),Error(R(268,e)));return e=Om(t),e=e===null?null:e.stateNode,e};yt.flushSync=function(e){return kn(e)};yt.hydrate=function(e,t,r){if(!Ro(t))throw Error(R(200));return Ao(null,e,t,!0,r)};yt.hydrateRoot=function(e,t,r){if(!Ju(e))throw Error(R(405));var n=r!=null&&r.hydratedSources||null,i=!1,a="",s=cg;if(r!=null&&(r.unstable_strictMode===!0&&(i=!0),r.identifierPrefix!==void 0&&(a=r.identifierPrefix),r.onRecoverableError!==void 0&&(s=r.onRecoverableError)),t=lg(t,null,e,1,r??null,i,!1,a,s),e[mr]=t.current,pa(e),n)for(e=0;e<n.length;e++)r=n[e],i=r._getVersion,i=i(r._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[r,i]:t.mutableSourceEagerHydrationData.push(r,i);return new Oo(t)};yt.render=function(e,t,r){if(!Ro(t))throw Error(R(200));return Ao(null,e,t,!1,r)};yt.unmountComponentAtNode=function(e){if(!Ro(e))throw Error(R(40));return e._reactRootContainer?(kn(function(){Ao(null,null,e,!1,function(){e._reactRootContainer=null,e[mr]=null})}),!0):!1};yt.unstable_batchedUpdates=qu;yt.unstable_renderSubtreeIntoContainer=function(e,t,r,n){if(!Ro(r))throw Error(R(200));if(e==null||e._reactInternals===void 0)throw Error(R(38));return Ao(e,t,r,!1,n)};yt.version="18.3.1-next-f1338f8080-20240426";function ug(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(ug)}catch(e){console.error(e)}}ug(),um.exports=yt;var Bx=um.exports,Lf=Bx;Il.createRoot=Lf.createRoot,Il.hydrateRoot=Lf.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ka(){return ka=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ka.apply(this,arguments)}var Mr;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Mr||(Mr={}));const Mf="popstate";function Vx(e){e===void 0&&(e={});function t(n,i){let{pathname:a,search:s,hash:o}=n.location;return Rc("",{pathname:a,search:s,hash:o},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function r(n,i){return typeof i=="string"?i:no(i)}return qx(t,r,null,e)}function Ce(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function dg(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Wx(){return Math.random().toString(36).substr(2,8)}function Ff(e,t){return{usr:e.state,key:e.key,idx:t}}function Rc(e,t,r,n){return r===void 0&&(r=null),ka({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?yi(t):t,{state:r,key:t&&t.key||n||Wx()})}function no(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&r!=="?"&&(t+=r.charAt(0)==="?"?r:"?"+r),n&&n!=="#"&&(t+=n.charAt(0)==="#"?n:"#"+n),t}function yi(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}function qx(e,t,r,n){n===void 0&&(n={});let{window:i=document.defaultView,v5Compat:a=!1}=n,s=i.history,o=Mr.Pop,c=null,u=d();u==null&&(u=0,s.replaceState(ka({},s.state,{idx:u}),""));function d(){return(s.state||{idx:null}).idx}function f(){o=Mr.Pop;let x=d(),m=x==null?null:x-u;u=x,c&&c({action:o,location:y.location,delta:m})}function h(x,m){o=Mr.Push;let p=Rc(y.location,x,m);r&&r(p,x),u=d()+1;let v=Ff(p,u),k=y.createHref(p);try{s.pushState(v,"",k)}catch(_){if(_ instanceof DOMException&&_.name==="DataCloneError")throw _;i.location.assign(k)}a&&c&&c({action:o,location:y.location,delta:1})}function w(x,m){o=Mr.Replace;let p=Rc(y.location,x,m);r&&r(p,x),u=d();let v=Ff(p,u),k=y.createHref(p);s.replaceState(v,"",k),a&&c&&c({action:o,location:y.location,delta:0})}function g(x){let m=i.location.origin!=="null"?i.location.origin:i.location.href,p=typeof x=="string"?x:no(x);return p=p.replace(/ $/,"%20"),Ce(m,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,m)}let y={get action(){return o},get location(){return e(i,s)},listen(x){if(c)throw new Error("A history only accepts one active listener");return i.addEventListener(Mf,f),c=x,()=>{i.removeEventListener(Mf,f),c=null}},createHref(x){return t(i,x)},createURL:g,encodeLocation(x){let m=g(x);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:h,replace:w,go(x){return s.go(x)}};return y}var zf;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(zf||(zf={}));function Hx(e,t,r){return r===void 0&&(r="/"),Yx(e,t,r,!1)}function Yx(e,t,r,n){let i=typeof t=="string"?yi(t):t,a=Zu(i.pathname||"/",r);if(a==null)return null;let s=fg(e);Kx(s);let o=null;for(let c=0;o==null&&c<s.length;++c){let u=aw(a);o=nw(s[c],u,n)}return o}function fg(e,t,r,n){t===void 0&&(t=[]),r===void 0&&(r=[]),n===void 0&&(n="");let i=(a,s,o)=>{let c={relativePath:o===void 0?a.path||"":o,caseSensitive:a.caseSensitive===!0,childrenIndex:s,route:a};c.relativePath.startsWith("/")&&(Ce(c.relativePath.startsWith(n),'Absolute route path "'+c.relativePath+'" nested under path '+('"'+n+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),c.relativePath=c.relativePath.slice(n.length));let u=qr([n,c.relativePath]),d=r.concat(c);a.children&&a.children.length>0&&(Ce(a.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),fg(a.children,t,d,u)),!(a.path==null&&!a.index)&&t.push({path:u,score:tw(u,a.index),routesMeta:d})};return e.forEach((a,s)=>{var o;if(a.path===""||!((o=a.path)!=null&&o.includes("?")))i(a,s);else for(let c of pg(a.path))i(a,s,c)}),t}function pg(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,i=r.endsWith("?"),a=r.replace(/\?$/,"");if(n.length===0)return i?[a,""]:[a];let s=pg(n.join("/")),o=[];return o.push(...s.map(c=>c===""?a:[a,c].join("/"))),i&&o.push(...s),o.map(c=>e.startsWith("/")&&c===""?"/":c)}function Kx(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:rw(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}const Qx=/^:[\w-]+$/,Gx=3,Xx=2,Jx=1,Zx=10,ew=-2,If=e=>e==="*";function tw(e,t){let r=e.split("/"),n=r.length;return r.some(If)&&(n+=ew),t&&(n+=Xx),r.filter(i=>!If(i)).reduce((i,a)=>i+(Qx.test(a)?Gx:a===""?Jx:Zx),n)}function rw(e,t){return e.length===t.length&&e.slice(0,-1).every((n,i)=>n===t[i])?e[e.length-1]-t[t.length-1]:0}function nw(e,t,r){r===void 0&&(r=!1);let{routesMeta:n}=e,i={},a="/",s=[];for(let o=0;o<n.length;++o){let c=n[o],u=o===n.length-1,d=a==="/"?t:t.slice(a.length)||"/",f=Uf({path:c.relativePath,caseSensitive:c.caseSensitive,end:u},d),h=c.route;if(!f&&u&&r&&!n[n.length-1].route.index&&(f=Uf({path:c.relativePath,caseSensitive:c.caseSensitive,end:!1},d)),!f)return null;Object.assign(i,f.params),s.push({params:i,pathname:qr([a,f.pathname]),pathnameBase:cw(qr([a,f.pathnameBase])),route:h}),f.pathnameBase!=="/"&&(a=qr([a,f.pathnameBase]))}return s}function Uf(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=iw(e.path,e.caseSensitive,e.end),i=t.match(r);if(!i)return null;let a=i[0],s=a.replace(/(.)\/+$/,"$1"),o=i.slice(1);return{params:n.reduce((u,d,f)=>{let{paramName:h,isOptional:w}=d;if(h==="*"){let y=o[f]||"";s=a.slice(0,a.length-y.length).replace(/(.)\/+$/,"$1")}const g=o[f];return w&&!g?u[h]=void 0:u[h]=(g||"").replace(/%2F/g,"/"),u},{}),pathname:a,pathnameBase:s,pattern:e}}function iw(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!0),dg(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let n=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,o,c)=>(n.push({paramName:o,isOptional:c!=null}),c?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),i+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?i+="\\/*$":e!==""&&e!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,t?void 0:"i"),n]}function aw(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return dg(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Zu(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function sw(e,t){t===void 0&&(t="/");let{pathname:r,search:n="",hash:i=""}=typeof e=="string"?yi(e):e;return{pathname:r?r.startsWith("/")?r:ow(r,t):t,search:uw(n),hash:dw(i)}}function ow(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(i=>{i===".."?r.length>1&&r.pop():i!=="."&&r.push(i)}),r.length>1?r.join("/"):"/"}function Nl(e,t,r,n){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(n)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function lw(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function ed(e,t){let r=lw(e);return t?r.map((n,i)=>i===r.length-1?n.pathname:n.pathnameBase):r.map(n=>n.pathnameBase)}function td(e,t,r,n){n===void 0&&(n=!1);let i;typeof e=="string"?i=yi(e):(i=ka({},e),Ce(!i.pathname||!i.pathname.includes("?"),Nl("?","pathname","search",i)),Ce(!i.pathname||!i.pathname.includes("#"),Nl("#","pathname","hash",i)),Ce(!i.search||!i.search.includes("#"),Nl("#","search","hash",i)));let a=e===""||i.pathname==="",s=a?"/":i.pathname,o;if(s==null)o=r;else{let f=t.length-1;if(!n&&s.startsWith("..")){let h=s.split("/");for(;h[0]==="..";)h.shift(),f-=1;i.pathname=h.join("/")}o=f>=0?t[f]:"/"}let c=sw(i,o),u=s&&s!=="/"&&s.endsWith("/"),d=(a||s===".")&&r.endsWith("/");return!c.pathname.endsWith("/")&&(u||d)&&(c.pathname+="/"),c}const qr=e=>e.join("/").replace(/\/\/+/g,"/"),cw=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),uw=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,dw=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function fw(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const mg=["post","put","patch","delete"];new Set(mg);const pw=["get",...mg];new Set(pw);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Sa(){return Sa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Sa.apply(this,arguments)}const rd=S.createContext(null),mw=S.createContext(null),Jr=S.createContext(null),Do=S.createContext(null),Zr=S.createContext({outlet:null,matches:[],isDataRoute:!1}),hg=S.createContext(null);function hw(e,t){let{relative:r}=t===void 0?{}:t;xi()||Ce(!1);let{basename:n,navigator:i}=S.useContext(Jr),{hash:a,pathname:s,search:o}=vg(e,{relative:r}),c=s;return n!=="/"&&(c=s==="/"?n:qr([n,s])),i.createHref({pathname:c,search:o,hash:a})}function xi(){return S.useContext(Do)!=null}function Oa(){return xi()||Ce(!1),S.useContext(Do).location}function gg(e){S.useContext(Jr).static||S.useLayoutEffect(e)}function Lo(){let{isDataRoute:e}=S.useContext(Zr);return e?_w():gw()}function gw(){xi()||Ce(!1);let e=S.useContext(rd),{basename:t,future:r,navigator:n}=S.useContext(Jr),{matches:i}=S.useContext(Zr),{pathname:a}=Oa(),s=JSON.stringify(ed(i,r.v7_relativeSplatPath)),o=S.useRef(!1);return gg(()=>{o.current=!0}),S.useCallback(function(u,d){if(d===void 0&&(d={}),!o.current)return;if(typeof u=="number"){n.go(u);return}let f=td(u,JSON.parse(s),a,d.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:qr([t,f.pathname])),(d.replace?n.replace:n.push)(f,d.state,d)},[t,n,s,a,e])}function vg(e,t){let{relative:r}=t===void 0?{}:t,{future:n}=S.useContext(Jr),{matches:i}=S.useContext(Zr),{pathname:a}=Oa(),s=JSON.stringify(ed(i,n.v7_relativeSplatPath));return S.useMemo(()=>td(e,JSON.parse(s),a,r==="path"),[e,s,a,r])}function vw(e,t){return yw(e,t)}function yw(e,t,r,n){xi()||Ce(!1);let{navigator:i}=S.useContext(Jr),{matches:a}=S.useContext(Zr),s=a[a.length-1],o=s?s.params:{};s&&s.pathname;let c=s?s.pathnameBase:"/";s&&s.route;let u=Oa(),d;if(t){var f;let x=typeof t=="string"?yi(t):t;c==="/"||(f=x.pathname)!=null&&f.startsWith(c)||Ce(!1),d=x}else d=u;let h=d.pathname||"/",w=h;if(c!=="/"){let x=c.replace(/^\//,"").split("/");w="/"+h.replace(/^\//,"").split("/").slice(x.length).join("/")}let g=Hx(e,{pathname:w}),y=Sw(g&&g.map(x=>Object.assign({},x,{params:Object.assign({},o,x.params),pathname:qr([c,i.encodeLocation?i.encodeLocation(x.pathname).pathname:x.pathname]),pathnameBase:x.pathnameBase==="/"?c:qr([c,i.encodeLocation?i.encodeLocation(x.pathnameBase).pathname:x.pathnameBase])})),a,r,n);return t&&y?S.createElement(Do.Provider,{value:{location:Sa({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:Mr.Pop}},y):y}function xw(){let e=Cw(),t=fw(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},a=null;return S.createElement(S.Fragment,null,S.createElement("h2",null,"Unexpected Application Error!"),S.createElement("h3",{style:{fontStyle:"italic"}},t),r?S.createElement("pre",{style:i},r):null,a)}const ww=S.createElement(xw,null);class bw extends S.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,r){return r.location!==t.location||r.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:r.error,location:r.location,revalidation:t.revalidation||r.revalidation}}componentDidCatch(t,r){console.error("React Router caught the following error during render",t,r)}render(){return this.state.error!==void 0?S.createElement(Zr.Provider,{value:this.props.routeContext},S.createElement(hg.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function kw(e){let{routeContext:t,match:r,children:n}=e,i=S.useContext(rd);return i&&i.static&&i.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=r.route.id),S.createElement(Zr.Provider,{value:t},n)}function Sw(e,t,r,n){var i;if(t===void 0&&(t=[]),r===void 0&&(r=null),n===void 0&&(n=null),e==null){var a;if(!r)return null;if(r.errors)e=r.matches;else if((a=n)!=null&&a.v7_partialHydration&&t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let s=e,o=(i=r)==null?void 0:i.errors;if(o!=null){let d=s.findIndex(f=>f.route.id&&(o==null?void 0:o[f.route.id])!==void 0);d>=0||Ce(!1),s=s.slice(0,Math.min(s.length,d+1))}let c=!1,u=-1;if(r&&n&&n.v7_partialHydration)for(let d=0;d<s.length;d++){let f=s[d];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(u=d),f.route.id){let{loaderData:h,errors:w}=r,g=f.route.loader&&h[f.route.id]===void 0&&(!w||w[f.route.id]===void 0);if(f.route.lazy||g){c=!0,u>=0?s=s.slice(0,u+1):s=[s[0]];break}}}return s.reduceRight((d,f,h)=>{let w,g=!1,y=null,x=null;r&&(w=o&&f.route.id?o[f.route.id]:void 0,y=f.route.errorElement||ww,c&&(u<0&&h===0?(Tw("route-fallback",!1),g=!0,x=null):u===h&&(g=!0,x=f.route.hydrateFallbackElement||null)));let m=t.concat(s.slice(0,h+1)),p=()=>{let v;return w?v=y:g?v=x:f.route.Component?v=S.createElement(f.route.Component,null):f.route.element?v=f.route.element:v=d,S.createElement(kw,{match:f,routeContext:{outlet:d,matches:m,isDataRoute:r!=null},children:v})};return r&&(f.route.ErrorBoundary||f.route.errorElement||h===0)?S.createElement(bw,{location:r.location,revalidation:r.revalidation,component:y,error:w,children:p(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):p()},null)}var yg=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(yg||{}),io=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(io||{});function Ew(e){let t=S.useContext(rd);return t||Ce(!1),t}function jw(e){let t=S.useContext(mw);return t||Ce(!1),t}function Nw(e){let t=S.useContext(Zr);return t||Ce(!1),t}function xg(e){let t=Nw(),r=t.matches[t.matches.length-1];return r.route.id||Ce(!1),r.route.id}function Cw(){var e;let t=S.useContext(hg),r=jw(io.UseRouteError),n=xg(io.UseRouteError);return t!==void 0?t:(e=r.errors)==null?void 0:e[n]}function _w(){let{router:e}=Ew(yg.UseNavigateStable),t=xg(io.UseNavigateStable),r=S.useRef(!1);return gg(()=>{r.current=!0}),S.useCallback(function(i,a){a===void 0&&(a={}),r.current&&(typeof i=="number"?e.navigate(i):e.navigate(i,Sa({fromRouteId:t},a)))},[e,t])}const $f={};function Tw(e,t,r){!t&&!$f[e]&&($f[e]=!0)}function Pw(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function An(e){let{to:t,replace:r,state:n,relative:i}=e;xi()||Ce(!1);let{future:a,static:s}=S.useContext(Jr),{matches:o}=S.useContext(Zr),{pathname:c}=Oa(),u=Lo(),d=td(t,ed(o,a.v7_relativeSplatPath),c,i==="path"),f=JSON.stringify(d);return S.useEffect(()=>u(JSON.parse(f),{replace:r,state:n,relative:i}),[u,f,i,r,n]),null}function ln(e){Ce(!1)}function Ow(e){let{basename:t="/",children:r=null,location:n,navigationType:i=Mr.Pop,navigator:a,static:s=!1,future:o}=e;xi()&&Ce(!1);let c=t.replace(/^\/*/,"/"),u=S.useMemo(()=>({basename:c,navigator:a,static:s,future:Sa({v7_relativeSplatPath:!1},o)}),[c,o,a,s]);typeof n=="string"&&(n=yi(n));let{pathname:d="/",search:f="",hash:h="",state:w=null,key:g="default"}=n,y=S.useMemo(()=>{let x=Zu(d,c);return x==null?null:{location:{pathname:x,search:f,hash:h,state:w,key:g},navigationType:i}},[c,d,f,h,w,g,i]);return y==null?null:S.createElement(Jr.Provider,{value:u},S.createElement(Do.Provider,{children:r,value:y}))}function Rw(e){let{children:t,location:r}=e;return vw(Ac(t),r)}new Promise(()=>{});function Ac(e,t){t===void 0&&(t=[]);let r=[];return S.Children.forEach(e,(n,i)=>{if(!S.isValidElement(n))return;let a=[...t,i];if(n.type===S.Fragment){r.push.apply(r,Ac(n.props.children,a));return}n.type!==ln&&Ce(!1),!n.props.index||!n.props.children||Ce(!1);let s={id:n.props.id||a.join("-"),caseSensitive:n.props.caseSensitive,element:n.props.element,Component:n.props.Component,index:n.props.index,path:n.props.path,loader:n.props.loader,action:n.props.action,errorElement:n.props.errorElement,ErrorBoundary:n.props.ErrorBoundary,hasErrorBoundary:n.props.ErrorBoundary!=null||n.props.errorElement!=null,shouldRevalidate:n.props.shouldRevalidate,handle:n.props.handle,lazy:n.props.lazy};n.props.children&&(s.children=Ac(n.props.children,a)),r.push(s)}),r}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Dc(){return Dc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Dc.apply(this,arguments)}function Aw(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function Dw(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Lw(e,t){return e.button===0&&(!t||t==="_self")&&!Dw(e)}const Mw=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],Fw="6";try{window.__reactRouterVersion=Fw}catch{}const zw="startTransition",Bf=Oy[zw];function Iw(e){let{basename:t,children:r,future:n,window:i}=e,a=S.useRef();a.current==null&&(a.current=Vx({window:i,v5Compat:!0}));let s=a.current,[o,c]=S.useState({action:s.action,location:s.location}),{v7_startTransition:u}=n||{},d=S.useCallback(f=>{u&&Bf?Bf(()=>c(f)):c(f)},[c,u]);return S.useLayoutEffect(()=>s.listen(d),[s,d]),S.useEffect(()=>Pw(n),[n]),S.createElement(Ow,{basename:t,children:r,location:o.location,navigationType:o.action,navigator:s,future:n})}const Uw=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",$w=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ui=S.forwardRef(function(t,r){let{onClick:n,relative:i,reloadDocument:a,replace:s,state:o,target:c,to:u,preventScrollReset:d,viewTransition:f}=t,h=Aw(t,Mw),{basename:w}=S.useContext(Jr),g,y=!1;if(typeof u=="string"&&$w.test(u)&&(g=u,Uw))try{let v=new URL(window.location.href),k=u.startsWith("//")?new URL(v.protocol+u):new URL(u),_=Zu(k.pathname,w);k.origin===v.origin&&_!=null?u=_+k.search+k.hash:y=!0}catch{}let x=hw(u,{relative:i}),m=Bw(u,{replace:s,state:o,target:c,preventScrollReset:d,relative:i,viewTransition:f});function p(v){n&&n(v),v.defaultPrevented||m(v)}return S.createElement("a",Dc({},h,{href:g||x,onClick:y||a?n:p,ref:r,target:c}))});var Vf;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Vf||(Vf={}));var Wf;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Wf||(Wf={}));function Bw(e,t){let{target:r,replace:n,state:i,preventScrollReset:a,relative:s,viewTransition:o}=t===void 0?{}:t,c=Lo(),u=Oa(),d=vg(e,{relative:s});return S.useCallback(f=>{if(Lw(f,r)){f.preventDefault();let h=n!==void 0?n:no(u)===no(d);c(e,{replace:h,state:i,preventScrollReset:a,relative:s,viewTransition:o})}},[u,c,d,n,i,r,e,a,s,o])}let Vw={data:""},Ww=e=>typeof window=="object"?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||Vw,qw=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,Hw=/\/\*[^]*?\*\/|  +/g,qf=/\n+/g,Rr=(e,t)=>{let r="",n="",i="";for(let a in e){let s=e[a];a[0]=="@"?a[1]=="i"?r=a+" "+s+";":n+=a[1]=="f"?Rr(s,a):a+"{"+Rr(s,a[1]=="k"?"":t)+"}":typeof s=="object"?n+=Rr(s,t?t.replace(/([^,])+/g,o=>a.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,c=>/&/.test(c)?c.replace(/&/g,o):o?o+" "+c:c)):a):s!=null&&(a=/^--/.test(a)?a:a.replace(/[A-Z]/g,"-$&").toLowerCase(),i+=Rr.p?Rr.p(a,s):a+":"+s+";")}return r+(t&&i?t+"{"+i+"}":i)+n},ar={},wg=e=>{if(typeof e=="object"){let t="";for(let r in e)t+=r+wg(e[r]);return t}return e},Yw=(e,t,r,n,i)=>{let a=wg(e),s=ar[a]||(ar[a]=(c=>{let u=0,d=11;for(;u<c.length;)d=101*d+c.charCodeAt(u++)>>>0;return"go"+d})(a));if(!ar[s]){let c=a!==e?e:(u=>{let d,f,h=[{}];for(;d=qw.exec(u.replace(Hw,""));)d[4]?h.shift():d[3]?(f=d[3].replace(qf," ").trim(),h.unshift(h[0][f]=h[0][f]||{})):h[0][d[1]]=d[2].replace(qf," ").trim();return h[0]})(e);ar[s]=Rr(i?{["@keyframes "+s]:c}:c,r?"":"."+s)}let o=r&&ar.g?ar.g:null;return r&&(ar.g=ar[s]),((c,u,d,f)=>{f?u.data=u.data.replace(f,c):u.data.indexOf(c)===-1&&(u.data=d?c+u.data:u.data+c)})(ar[s],t,n,o),s},Kw=(e,t,r)=>e.reduce((n,i,a)=>{let s=t[a];if(s&&s.call){let o=s(r),c=o&&o.props&&o.props.className||/^go/.test(o)&&o;s=c?"."+c:o&&typeof o=="object"?o.props?"":Rr(o,""):o===!1?"":o}return n+i+(s??"")},"");function Mo(e){let t=this||{},r=e.call?e(t.p):e;return Yw(r.unshift?r.raw?Kw(r,[].slice.call(arguments,1),t.p):r.reduce((n,i)=>Object.assign(n,i&&i.call?i(t.p):i),{}):r,Ww(t.target),t.g,t.o,t.k)}let bg,Lc,Mc;Mo.bind({g:1});let vr=Mo.bind({k:1});function Qw(e,t,r,n){Rr.p=t,bg=e,Lc=r,Mc=n}function en(e,t){let r=this||{};return function(){let n=arguments;function i(a,s){let o=Object.assign({},a),c=o.className||i.className;r.p=Object.assign({theme:Lc&&Lc()},o),r.o=/ *go\d+/.test(c),o.className=Mo.apply(r,n)+(c?" "+c:""),t&&(o.ref=s);let u=e;return e[0]&&(u=o.as||e,delete o.as),Mc&&u[0]&&Mc(o),bg(u,o)}return t?t(i):i}}var Gw=e=>typeof e=="function",ao=(e,t)=>Gw(e)?e(t):e,Xw=(()=>{let e=0;return()=>(++e).toString()})(),kg=(()=>{let e;return()=>{if(e===void 0&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),Jw=20,nd="default",Sg=(e,t)=>{let{toastLimit:r}=e.settings;switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,r)};case 1:return{...e,toasts:e.toasts.map(s=>s.id===t.toast.id?{...s,...t.toast}:s)};case 2:let{toast:n}=t;return Sg(e,{type:e.toasts.find(s=>s.id===n.id)?1:0,toast:n});case 3:let{toastId:i}=t;return{...e,toasts:e.toasts.map(s=>s.id===i||i===void 0?{...s,dismissed:!0,visible:!1}:s)};case 4:return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(s=>s.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let a=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(s=>({...s,pauseDuration:s.pauseDuration+a}))}}},Ss=[],Eg={toasts:[],pausedAt:void 0,settings:{toastLimit:Jw}},Jt={},jg=(e,t=nd)=>{Jt[t]=Sg(Jt[t]||Eg,e),Ss.forEach(([r,n])=>{r===t&&n(Jt[t])})},Ng=e=>Object.keys(Jt).forEach(t=>jg(e,t)),Zw=e=>Object.keys(Jt).find(t=>Jt[t].toasts.some(r=>r.id===e)),Fo=(e=nd)=>t=>{jg(t,e)},e1={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},t1=(e={},t=nd)=>{let[r,n]=S.useState(Jt[t]||Eg),i=S.useRef(Jt[t]);S.useEffect(()=>(i.current!==Jt[t]&&n(Jt[t]),Ss.push([t,n]),()=>{let s=Ss.findIndex(([o])=>o===t);s>-1&&Ss.splice(s,1)}),[t]);let a=r.toasts.map(s=>{var o,c,u;return{...e,...e[s.type],...s,removeDelay:s.removeDelay||((o=e[s.type])==null?void 0:o.removeDelay)||(e==null?void 0:e.removeDelay),duration:s.duration||((c=e[s.type])==null?void 0:c.duration)||(e==null?void 0:e.duration)||e1[s.type],style:{...e.style,...(u=e[s.type])==null?void 0:u.style,...s.style}}});return{...r,toasts:a}},r1=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(r==null?void 0:r.id)||Xw()}),Ra=e=>(t,r)=>{let n=r1(t,e,r);return Fo(n.toasterId||Zw(n.id))({type:2,toast:n}),n.id},Le=(e,t)=>Ra("blank")(e,t);Le.error=Ra("error");Le.success=Ra("success");Le.loading=Ra("loading");Le.custom=Ra("custom");Le.dismiss=(e,t)=>{let r={type:3,toastId:e};t?Fo(t)(r):Ng(r)};Le.dismissAll=e=>Le.dismiss(void 0,e);Le.remove=(e,t)=>{let r={type:4,toastId:e};t?Fo(t)(r):Ng(r)};Le.removeAll=e=>Le.remove(void 0,e);Le.promise=(e,t,r)=>{let n=Le.loading(t.loading,{...r,...r==null?void 0:r.loading});return typeof e=="function"&&(e=e()),e.then(i=>{let a=t.success?ao(t.success,i):void 0;return a?Le.success(a,{id:n,...r,...r==null?void 0:r.success}):Le.dismiss(n),i}).catch(i=>{let a=t.error?ao(t.error,i):void 0;a?Le.error(a,{id:n,...r,...r==null?void 0:r.error}):Le.dismiss(n)}),e};var n1=1e3,i1=(e,t="default")=>{let{toasts:r,pausedAt:n}=t1(e,t),i=S.useRef(new Map).current,a=S.useCallback((f,h=n1)=>{if(i.has(f))return;let w=setTimeout(()=>{i.delete(f),s({type:4,toastId:f})},h);i.set(f,w)},[]);S.useEffect(()=>{if(n)return;let f=Date.now(),h=r.map(w=>{if(w.duration===1/0)return;let g=(w.duration||0)+w.pauseDuration-(f-w.createdAt);if(g<0){w.visible&&Le.dismiss(w.id);return}return setTimeout(()=>Le.dismiss(w.id,t),g)});return()=>{h.forEach(w=>w&&clearTimeout(w))}},[r,n,t]);let s=S.useCallback(Fo(t),[t]),o=S.useCallback(()=>{s({type:5,time:Date.now()})},[s]),c=S.useCallback((f,h)=>{s({type:1,toast:{id:f,height:h}})},[s]),u=S.useCallback(()=>{n&&s({type:6,time:Date.now()})},[n,s]),d=S.useCallback((f,h)=>{let{reverseOrder:w=!1,gutter:g=8,defaultPosition:y}=h||{},x=r.filter(v=>(v.position||y)===(f.position||y)&&v.height),m=x.findIndex(v=>v.id===f.id),p=x.filter((v,k)=>k<m&&v.visible).length;return x.filter(v=>v.visible).slice(...w?[p+1]:[0,p]).reduce((v,k)=>v+(k.height||0)+g,0)},[r]);return S.useEffect(()=>{r.forEach(f=>{if(f.dismissed)a(f.id,f.removeDelay);else{let h=i.get(f.id);h&&(clearTimeout(h),i.delete(f.id))}})},[r,a]),{toasts:r,handlers:{updateHeight:c,startPause:o,endPause:u,calculateOffset:d}}},a1=vr`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,s1=vr`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,o1=vr`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,l1=en("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${a1} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${s1} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${o1} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,c1=vr`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,u1=en("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${c1} 1s linear infinite;
`,d1=vr`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,f1=vr`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,p1=en("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${d1} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${f1} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,m1=en("div")`
  position: absolute;
`,h1=en("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,g1=vr`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,v1=en("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${g1} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,y1=({toast:e})=>{let{icon:t,type:r,iconTheme:n}=e;return t!==void 0?typeof t=="string"?S.createElement(v1,null,t):t:r==="blank"?null:S.createElement(h1,null,S.createElement(u1,{...n}),r!=="loading"&&S.createElement(m1,null,r==="error"?S.createElement(l1,{...n}):S.createElement(p1,{...n})))},x1=e=>`
0% {transform: translate3d(0,${e*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,w1=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${e*-150}%,-1px) scale(.6); opacity:0;}
`,b1="0%{opacity:0;} 100%{opacity:1;}",k1="0%{opacity:1;} 100%{opacity:0;}",S1=en("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,E1=en("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,j1=(e,t)=>{let r=e.includes("top")?1:-1,[n,i]=kg()?[b1,k1]:[x1(r),w1(r)];return{animation:t?`${vr(n)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${vr(i)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},N1=S.memo(({toast:e,position:t,style:r,children:n})=>{let i=e.height?j1(e.position||t||"top-center",e.visible):{opacity:0},a=S.createElement(y1,{toast:e}),s=S.createElement(E1,{...e.ariaProps},ao(e.message,e));return S.createElement(S1,{className:e.className,style:{...i,...r,...e.style}},typeof n=="function"?n({icon:a,message:s}):S.createElement(S.Fragment,null,a,s))});Qw(S.createElement);var C1=({id:e,className:t,style:r,onHeightUpdate:n,children:i})=>{let a=S.useCallback(s=>{if(s){let o=()=>{let c=s.getBoundingClientRect().height;n(e,c)};o(),new MutationObserver(o).observe(s,{subtree:!0,childList:!0,characterData:!0})}},[e,n]);return S.createElement("div",{ref:a,className:t,style:r},i)},_1=(e,t)=>{let r=e.includes("top"),n=r?{top:0}:{bottom:0},i=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:kg()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...n,...i}},T1=Mo`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,ss=16,P1=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:n,children:i,toasterId:a,containerStyle:s,containerClassName:o})=>{let{toasts:c,handlers:u}=i1(r,a);return S.createElement("div",{"data-rht-toaster":a||"",style:{position:"fixed",zIndex:9999,top:ss,left:ss,right:ss,bottom:ss,pointerEvents:"none",...s},className:o,onMouseEnter:u.startPause,onMouseLeave:u.endPause},c.map(d=>{let f=d.position||t,h=u.calculateOffset(d,{reverseOrder:e,gutter:n,defaultPosition:t}),w=_1(f,h);return S.createElement(C1,{id:d.id,key:d.id,onHeightUpdate:u.updateHeight,className:d.visible?T1:"",style:w},d.type==="custom"?ao(d.message,d):i?i(d):S.createElement(N1,{toast:d,position:f}))}))},le=Le;const Hf=e=>{let t;const r=new Set,n=(d,f)=>{const h=typeof d=="function"?d(t):d;if(!Object.is(h,t)){const w=t;t=f??(typeof h!="object"||h===null)?h:Object.assign({},t,h),r.forEach(g=>g(t,w))}},i=()=>t,c={setState:n,getState:i,getInitialState:()=>u,subscribe:d=>(r.add(d),()=>r.delete(d)),destroy:()=>{r.clear()}},u=t=e(n,i,c);return c},O1=e=>e?Hf(e):Hf;var Cg={exports:{}},_g={},Tg={exports:{}},Pg={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var di=S;function R1(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var A1=typeof Object.is=="function"?Object.is:R1,D1=di.useState,L1=di.useEffect,M1=di.useLayoutEffect,F1=di.useDebugValue;function z1(e,t){var r=t(),n=D1({inst:{value:r,getSnapshot:t}}),i=n[0].inst,a=n[1];return M1(function(){i.value=r,i.getSnapshot=t,Cl(i)&&a({inst:i})},[e,r,t]),L1(function(){return Cl(i)&&a({inst:i}),e(function(){Cl(i)&&a({inst:i})})},[e]),F1(r),r}function Cl(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!A1(e,r)}catch{return!0}}function I1(e,t){return t()}var U1=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?I1:z1;Pg.useSyncExternalStore=di.useSyncExternalStore!==void 0?di.useSyncExternalStore:U1;Tg.exports=Pg;var $1=Tg.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zo=S,B1=$1;function V1(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var W1=typeof Object.is=="function"?Object.is:V1,q1=B1.useSyncExternalStore,H1=zo.useRef,Y1=zo.useEffect,K1=zo.useMemo,Q1=zo.useDebugValue;_g.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var a=H1(null);if(a.current===null){var s={hasValue:!1,value:null};a.current=s}else s=a.current;a=K1(function(){function c(w){if(!u){if(u=!0,d=w,w=n(w),i!==void 0&&s.hasValue){var g=s.value;if(i(g,w))return f=g}return f=w}if(g=f,W1(d,w))return g;var y=n(w);return i!==void 0&&i(g,y)?(d=w,g):(d=w,f=y)}var u=!1,d,f,h=r===void 0?null:r;return[function(){return c(t())},h===null?void 0:function(){return c(h())}]},[t,r,n,i]);var o=q1(e,a[0],a[1]);return Y1(function(){s.hasValue=!0,s.value=o},[o]),Q1(o),o};Cg.exports=_g;var G1=Cg.exports;const X1=iu(G1),{useDebugValue:J1}=Ve,{useSyncExternalStoreWithSelector:Z1}=X1;const eb=e=>e;function tb(e,t=eb,r){const n=Z1(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,r);return J1(n),n}const Yf=e=>{const t=typeof e=="function"?O1(e):e,r=(n,i)=>tb(t,n,i);return Object.assign(r,t),r},Og=e=>e?Yf(e):Yf;function rb(e,t){let r;try{r=e()}catch{return}return{getItem:i=>{var a;const s=c=>c===null?null:JSON.parse(c,t==null?void 0:t.reviver),o=(a=r.getItem(i))!=null?a:null;return o instanceof Promise?o.then(s):s(o)},setItem:(i,a)=>r.setItem(i,JSON.stringify(a,t==null?void 0:t.replacer)),removeItem:i=>r.removeItem(i)}}const Ea=e=>t=>{try{const r=e(t);return r instanceof Promise?r:{then(n){return Ea(n)(r)},catch(n){return this}}}catch(r){return{then(n){return this},catch(n){return Ea(n)(r)}}}},nb=(e,t)=>(r,n,i)=>{let a={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:x=>x,version:0,merge:(x,m)=>({...m,...x}),...t},s=!1;const o=new Set,c=new Set;let u;try{u=a.getStorage()}catch{}if(!u)return e((...x)=>{console.warn(`[zustand persist middleware] Unable to update item '${a.name}', the given storage is currently unavailable.`),r(...x)},n,i);const d=Ea(a.serialize),f=()=>{const x=a.partialize({...n()});let m;const p=d({state:x,version:a.version}).then(v=>u.setItem(a.name,v)).catch(v=>{m=v});if(m)throw m;return p},h=i.setState;i.setState=(x,m)=>{h(x,m),f()};const w=e((...x)=>{r(...x),f()},n,i);let g;const y=()=>{var x;if(!u)return;s=!1,o.forEach(p=>p(n()));const m=((x=a.onRehydrateStorage)==null?void 0:x.call(a,n()))||void 0;return Ea(u.getItem.bind(u))(a.name).then(p=>{if(p)return a.deserialize(p)}).then(p=>{if(p)if(typeof p.version=="number"&&p.version!==a.version){if(a.migrate)return a.migrate(p.state,p.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return p.state}).then(p=>{var v;return g=a.merge(p,(v=n())!=null?v:w),r(g,!0),f()}).then(()=>{m==null||m(g,void 0),s=!0,c.forEach(p=>p(g))}).catch(p=>{m==null||m(void 0,p)})};return i.persist={setOptions:x=>{a={...a,...x},x.getStorage&&(u=x.getStorage())},clearStorage:()=>{u==null||u.removeItem(a.name)},getOptions:()=>a,rehydrate:()=>y(),hasHydrated:()=>s,onHydrate:x=>(o.add(x),()=>{o.delete(x)}),onFinishHydration:x=>(c.add(x),()=>{c.delete(x)})},y(),g||w},ib=(e,t)=>(r,n,i)=>{let a={storage:rb(()=>localStorage),partialize:y=>y,version:0,merge:(y,x)=>({...x,...y}),...t},s=!1;const o=new Set,c=new Set;let u=a.storage;if(!u)return e((...y)=>{console.warn(`[zustand persist middleware] Unable to update item '${a.name}', the given storage is currently unavailable.`),r(...y)},n,i);const d=()=>{const y=a.partialize({...n()});return u.setItem(a.name,{state:y,version:a.version})},f=i.setState;i.setState=(y,x)=>{f(y,x),d()};const h=e((...y)=>{r(...y),d()},n,i);i.getInitialState=()=>h;let w;const g=()=>{var y,x;if(!u)return;s=!1,o.forEach(p=>{var v;return p((v=n())!=null?v:h)});const m=((x=a.onRehydrateStorage)==null?void 0:x.call(a,(y=n())!=null?y:h))||void 0;return Ea(u.getItem.bind(u))(a.name).then(p=>{if(p)if(typeof p.version=="number"&&p.version!==a.version){if(a.migrate)return[!0,a.migrate(p.state,p.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,p.state];return[!1,void 0]}).then(p=>{var v;const[k,_]=p;if(w=a.merge(_,(v=n())!=null?v:h),r(w,!0),k)return d()}).then(()=>{m==null||m(w,void 0),w=n(),s=!0,c.forEach(p=>p(w))}).catch(p=>{m==null||m(void 0,p)})};return i.persist={setOptions:y=>{a={...a,...y},y.storage&&(u=y.storage)},clearStorage:()=>{u==null||u.removeItem(a.name)},getOptions:()=>a,rehydrate:()=>g(),hasHydrated:()=>s,onHydrate:y=>(o.add(y),()=>{o.delete(y)}),onFinishHydration:y=>(c.add(y),()=>{c.delete(y)})},a.skipHydration||g(),w||h},ab=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?nb(e,t):ib(e,t),sb=ab;function Rg(e,t){return function(){return e.apply(t,arguments)}}const{toString:ob}=Object.prototype,{getPrototypeOf:id}=Object,{iterator:Io,toStringTag:Ag}=Symbol,Uo=(e=>t=>{const r=ob.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),Vt=e=>(e=e.toLowerCase(),t=>Uo(t)===e),$o=e=>t=>typeof t===e,{isArray:wi}=Array,fi=$o("undefined");function Aa(e){return e!==null&&!fi(e)&&e.constructor!==null&&!fi(e.constructor)&&ft(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Dg=Vt("ArrayBuffer");function lb(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Dg(e.buffer),t}const cb=$o("string"),ft=$o("function"),Lg=$o("number"),Da=e=>e!==null&&typeof e=="object",ub=e=>e===!0||e===!1,Es=e=>{if(Uo(e)!=="object")return!1;const t=id(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Ag in e)&&!(Io in e)},db=e=>{if(!Da(e)||Aa(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},fb=Vt("Date"),pb=Vt("File"),mb=Vt("Blob"),hb=Vt("FileList"),gb=e=>Da(e)&&ft(e.pipe),vb=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||ft(e.append)&&((t=Uo(e))==="formdata"||t==="object"&&ft(e.toString)&&e.toString()==="[object FormData]"))},yb=Vt("URLSearchParams"),[xb,wb,bb,kb]=["ReadableStream","Request","Response","Headers"].map(Vt),Sb=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function La(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let n,i;if(typeof e!="object"&&(e=[e]),wi(e))for(n=0,i=e.length;n<i;n++)t.call(null,e[n],n,e);else{if(Aa(e))return;const a=r?Object.getOwnPropertyNames(e):Object.keys(e),s=a.length;let o;for(n=0;n<s;n++)o=a[n],t.call(null,e[o],o,e)}}function Mg(e,t){if(Aa(e))return null;t=t.toLowerCase();const r=Object.keys(e);let n=r.length,i;for(;n-- >0;)if(i=r[n],t===i.toLowerCase())return i;return null}const mn=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),Fg=e=>!fi(e)&&e!==mn;function Fc(){const{caseless:e,skipUndefined:t}=Fg(this)&&this||{},r={},n=(i,a)=>{const s=e&&Mg(r,a)||a;Es(r[s])&&Es(i)?r[s]=Fc(r[s],i):Es(i)?r[s]=Fc({},i):wi(i)?r[s]=i.slice():(!t||!fi(i))&&(r[s]=i)};for(let i=0,a=arguments.length;i<a;i++)arguments[i]&&La(arguments[i],n);return r}const Eb=(e,t,r,{allOwnKeys:n}={})=>(La(t,(i,a)=>{r&&ft(i)?e[a]=Rg(i,r):e[a]=i},{allOwnKeys:n}),e),jb=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Nb=(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},Cb=(e,t,r,n)=>{let i,a,s;const o={};if(t=t||{},e==null)return t;do{for(i=Object.getOwnPropertyNames(e),a=i.length;a-- >0;)s=i[a],(!n||n(s,e,t))&&!o[s]&&(t[s]=e[s],o[s]=!0);e=r!==!1&&id(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},_b=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const n=e.indexOf(t,r);return n!==-1&&n===r},Tb=e=>{if(!e)return null;if(wi(e))return e;let t=e.length;if(!Lg(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},Pb=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&id(Uint8Array)),Ob=(e,t)=>{const n=(e&&e[Io]).call(e);let i;for(;(i=n.next())&&!i.done;){const a=i.value;t.call(e,a[0],a[1])}},Rb=(e,t)=>{let r;const n=[];for(;(r=e.exec(t))!==null;)n.push(r);return n},Ab=Vt("HTMLFormElement"),Db=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,i){return n.toUpperCase()+i}),Kf=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),Lb=Vt("RegExp"),zg=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),n={};La(r,(i,a)=>{let s;(s=t(i,a,e))!==!1&&(n[a]=s||i)}),Object.defineProperties(e,n)},Mb=e=>{zg(e,(t,r)=>{if(ft(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=e[r];if(ft(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},Fb=(e,t)=>{const r={},n=i=>{i.forEach(a=>{r[a]=!0})};return wi(e)?n(e):n(String(e).split(t)),r},zb=()=>{},Ib=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Ub(e){return!!(e&&ft(e.append)&&e[Ag]==="FormData"&&e[Io])}const $b=e=>{const t=new Array(10),r=(n,i)=>{if(Da(n)){if(t.indexOf(n)>=0)return;if(Aa(n))return n;if(!("toJSON"in n)){t[i]=n;const a=wi(n)?[]:{};return La(n,(s,o)=>{const c=r(s,i+1);!fi(c)&&(a[o]=c)}),t[i]=void 0,a}}return n};return r(e,0)},Bb=Vt("AsyncFunction"),Vb=e=>e&&(Da(e)||ft(e))&&ft(e.then)&&ft(e.catch),Ig=((e,t)=>e?setImmediate:t?((r,n)=>(mn.addEventListener("message",({source:i,data:a})=>{i===mn&&a===r&&n.length&&n.shift()()},!1),i=>{n.push(i),mn.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",ft(mn.postMessage)),Wb=typeof queueMicrotask<"u"?queueMicrotask.bind(mn):typeof process<"u"&&process.nextTick||Ig,qb=e=>e!=null&&ft(e[Io]),N={isArray:wi,isArrayBuffer:Dg,isBuffer:Aa,isFormData:vb,isArrayBufferView:lb,isString:cb,isNumber:Lg,isBoolean:ub,isObject:Da,isPlainObject:Es,isEmptyObject:db,isReadableStream:xb,isRequest:wb,isResponse:bb,isHeaders:kb,isUndefined:fi,isDate:fb,isFile:pb,isBlob:mb,isRegExp:Lb,isFunction:ft,isStream:gb,isURLSearchParams:yb,isTypedArray:Pb,isFileList:hb,forEach:La,merge:Fc,extend:Eb,trim:Sb,stripBOM:jb,inherits:Nb,toFlatObject:Cb,kindOf:Uo,kindOfTest:Vt,endsWith:_b,toArray:Tb,forEachEntry:Ob,matchAll:Rb,isHTMLForm:Ab,hasOwnProperty:Kf,hasOwnProp:Kf,reduceDescriptors:zg,freezeMethods:Mb,toObjectSet:Fb,toCamelCase:Db,noop:zb,toFiniteNumber:Ib,findKey:Mg,global:mn,isContextDefined:Fg,isSpecCompliantForm:Ub,toJSONObject:$b,isAsyncFn:Bb,isThenable:Vb,setImmediate:Ig,asap:Wb,isIterable:qb};function G(e,t,r,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),i&&(this.response=i,this.status=i.status?i.status:null)}N.inherits(G,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:N.toJSONObject(this.config),code:this.code,status:this.status}}});const Ug=G.prototype,$g={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{$g[e]={value:e}});Object.defineProperties(G,$g);Object.defineProperty(Ug,"isAxiosError",{value:!0});G.from=(e,t,r,n,i,a)=>{const s=Object.create(Ug);N.toFlatObject(e,s,function(d){return d!==Error.prototype},u=>u!=="isAxiosError");const o=e&&e.message?e.message:"Error",c=t==null&&e?e.code:t;return G.call(s,o,c,r,n,i),e&&s.cause==null&&Object.defineProperty(s,"cause",{value:e,configurable:!0}),s.name=e&&e.name||"Error",a&&Object.assign(s,a),s};const Hb=null;function zc(e){return N.isPlainObject(e)||N.isArray(e)}function Bg(e){return N.endsWith(e,"[]")?e.slice(0,-2):e}function Qf(e,t,r){return e?e.concat(t).map(function(i,a){return i=Bg(i),!r&&a?"["+i+"]":i}).join(r?".":""):t}function Yb(e){return N.isArray(e)&&!e.some(zc)}const Kb=N.toFlatObject(N,{},null,function(t){return/^is[A-Z]/.test(t)});function Bo(e,t,r){if(!N.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=N.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,x){return!N.isUndefined(x[y])});const n=r.metaTokens,i=r.visitor||d,a=r.dots,s=r.indexes,c=(r.Blob||typeof Blob<"u"&&Blob)&&N.isSpecCompliantForm(t);if(!N.isFunction(i))throw new TypeError("visitor must be a function");function u(g){if(g===null)return"";if(N.isDate(g))return g.toISOString();if(N.isBoolean(g))return g.toString();if(!c&&N.isBlob(g))throw new G("Blob is not supported. Use a Buffer instead.");return N.isArrayBuffer(g)||N.isTypedArray(g)?c&&typeof Blob=="function"?new Blob([g]):Buffer.from(g):g}function d(g,y,x){let m=g;if(g&&!x&&typeof g=="object"){if(N.endsWith(y,"{}"))y=n?y:y.slice(0,-2),g=JSON.stringify(g);else if(N.isArray(g)&&Yb(g)||(N.isFileList(g)||N.endsWith(y,"[]"))&&(m=N.toArray(g)))return y=Bg(y),m.forEach(function(v,k){!(N.isUndefined(v)||v===null)&&t.append(s===!0?Qf([y],k,a):s===null?y:y+"[]",u(v))}),!1}return zc(g)?!0:(t.append(Qf(x,y,a),u(g)),!1)}const f=[],h=Object.assign(Kb,{defaultVisitor:d,convertValue:u,isVisitable:zc});function w(g,y){if(!N.isUndefined(g)){if(f.indexOf(g)!==-1)throw Error("Circular reference detected in "+y.join("."));f.push(g),N.forEach(g,function(m,p){(!(N.isUndefined(m)||m===null)&&i.call(t,m,N.isString(p)?p.trim():p,y,h))===!0&&w(m,y?y.concat(p):[p])}),f.pop()}}if(!N.isObject(e))throw new TypeError("data must be an object");return w(e),t}function Gf(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function ad(e,t){this._pairs=[],e&&Bo(e,this,t)}const Vg=ad.prototype;Vg.append=function(t,r){this._pairs.push([t,r])};Vg.toString=function(t){const r=t?function(n){return t.call(this,n,Gf)}:Gf;return this._pairs.map(function(i){return r(i[0])+"="+r(i[1])},"").join("&")};function Qb(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+")}function Wg(e,t,r){if(!t)return e;const n=r&&r.encode||Qb;N.isFunction(r)&&(r={serialize:r});const i=r&&r.serialize;let a;if(i?a=i(t,r):a=N.isURLSearchParams(t)?t.toString():new ad(t,r).toString(n),a){const s=e.indexOf("#");s!==-1&&(e=e.slice(0,s)),e+=(e.indexOf("?")===-1?"?":"&")+a}return e}class Gb{constructor(){this.handlers=[]}use(t,r,n){return this.handlers.push({fulfilled:t,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){N.forEach(this.handlers,function(n){n!==null&&t(n)})}}const Xf=Gb,qg={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Xb=typeof URLSearchParams<"u"?URLSearchParams:ad,Jb=typeof FormData<"u"?FormData:null,Zb=typeof Blob<"u"?Blob:null,ek={isBrowser:!0,classes:{URLSearchParams:Xb,FormData:Jb,Blob:Zb},protocols:["http","https","file","blob","url","data"]},sd=typeof window<"u"&&typeof document<"u",Ic=typeof navigator=="object"&&navigator||void 0,tk=sd&&(!Ic||["ReactNative","NativeScript","NS"].indexOf(Ic.product)<0),rk=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),nk=sd&&window.location.href||"http://localhost",ik=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:sd,hasStandardBrowserEnv:tk,hasStandardBrowserWebWorkerEnv:rk,navigator:Ic,origin:nk},Symbol.toStringTag,{value:"Module"})),Xe={...ik,...ek};function ak(e,t){return Bo(e,new Xe.classes.URLSearchParams,{visitor:function(r,n,i,a){return Xe.isNode&&N.isBuffer(r)?(this.append(n,r.toString("base64")),!1):a.defaultVisitor.apply(this,arguments)},...t})}function sk(e){return N.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function ok(e){const t={},r=Object.keys(e);let n;const i=r.length;let a;for(n=0;n<i;n++)a=r[n],t[a]=e[a];return t}function Hg(e){function t(r,n,i,a){let s=r[a++];if(s==="__proto__")return!0;const o=Number.isFinite(+s),c=a>=r.length;return s=!s&&N.isArray(i)?i.length:s,c?(N.hasOwnProp(i,s)?i[s]=[i[s],n]:i[s]=n,!o):((!i[s]||!N.isObject(i[s]))&&(i[s]=[]),t(r,n,i[s],a)&&N.isArray(i[s])&&(i[s]=ok(i[s])),!o)}if(N.isFormData(e)&&N.isFunction(e.entries)){const r={};return N.forEachEntry(e,(n,i)=>{t(sk(n),i,r,0)}),r}return null}function lk(e,t,r){if(N.isString(e))try{return(t||JSON.parse)(e),N.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(e)}const od={transitional:qg,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const n=r.getContentType()||"",i=n.indexOf("application/json")>-1,a=N.isObject(t);if(a&&N.isHTMLForm(t)&&(t=new FormData(t)),N.isFormData(t))return i?JSON.stringify(Hg(t)):t;if(N.isArrayBuffer(t)||N.isBuffer(t)||N.isStream(t)||N.isFile(t)||N.isBlob(t)||N.isReadableStream(t))return t;if(N.isArrayBufferView(t))return t.buffer;if(N.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let o;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return ak(t,this.formSerializer).toString();if((o=N.isFileList(t))||n.indexOf("multipart/form-data")>-1){const c=this.env&&this.env.FormData;return Bo(o?{"files[]":t}:t,c&&new c,this.formSerializer)}}return a||i?(r.setContentType("application/json",!1),lk(t)):t}],transformResponse:[function(t){const r=this.transitional||od.transitional,n=r&&r.forcedJSONParsing,i=this.responseType==="json";if(N.isResponse(t)||N.isReadableStream(t))return t;if(t&&N.isString(t)&&(n&&!this.responseType||i)){const s=!(r&&r.silentJSONParsing)&&i;try{return JSON.parse(t,this.parseReviver)}catch(o){if(s)throw o.name==="SyntaxError"?G.from(o,G.ERR_BAD_RESPONSE,this,null,this.response):o}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Xe.classes.FormData,Blob:Xe.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};N.forEach(["delete","get","head","post","put","patch"],e=>{od.headers[e]={}});const ld=od,ck=N.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),uk=e=>{const t={};let r,n,i;return e&&e.split(`
`).forEach(function(s){i=s.indexOf(":"),r=s.substring(0,i).trim().toLowerCase(),n=s.substring(i+1).trim(),!(!r||t[r]&&ck[r])&&(r==="set-cookie"?t[r]?t[r].push(n):t[r]=[n]:t[r]=t[r]?t[r]+", "+n:n)}),t},Jf=Symbol("internals");function zi(e){return e&&String(e).trim().toLowerCase()}function js(e){return e===!1||e==null?e:N.isArray(e)?e.map(js):String(e)}function dk(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(e);)t[n[1]]=n[2];return t}const fk=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function _l(e,t,r,n,i){if(N.isFunction(n))return n.call(this,t,r);if(i&&(t=r),!!N.isString(t)){if(N.isString(n))return t.indexOf(n)!==-1;if(N.isRegExp(n))return n.test(t)}}function pk(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,n)=>r.toUpperCase()+n)}function mk(e,t){const r=N.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+r,{value:function(i,a,s){return this[n].call(this,t,i,a,s)},configurable:!0})})}class Vo{constructor(t){t&&this.set(t)}set(t,r,n){const i=this;function a(o,c,u){const d=zi(c);if(!d)throw new Error("header name must be a non-empty string");const f=N.findKey(i,d);(!f||i[f]===void 0||u===!0||u===void 0&&i[f]!==!1)&&(i[f||c]=js(o))}const s=(o,c)=>N.forEach(o,(u,d)=>a(u,d,c));if(N.isPlainObject(t)||t instanceof this.constructor)s(t,r);else if(N.isString(t)&&(t=t.trim())&&!fk(t))s(uk(t),r);else if(N.isObject(t)&&N.isIterable(t)){let o={},c,u;for(const d of t){if(!N.isArray(d))throw TypeError("Object iterator must return a key-value pair");o[u=d[0]]=(c=o[u])?N.isArray(c)?[...c,d[1]]:[c,d[1]]:d[1]}s(o,r)}else t!=null&&a(r,t,n);return this}get(t,r){if(t=zi(t),t){const n=N.findKey(this,t);if(n){const i=this[n];if(!r)return i;if(r===!0)return dk(i);if(N.isFunction(r))return r.call(this,i,n);if(N.isRegExp(r))return r.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=zi(t),t){const n=N.findKey(this,t);return!!(n&&this[n]!==void 0&&(!r||_l(this,this[n],n,r)))}return!1}delete(t,r){const n=this;let i=!1;function a(s){if(s=zi(s),s){const o=N.findKey(n,s);o&&(!r||_l(n,n[o],o,r))&&(delete n[o],i=!0)}}return N.isArray(t)?t.forEach(a):a(t),i}clear(t){const r=Object.keys(this);let n=r.length,i=!1;for(;n--;){const a=r[n];(!t||_l(this,this[a],a,t,!0))&&(delete this[a],i=!0)}return i}normalize(t){const r=this,n={};return N.forEach(this,(i,a)=>{const s=N.findKey(n,a);if(s){r[s]=js(i),delete r[a];return}const o=t?pk(a):String(a).trim();o!==a&&delete r[a],r[o]=js(i),n[o]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return N.forEach(this,(n,i)=>{n!=null&&n!==!1&&(r[i]=t&&N.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const n=new this(t);return r.forEach(i=>n.set(i)),n}static accessor(t){const n=(this[Jf]=this[Jf]={accessors:{}}).accessors,i=this.prototype;function a(s){const o=zi(s);n[o]||(mk(i,s),n[o]=!0)}return N.isArray(t)?t.forEach(a):a(t),this}}Vo.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);N.reduceDescriptors(Vo.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[r]=n}}});N.freezeMethods(Vo);const $t=Vo;function Tl(e,t){const r=this||ld,n=t||r,i=$t.from(n.headers);let a=n.data;return N.forEach(e,function(o){a=o.call(r,a,i.normalize(),t?t.status:void 0)}),i.normalize(),a}function Yg(e){return!!(e&&e.__CANCEL__)}function bi(e,t,r){G.call(this,e??"canceled",G.ERR_CANCELED,t,r),this.name="CanceledError"}N.inherits(bi,G,{__CANCEL__:!0});function Kg(e,t,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new G("Request failed with status code "+r.status,[G.ERR_BAD_REQUEST,G.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function hk(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function gk(e,t){e=e||10;const r=new Array(e),n=new Array(e);let i=0,a=0,s;return t=t!==void 0?t:1e3,function(c){const u=Date.now(),d=n[a];s||(s=u),r[i]=c,n[i]=u;let f=a,h=0;for(;f!==i;)h+=r[f++],f=f%e;if(i=(i+1)%e,i===a&&(a=(a+1)%e),u-s<t)return;const w=d&&u-d;return w?Math.round(h*1e3/w):void 0}}function vk(e,t){let r=0,n=1e3/t,i,a;const s=(u,d=Date.now())=>{r=d,i=null,a&&(clearTimeout(a),a=null),e(...u)};return[(...u)=>{const d=Date.now(),f=d-r;f>=n?s(u,d):(i=u,a||(a=setTimeout(()=>{a=null,s(i)},n-f)))},()=>i&&s(i)]}const so=(e,t,r=3)=>{let n=0;const i=gk(50,250);return vk(a=>{const s=a.loaded,o=a.lengthComputable?a.total:void 0,c=s-n,u=i(c),d=s<=o;n=s;const f={loaded:s,total:o,progress:o?s/o:void 0,bytes:c,rate:u||void 0,estimated:u&&o&&d?(o-s)/u:void 0,event:a,lengthComputable:o!=null,[t?"download":"upload"]:!0};e(f)},r)},Zf=(e,t)=>{const r=e!=null;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},ep=e=>(...t)=>N.asap(()=>e(...t)),yk=Xe.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,Xe.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(Xe.origin),Xe.navigator&&/(msie|trident)/i.test(Xe.navigator.userAgent)):()=>!0,xk=Xe.hasStandardBrowserEnv?{write(e,t,r,n,i,a){const s=[e+"="+encodeURIComponent(t)];N.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),N.isString(n)&&s.push("path="+n),N.isString(i)&&s.push("domain="+i),a===!0&&s.push("secure"),document.cookie=s.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function wk(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function bk(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Qg(e,t,r){let n=!wk(t);return e&&(n||r==!1)?bk(e,t):t}const tp=e=>e instanceof $t?{...e}:e;function Sn(e,t){t=t||{};const r={};function n(u,d,f,h){return N.isPlainObject(u)&&N.isPlainObject(d)?N.merge.call({caseless:h},u,d):N.isPlainObject(d)?N.merge({},d):N.isArray(d)?d.slice():d}function i(u,d,f,h){if(N.isUndefined(d)){if(!N.isUndefined(u))return n(void 0,u,f,h)}else return n(u,d,f,h)}function a(u,d){if(!N.isUndefined(d))return n(void 0,d)}function s(u,d){if(N.isUndefined(d)){if(!N.isUndefined(u))return n(void 0,u)}else return n(void 0,d)}function o(u,d,f){if(f in t)return n(u,d);if(f in e)return n(void 0,u)}const c={url:a,method:a,data:a,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:o,headers:(u,d,f)=>i(tp(u),tp(d),f,!0)};return N.forEach(Object.keys({...e,...t}),function(d){const f=c[d]||i,h=f(e[d],t[d],d);N.isUndefined(h)&&f!==o||(r[d]=h)}),r}const Gg=e=>{const t=Sn({},e);let{data:r,withXSRFToken:n,xsrfHeaderName:i,xsrfCookieName:a,headers:s,auth:o}=t;if(t.headers=s=$t.from(s),t.url=Wg(Qg(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),o&&s.set("Authorization","Basic "+btoa((o.username||"")+":"+(o.password?unescape(encodeURIComponent(o.password)):""))),N.isFormData(r)){if(Xe.hasStandardBrowserEnv||Xe.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if(N.isFunction(r.getHeaders)){const c=r.getHeaders(),u=["content-type","content-length"];Object.entries(c).forEach(([d,f])=>{u.includes(d.toLowerCase())&&s.set(d,f)})}}if(Xe.hasStandardBrowserEnv&&(n&&N.isFunction(n)&&(n=n(t)),n||n!==!1&&yk(t.url))){const c=i&&a&&xk.read(a);c&&s.set(i,c)}return t},kk=typeof XMLHttpRequest<"u",Sk=kk&&function(e){return new Promise(function(r,n){const i=Gg(e);let a=i.data;const s=$t.from(i.headers).normalize();let{responseType:o,onUploadProgress:c,onDownloadProgress:u}=i,d,f,h,w,g;function y(){w&&w(),g&&g(),i.cancelToken&&i.cancelToken.unsubscribe(d),i.signal&&i.signal.removeEventListener("abort",d)}let x=new XMLHttpRequest;x.open(i.method.toUpperCase(),i.url,!0),x.timeout=i.timeout;function m(){if(!x)return;const v=$t.from("getAllResponseHeaders"in x&&x.getAllResponseHeaders()),_={data:!o||o==="text"||o==="json"?x.responseText:x.response,status:x.status,statusText:x.statusText,headers:v,config:e,request:x};Kg(function(P){r(P),y()},function(P){n(P),y()},_),x=null}"onloadend"in x?x.onloadend=m:x.onreadystatechange=function(){!x||x.readyState!==4||x.status===0&&!(x.responseURL&&x.responseURL.indexOf("file:")===0)||setTimeout(m)},x.onabort=function(){x&&(n(new G("Request aborted",G.ECONNABORTED,e,x)),x=null)},x.onerror=function(k){const _=k&&k.message?k.message:"Network Error",A=new G(_,G.ERR_NETWORK,e,x);A.event=k||null,n(A),x=null},x.ontimeout=function(){let k=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const _=i.transitional||qg;i.timeoutErrorMessage&&(k=i.timeoutErrorMessage),n(new G(k,_.clarifyTimeoutError?G.ETIMEDOUT:G.ECONNABORTED,e,x)),x=null},a===void 0&&s.setContentType(null),"setRequestHeader"in x&&N.forEach(s.toJSON(),function(k,_){x.setRequestHeader(_,k)}),N.isUndefined(i.withCredentials)||(x.withCredentials=!!i.withCredentials),o&&o!=="json"&&(x.responseType=i.responseType),u&&([h,g]=so(u,!0),x.addEventListener("progress",h)),c&&x.upload&&([f,w]=so(c),x.upload.addEventListener("progress",f),x.upload.addEventListener("loadend",w)),(i.cancelToken||i.signal)&&(d=v=>{x&&(n(!v||v.type?new bi(null,e,x):v),x.abort(),x=null)},i.cancelToken&&i.cancelToken.subscribe(d),i.signal&&(i.signal.aborted?d():i.signal.addEventListener("abort",d)));const p=hk(i.url);if(p&&Xe.protocols.indexOf(p)===-1){n(new G("Unsupported protocol "+p+":",G.ERR_BAD_REQUEST,e));return}x.send(a||null)})},Ek=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let n=new AbortController,i;const a=function(u){if(!i){i=!0,o();const d=u instanceof Error?u:this.reason;n.abort(d instanceof G?d:new bi(d instanceof Error?d.message:d))}};let s=t&&setTimeout(()=>{s=null,a(new G(`timeout ${t} of ms exceeded`,G.ETIMEDOUT))},t);const o=()=>{e&&(s&&clearTimeout(s),s=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(a):u.removeEventListener("abort",a)}),e=null)};e.forEach(u=>u.addEventListener("abort",a));const{signal:c}=n;return c.unsubscribe=()=>N.asap(o),c}},jk=Ek,Nk=function*(e,t){let r=e.byteLength;if(!t||r<t){yield e;return}let n=0,i;for(;n<r;)i=n+t,yield e.slice(n,i),n=i},Ck=async function*(e,t){for await(const r of _k(e))yield*Nk(r,t)},_k=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:r,value:n}=await t.read();if(r)break;yield n}}finally{await t.cancel()}},rp=(e,t,r,n)=>{const i=Ck(e,t);let a=0,s,o=c=>{s||(s=!0,n&&n(c))};return new ReadableStream({async pull(c){try{const{done:u,value:d}=await i.next();if(u){o(),c.close();return}let f=d.byteLength;if(r){let h=a+=f;r(h)}c.enqueue(new Uint8Array(d))}catch(u){throw o(u),u}},cancel(c){return o(c),i.return()}},{highWaterMark:2})},np=64*1024,{isFunction:os}=N,Tk=(({Request:e,Response:t})=>({Request:e,Response:t}))(N.global),{ReadableStream:ip,TextEncoder:ap}=N.global,sp=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Pk=e=>{e=N.merge.call({skipUndefined:!0},Tk,e);const{fetch:t,Request:r,Response:n}=e,i=t?os(t):typeof fetch=="function",a=os(r),s=os(n);if(!i)return!1;const o=i&&os(ip),c=i&&(typeof ap=="function"?(g=>y=>g.encode(y))(new ap):async g=>new Uint8Array(await new r(g).arrayBuffer())),u=a&&o&&sp(()=>{let g=!1;const y=new r(Xe.origin,{body:new ip,method:"POST",get duplex(){return g=!0,"half"}}).headers.has("Content-Type");return g&&!y}),d=s&&o&&sp(()=>N.isReadableStream(new n("").body)),f={stream:d&&(g=>g.body)};i&&["text","arrayBuffer","blob","formData","stream"].forEach(g=>{!f[g]&&(f[g]=(y,x)=>{let m=y&&y[g];if(m)return m.call(y);throw new G(`Response type '${g}' is not supported`,G.ERR_NOT_SUPPORT,x)})});const h=async g=>{if(g==null)return 0;if(N.isBlob(g))return g.size;if(N.isSpecCompliantForm(g))return(await new r(Xe.origin,{method:"POST",body:g}).arrayBuffer()).byteLength;if(N.isArrayBufferView(g)||N.isArrayBuffer(g))return g.byteLength;if(N.isURLSearchParams(g)&&(g=g+""),N.isString(g))return(await c(g)).byteLength},w=async(g,y)=>{const x=N.toFiniteNumber(g.getContentLength());return x??h(y)};return async g=>{let{url:y,method:x,data:m,signal:p,cancelToken:v,timeout:k,onDownloadProgress:_,onUploadProgress:A,responseType:P,headers:O,withCredentials:L="same-origin",fetchOptions:U}=Gg(g),Z=t||fetch;P=P?(P+"").toLowerCase():"text";let M=jk([p,v&&v.toAbortSignal()],k),W=null;const V=M&&M.unsubscribe&&(()=>{M.unsubscribe()});let q;try{if(A&&u&&x!=="get"&&x!=="head"&&(q=await w(O,m))!==0){let K=new r(y,{method:"POST",body:m,duplex:"half"}),ae;if(N.isFormData(m)&&(ae=K.headers.get("content-type"))&&O.setContentType(ae),K.body){const[at,je]=Zf(q,so(ep(A)));m=rp(K.body,np,at,je)}}N.isString(L)||(L=L?"include":"omit");const Q=a&&"credentials"in r.prototype,de={...U,signal:M,method:x.toUpperCase(),headers:O.normalize().toJSON(),body:m,duplex:"half",credentials:Q?L:void 0};W=a&&new r(y,de);let D=await(a?Z(W,U):Z(y,de));const z=d&&(P==="stream"||P==="response");if(d&&(_||z&&V)){const K={};["status","statusText","headers"].forEach(wt=>{K[wt]=D[wt]});const ae=N.toFiniteNumber(D.headers.get("content-length")),[at,je]=_&&Zf(ae,so(ep(_),!0))||[];D=new n(rp(D.body,np,at,()=>{je&&je(),V&&V()}),K)}P=P||"text";let H=await f[N.findKey(f,P)||"text"](D,g);return!z&&V&&V(),await new Promise((K,ae)=>{Kg(K,ae,{data:H,headers:$t.from(D.headers),status:D.status,statusText:D.statusText,config:g,request:W})})}catch(Q){throw V&&V(),Q&&Q.name==="TypeError"&&/Load failed|fetch/i.test(Q.message)?Object.assign(new G("Network Error",G.ERR_NETWORK,g,W),{cause:Q.cause||Q}):G.from(Q,Q&&Q.code,g,W)}}},Ok=new Map,Xg=e=>{let t=e?e.env:{};const{fetch:r,Request:n,Response:i}=t,a=[n,i,r];let s=a.length,o=s,c,u,d=Ok;for(;o--;)c=a[o],u=d.get(c),u===void 0&&d.set(c,u=o?new Map:Pk(t)),d=u;return u};Xg();const Uc={http:Hb,xhr:Sk,fetch:{get:Xg}};N.forEach(Uc,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const op=e=>`- ${e}`,Rk=e=>N.isFunction(e)||e===null||e===!1,Jg={getAdapter:(e,t)=>{e=N.isArray(e)?e:[e];const{length:r}=e;let n,i;const a={};for(let s=0;s<r;s++){n=e[s];let o;if(i=n,!Rk(n)&&(i=Uc[(o=String(n)).toLowerCase()],i===void 0))throw new G(`Unknown adapter '${o}'`);if(i&&(N.isFunction(i)||(i=i.get(t))))break;a[o||"#"+s]=i}if(!i){const s=Object.entries(a).map(([c,u])=>`adapter ${c} `+(u===!1?"is not supported by the environment":"is not available in the build"));let o=r?s.length>1?`since :
`+s.map(op).join(`
`):" "+op(s[0]):"as no adapter specified";throw new G("There is no suitable adapter to dispatch the request "+o,"ERR_NOT_SUPPORT")}return i},adapters:Uc};function Pl(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new bi(null,e)}function lp(e){return Pl(e),e.headers=$t.from(e.headers),e.data=Tl.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Jg.getAdapter(e.adapter||ld.adapter,e)(e).then(function(n){return Pl(e),n.data=Tl.call(e,e.transformResponse,n),n.headers=$t.from(n.headers),n},function(n){return Yg(n)||(Pl(e),n&&n.response&&(n.response.data=Tl.call(e,e.transformResponse,n.response),n.response.headers=$t.from(n.response.headers))),Promise.reject(n)})}const Zg="1.12.2",Wo={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Wo[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const cp={};Wo.transitional=function(t,r,n){function i(a,s){return"[Axios v"+Zg+"] Transitional option '"+a+"'"+s+(n?". "+n:"")}return(a,s,o)=>{if(t===!1)throw new G(i(s," has been removed"+(r?" in "+r:"")),G.ERR_DEPRECATED);return r&&!cp[s]&&(cp[s]=!0,console.warn(i(s," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(a,s,o):!0}};Wo.spelling=function(t){return(r,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};function Ak(e,t,r){if(typeof e!="object")throw new G("options must be an object",G.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let i=n.length;for(;i-- >0;){const a=n[i],s=t[a];if(s){const o=e[a],c=o===void 0||s(o,a,e);if(c!==!0)throw new G("option "+a+" must be "+c,G.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new G("Unknown option "+a,G.ERR_BAD_OPTION)}}const Ns={assertOptions:Ak,validators:Wo},qt=Ns.validators;class oo{constructor(t){this.defaults=t||{},this.interceptors={request:new Xf,response:new Xf}}async request(t,r){try{return await this._request(t,r)}catch(n){if(n instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const a=i.stack?i.stack.replace(/^.+\n/,""):"";try{n.stack?a&&!String(n.stack).endsWith(a.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+a):n.stack=a}catch{}}throw n}}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=Sn(this.defaults,r);const{transitional:n,paramsSerializer:i,headers:a}=r;n!==void 0&&Ns.assertOptions(n,{silentJSONParsing:qt.transitional(qt.boolean),forcedJSONParsing:qt.transitional(qt.boolean),clarifyTimeoutError:qt.transitional(qt.boolean)},!1),i!=null&&(N.isFunction(i)?r.paramsSerializer={serialize:i}:Ns.assertOptions(i,{encode:qt.function,serialize:qt.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),Ns.assertOptions(r,{baseUrl:qt.spelling("baseURL"),withXsrfToken:qt.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let s=a&&N.merge(a.common,a[r.method]);a&&N.forEach(["delete","get","head","post","put","patch","common"],g=>{delete a[g]}),r.headers=$t.concat(s,a);const o=[];let c=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(r)===!1||(c=c&&y.synchronous,o.unshift(y.fulfilled,y.rejected))});const u=[];this.interceptors.response.forEach(function(y){u.push(y.fulfilled,y.rejected)});let d,f=0,h;if(!c){const g=[lp.bind(this),void 0];for(g.unshift(...o),g.push(...u),h=g.length,d=Promise.resolve(r);f<h;)d=d.then(g[f++],g[f++]);return d}h=o.length;let w=r;for(;f<h;){const g=o[f++],y=o[f++];try{w=g(w)}catch(x){y.call(this,x);break}}try{d=lp.call(this,w)}catch(g){return Promise.reject(g)}for(f=0,h=u.length;f<h;)d=d.then(u[f++],u[f++]);return d}getUri(t){t=Sn(this.defaults,t);const r=Qg(t.baseURL,t.url,t.allowAbsoluteUrls);return Wg(r,t.params,t.paramsSerializer)}}N.forEach(["delete","get","head","options"],function(t){oo.prototype[t]=function(r,n){return this.request(Sn(n||{},{method:t,url:r,data:(n||{}).data}))}});N.forEach(["post","put","patch"],function(t){function r(n){return function(a,s,o){return this.request(Sn(o||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:a,data:s}))}}oo.prototype[t]=r(),oo.prototype[t+"Form"]=r(!0)});const Cs=oo;class cd{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(a){r=a});const n=this;this.promise.then(i=>{if(!n._listeners)return;let a=n._listeners.length;for(;a-- >0;)n._listeners[a](i);n._listeners=null}),this.promise.then=i=>{let a;const s=new Promise(o=>{n.subscribe(o),a=o}).then(i);return s.cancel=function(){n.unsubscribe(a)},s},t(function(a,s,o){n.reason||(n.reason=new bi(a,s,o),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const t=new AbortController,r=n=>{t.abort(n)};return this.subscribe(r),t.signal.unsubscribe=()=>this.unsubscribe(r),t.signal}static source(){let t;return{token:new cd(function(i){t=i}),cancel:t}}}const Dk=cd;function Lk(e){return function(r){return e.apply(null,r)}}function Mk(e){return N.isObject(e)&&e.isAxiosError===!0}const $c={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries($c).forEach(([e,t])=>{$c[t]=e});const Fk=$c;function ev(e){const t=new Cs(e),r=Rg(Cs.prototype.request,t);return N.extend(r,Cs.prototype,t,{allOwnKeys:!0}),N.extend(r,t,null,{allOwnKeys:!0}),r.create=function(i){return ev(Sn(e,i))},r}const Ae=ev(ld);Ae.Axios=Cs;Ae.CanceledError=bi;Ae.CancelToken=Dk;Ae.isCancel=Yg;Ae.VERSION=Zg;Ae.toFormData=Bo;Ae.AxiosError=G;Ae.Cancel=Ae.CanceledError;Ae.all=function(t){return Promise.all(t)};Ae.spread=Lk;Ae.isAxiosError=Mk;Ae.mergeConfig=Sn;Ae.AxiosHeaders=$t;Ae.formToJSON=e=>Hg(N.isHTMLForm(e)?new FormData(e):e);Ae.getAdapter=Jg.getAdapter;Ae.HttpStatusCode=Fk;Ae.default=Ae;const up=Ae;class zk{constructor(){an(this,"client");an(this,"refreshToken",null);this.client=up.create({baseURL:"/api",timeout:1e4,headers:{"Content-Type":"application/json"}}),this.loadTokens(),this.client.interceptors.request.use(t=>{const r=localStorage.getItem("accessToken");return r&&(t.headers.Authorization=`Bearer ${r}`),t},t=>Promise.reject(t)),this.client.interceptors.response.use(t=>t,async t=>{var n;const r=t.config;if(((n=t.response)==null?void 0:n.status)===401&&!r._retry){r._retry=!0;try{await this.refreshAccessToken();const i=localStorage.getItem("accessToken");if(i)return r.headers.Authorization=`Bearer ${i}`,this.client(r)}catch(i){return this.logout(),window.location.href="/login",Promise.reject(i)}}return Promise.reject(t)})}loadTokens(){this.refreshToken=localStorage.getItem("refreshToken")}async refreshAccessToken(){if(!this.refreshToken)throw new Error("No refresh token available");const t=await up.post("/api/auth/refresh",{refreshToken:this.refreshToken});if(t.data.success&&t.data.data)localStorage.setItem("accessToken",t.data.data.accessToken),localStorage.setItem("refreshToken",t.data.data.refreshToken),this.refreshToken=t.data.data.refreshToken;else throw new Error("Failed to refresh token")}handleError(t){var n,i;const r=((i=(n=t.response)==null?void 0:n.data)==null?void 0:i.message)||t.message||"An error occurred";return le.error(r),Promise.reject(t)}async login(t){try{const r=await this.client.post("/auth/login",t);if(r.data.success&&r.data.data)return localStorage.setItem("accessToken",r.data.data.accessToken),localStorage.setItem("refreshToken",r.data.data.refreshToken),this.refreshToken=r.data.data.refreshToken,r.data.data;throw new Error(r.data.message||"Login failed")}catch(r){return this.handleError(r)}}async register(t){try{const r=await this.client.post("/auth/register",t);if(r.data.success&&r.data.data)return localStorage.setItem("accessToken",r.data.data.accessToken),localStorage.setItem("refreshToken",r.data.data.refreshToken),this.refreshToken=r.data.data.refreshToken,r.data.data;throw new Error(r.data.message||"Registration failed")}catch(r){return this.handleError(r)}}async logout(){try{this.refreshToken&&await this.client.post("/auth/logout",{refreshToken:this.refreshToken})}catch(t){console.error("Logout error:",t)}finally{localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),this.refreshToken=null}}async logoutAll(){try{this.refreshToken&&await this.client.post("/auth/logout-all",{refreshToken:this.refreshToken})}catch(t){console.error("Logout all error:",t)}finally{localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),this.refreshToken=null}}async getProfile(){try{const t=await this.client.get("/user/profile");if(t.data.success&&t.data.data)return t.data.data.user;throw new Error(t.data.message||"Failed to get profile")}catch(t){return this.handleError(t)}}async updateProfile(t){try{const r=await this.client.put("/user/profile",t);if(r.data.success&&r.data.data)return r.data.data.user;throw new Error(r.data.message||"Failed to update profile")}catch(r){return this.handleError(r)}}async updateSettings(t){try{const r=await this.client.put("/user/settings",t);if(r.data.success&&r.data.data)return r.data.data.user;throw new Error(r.data.message||"Failed to update settings")}catch(r){return this.handleError(r)}}async updateStatus(t){try{const r=await this.client.put("/user/status",{status:t});if(r.data.success&&r.data.data)return r.data.data.user;throw new Error(r.data.message||"Failed to update status")}catch(r){return this.handleError(r)}}async searchUsers(t){try{const r=await this.client.get(`/user/search?query=${encodeURIComponent(t)}`);if(r.data.success&&r.data.data)return r.data.data.users;throw new Error(r.data.message||"Failed to search users")}catch(r){return this.handleError(r)}}async getFriends(){try{const t=await this.client.get("/friends/list");if(t.data.success&&t.data.data)return t.data.data.friends;throw new Error(t.data.message||"Failed to get friends")}catch(t){return this.handleError(t)}}async sendFriendRequest(t,r){try{const n=await this.client.post("/friends/request",{userId:t,message:r});if(!n.data.success)throw new Error(n.data.message||"Failed to send friend request")}catch(n){return this.handleError(n)}}async getFriendRequests(t="received"){try{const r=await this.client.get(`/friends/requests?type=${t}`);if(r.data.success&&r.data.data)return r.data.data.requests;throw new Error(r.data.message||"Failed to get friend requests")}catch(r){return this.handleError(r)}}async acceptFriendRequest(t){try{const r=await this.client.post("/friends/accept",{requestId:t});if(!r.data.success)throw new Error(r.data.message||"Failed to accept friend request")}catch(r){return this.handleError(r)}}async rejectFriendRequest(t){try{const r=await this.client.post("/friends/reject",{requestId:t});if(!r.data.success)throw new Error(r.data.message||"Failed to reject friend request")}catch(r){return this.handleError(r)}}async removeFriend(t){try{const r=await this.client.delete("/friends/remove",{data:{userId:t}});if(!r.data.success)throw new Error(r.data.message||"Failed to remove friend")}catch(r){return this.handleError(r)}}async getChats(){try{const t=await this.client.get("/chats/list");if(t.data.success&&t.data.data)return t.data.data.chats;throw new Error(t.data.message||"Failed to get chats")}catch(t){return this.handleError(t)}}async createPrivateChat(t){try{const r=await this.client.post("/chats/create-private",{friendId:t});if(r.data.success&&r.data.data)return r.data.data.chat;throw new Error(r.data.message||"Failed to create private chat")}catch(r){return this.handleError(r)}}async createGroupChat(t,r,n){try{const i=await this.client.post("/chats/create-group",{name:t,members:r,description:n});if(i.data.success&&i.data.data)return i.data.data.chat;throw new Error(i.data.message||"Failed to create group chat")}catch(i){return this.handleError(i)}}async getChatMessages(t,r=1,n=50){try{const i=await this.client.get(`/chats/${t}/messages?page=${r}&limit=${n}`);if(i.data.success&&i.data.data)return{messages:i.data.data.items,hasMore:i.data.data.pagination.hasMore};throw new Error(i.data.message||"Failed to get chat messages")}catch(i){return this.handleError(i)}}async getChatDetails(t){try{const r=await this.client.get(`/chats/${t}`);if(r.data.success&&r.data.data)return r.data.data.chat;throw new Error(r.data.message||"Failed to get chat details")}catch(r){return this.handleError(r)}}async uploadImage(t){try{const r=new FormData;r.append("image",t);const n=await this.client.post("/upload/image",r,{headers:{"Content-Type":"multipart/form-data"}});if(n.data.success&&n.data.data)return n.data.data;throw new Error(n.data.message||"Failed to upload image")}catch(r){return this.handleError(r)}}async uploadFile(t){try{const r=new FormData;r.append("file",t);const n=await this.client.post("/upload/file",r,{headers:{"Content-Type":"multipart/form-data"}});if(n.data.success&&n.data.data)return n.data.data;throw new Error(n.data.message||"Failed to upload file")}catch(r){return this.handleError(r)}}}const et=new zk,nr=Og()(sb((e,t)=>({user:null,isAuthenticated:!1,isLoading:!1,login:async(r,n)=>{e({isLoading:!0});try{const i=await et.login({email:r,password:n});e({user:i.user,isAuthenticated:!0,isLoading:!1})}catch(i){throw e({isLoading:!1}),i}},register:async(r,n,i)=>{e({isLoading:!0});try{const a=await et.register({username:r,email:n,password:i});e({user:a.user,isAuthenticated:!0,isLoading:!1})}catch(a){throw e({isLoading:!1}),a}},logout:async()=>{e({isLoading:!0});try{await et.logout()}catch(r){console.error("Logout error:",r)}finally{e({user:null,isAuthenticated:!1,isLoading:!1})}},logoutAll:async()=>{e({isLoading:!0});try{await et.logoutAll()}catch(r){console.error("Logout all error:",r)}finally{e({user:null,isAuthenticated:!1,isLoading:!1})}},updateProfile:async r=>{const{user:n}=t();if(!n)throw new Error("User not authenticated");e({isLoading:!0});try{const i=await et.updateProfile(r);e({user:i,isLoading:!1})}catch(i){throw e({isLoading:!1}),i}},updateSettings:async r=>{const{user:n}=t();if(!n)throw new Error("User not authenticated");e({isLoading:!0});try{const i=await et.updateSettings(r);e({user:i,isLoading:!1})}catch(i){throw e({isLoading:!1}),i}},updateStatus:async r=>{const{user:n}=t();if(!n)throw new Error("User not authenticated");try{const i=await et.updateStatus(r);e({user:i})}catch(i){throw console.error("Status update error:",i),i}},loadUser:async()=>{if(!localStorage.getItem("accessToken")){e({isAuthenticated:!1,user:null});return}e({isLoading:!0});try{const n=await et.getProfile();e({user:n,isAuthenticated:!0,isLoading:!1})}catch(n){console.error("Load user error:",n),e({user:null,isAuthenticated:!1,isLoading:!1}),localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken")}},clearAuth:()=>{e({user:null,isAuthenticated:!1,isLoading:!1}),localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken")}}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})})),We=Og((e,t)=>({chats:[],currentChat:null,messages:{},friends:[],onlineUsers:[],typingUsers:{},isLoading:!1,setCurrentChat:r=>{e({currentChat:r})},loadChats:async()=>{e({isLoading:!0});try{const r=await et.getChats();e({chats:r,isLoading:!1})}catch(r){console.error("Load chats error:",r),e({isLoading:!1})}},loadMessages:async(r,n=1)=>{try{const{messages:i,hasMore:a}=await et.getChatMessages(r,n),s=t().messages[r]||[];return e(n===1?{messages:{...t().messages,[r]:i}}:{messages:{...t().messages,[r]:[...i,...s]}}),a}catch(i){return console.error("Load messages error:",i),!1}},addMessage:r=>{const{messages:n}=t(),i=n[r.chatId]||[];i.some(s=>s._id===r._id)||e({messages:{...n,[r.chatId]:[...i,r]}})},updateMessage:(r,n)=>{const{messages:i}=t(),a={...i};Object.keys(a).forEach(s=>{const o=a[s],c=o.findIndex(u=>u._id===r);c!==-1&&(a[s]=[...o.slice(0,c),{...o[c],...n},...o.slice(c+1)])}),e({messages:a})},loadFriends:async()=>{try{const r=await et.getFriends();e({friends:r})}catch(r){console.error("Load friends error:",r)}},setOnlineUsers:r=>{e({onlineUsers:r})},addTypingUser:(r,n)=>{const{typingUsers:i}=t(),a=i[r]||[];a.includes(n)||e({typingUsers:{...i,[r]:[...a,n]}})},removeTypingUser:(r,n)=>{const{typingUsers:i}=t(),a=i[r]||[];e({typingUsers:{...i,[r]:a.filter(s=>s!==n)}})},createPrivateChat:async r=>{try{const n=await et.createPrivateChat(r),{chats:i}=t();return i.some(s=>s._id===n._id)||e({chats:[n,...i]}),n}catch(n){throw console.error("Create private chat error:",n),n}},createGroupChat:async(r,n,i)=>{try{const a=await et.createGroupChat(r,n,i),{chats:s}=t();return e({chats:[a,...s]}),a}catch(a){throw console.error("Create group chat error:",a),a}},clearChats:()=>{e({chats:[],currentChat:null,messages:{},friends:[],onlineUsers:[],typingUsers:{}})}})),rr=Object.create(null);rr.open="0";rr.close="1";rr.ping="2";rr.pong="3";rr.message="4";rr.upgrade="5";rr.noop="6";const _s=Object.create(null);Object.keys(rr).forEach(e=>{_s[rr[e]]=e});const Bc={type:"error",data:"parser error"},tv=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",rv=typeof ArrayBuffer=="function",nv=e=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(e):e&&e.buffer instanceof ArrayBuffer,ud=({type:e,data:t},r,n)=>tv&&t instanceof Blob?r?n(t):dp(t,n):rv&&(t instanceof ArrayBuffer||nv(t))?r?n(t):dp(new Blob([t]),n):n(rr[e]+(t||"")),dp=(e,t)=>{const r=new FileReader;return r.onload=function(){const n=r.result.split(",")[1];t("b"+(n||""))},r.readAsDataURL(e)};function fp(e){return e instanceof Uint8Array?e:e instanceof ArrayBuffer?new Uint8Array(e):new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}let Ol;function Ik(e,t){if(tv&&e.data instanceof Blob)return e.data.arrayBuffer().then(fp).then(t);if(rv&&(e.data instanceof ArrayBuffer||nv(e.data)))return t(fp(e.data));ud(e,!1,r=>{Ol||(Ol=new TextEncoder),t(Ol.encode(r))})}const pp="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Yi=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let e=0;e<pp.length;e++)Yi[pp.charCodeAt(e)]=e;const Uk=e=>{let t=e.length*.75,r=e.length,n,i=0,a,s,o,c;e[e.length-1]==="="&&(t--,e[e.length-2]==="="&&t--);const u=new ArrayBuffer(t),d=new Uint8Array(u);for(n=0;n<r;n+=4)a=Yi[e.charCodeAt(n)],s=Yi[e.charCodeAt(n+1)],o=Yi[e.charCodeAt(n+2)],c=Yi[e.charCodeAt(n+3)],d[i++]=a<<2|s>>4,d[i++]=(s&15)<<4|o>>2,d[i++]=(o&3)<<6|c&63;return u},$k=typeof ArrayBuffer=="function",dd=(e,t)=>{if(typeof e!="string")return{type:"message",data:iv(e,t)};const r=e.charAt(0);return r==="b"?{type:"message",data:Bk(e.substring(1),t)}:_s[r]?e.length>1?{type:_s[r],data:e.substring(1)}:{type:_s[r]}:Bc},Bk=(e,t)=>{if($k){const r=Uk(e);return iv(r,t)}else return{base64:!0,data:e}},iv=(e,t)=>{switch(t){case"blob":return e instanceof Blob?e:new Blob([e]);case"arraybuffer":default:return e instanceof ArrayBuffer?e:e.buffer}},av=String.fromCharCode(30),Vk=(e,t)=>{const r=e.length,n=new Array(r);let i=0;e.forEach((a,s)=>{ud(a,!1,o=>{n[s]=o,++i===r&&t(n.join(av))})})},Wk=(e,t)=>{const r=e.split(av),n=[];for(let i=0;i<r.length;i++){const a=dd(r[i],t);if(n.push(a),a.type==="error")break}return n};function qk(){return new TransformStream({transform(e,t){Ik(e,r=>{const n=r.length;let i;if(n<126)i=new Uint8Array(1),new DataView(i.buffer).setUint8(0,n);else if(n<65536){i=new Uint8Array(3);const a=new DataView(i.buffer);a.setUint8(0,126),a.setUint16(1,n)}else{i=new Uint8Array(9);const a=new DataView(i.buffer);a.setUint8(0,127),a.setBigUint64(1,BigInt(n))}e.data&&typeof e.data!="string"&&(i[0]|=128),t.enqueue(i),t.enqueue(r)})}})}let Rl;function ls(e){return e.reduce((t,r)=>t+r.length,0)}function cs(e,t){if(e[0].length===t)return e.shift();const r=new Uint8Array(t);let n=0;for(let i=0;i<t;i++)r[i]=e[0][n++],n===e[0].length&&(e.shift(),n=0);return e.length&&n<e[0].length&&(e[0]=e[0].slice(n)),r}function Hk(e,t){Rl||(Rl=new TextDecoder);const r=[];let n=0,i=-1,a=!1;return new TransformStream({transform(s,o){for(r.push(s);;){if(n===0){if(ls(r)<1)break;const c=cs(r,1);a=(c[0]&128)===128,i=c[0]&127,i<126?n=3:i===126?n=1:n=2}else if(n===1){if(ls(r)<2)break;const c=cs(r,2);i=new DataView(c.buffer,c.byteOffset,c.length).getUint16(0),n=3}else if(n===2){if(ls(r)<8)break;const c=cs(r,8),u=new DataView(c.buffer,c.byteOffset,c.length),d=u.getUint32(0);if(d>Math.pow(2,53-32)-1){o.enqueue(Bc);break}i=d*Math.pow(2,32)+u.getUint32(4),n=3}else{if(ls(r)<i)break;const c=cs(r,i);o.enqueue(dd(a?c:Rl.decode(c),t)),n=0}if(i===0||i>e){o.enqueue(Bc);break}}}})}const sv=4;function Oe(e){if(e)return Yk(e)}function Yk(e){for(var t in Oe.prototype)e[t]=Oe.prototype[t];return e}Oe.prototype.on=Oe.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this};Oe.prototype.once=function(e,t){function r(){this.off(e,r),t.apply(this,arguments)}return r.fn=t,this.on(e,r),this};Oe.prototype.off=Oe.prototype.removeListener=Oe.prototype.removeAllListeners=Oe.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var r=this._callbacks["$"+e];if(!r)return this;if(arguments.length==1)return delete this._callbacks["$"+e],this;for(var n,i=0;i<r.length;i++)if(n=r[i],n===t||n.fn===t){r.splice(i,1);break}return r.length===0&&delete this._callbacks["$"+e],this};Oe.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),r=this._callbacks["$"+e],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(r){r=r.slice(0);for(var n=0,i=r.length;n<i;++n)r[n].apply(this,t)}return this};Oe.prototype.emitReserved=Oe.prototype.emit;Oe.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]};Oe.prototype.hasListeners=function(e){return!!this.listeners(e).length};const qo=(()=>typeof Promise=="function"&&typeof Promise.resolve=="function"?t=>Promise.resolve().then(t):(t,r)=>r(t,0))(),Et=(()=>typeof self<"u"?self:typeof window<"u"?window:Function("return this")())(),Kk="arraybuffer";function ov(e,...t){return t.reduce((r,n)=>(e.hasOwnProperty(n)&&(r[n]=e[n]),r),{})}const Qk=Et.setTimeout,Gk=Et.clearTimeout;function Ho(e,t){t.useNativeTimers?(e.setTimeoutFn=Qk.bind(Et),e.clearTimeoutFn=Gk.bind(Et)):(e.setTimeoutFn=Et.setTimeout.bind(Et),e.clearTimeoutFn=Et.clearTimeout.bind(Et))}const Xk=1.33;function Jk(e){return typeof e=="string"?Zk(e):Math.ceil((e.byteLength||e.size)*Xk)}function Zk(e){let t=0,r=0;for(let n=0,i=e.length;n<i;n++)t=e.charCodeAt(n),t<128?r+=1:t<2048?r+=2:t<55296||t>=57344?r+=3:(n++,r+=4);return r}function lv(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function eS(e){let t="";for(let r in e)e.hasOwnProperty(r)&&(t.length&&(t+="&"),t+=encodeURIComponent(r)+"="+encodeURIComponent(e[r]));return t}function tS(e){let t={},r=e.split("&");for(let n=0,i=r.length;n<i;n++){let a=r[n].split("=");t[decodeURIComponent(a[0])]=decodeURIComponent(a[1])}return t}class rS extends Error{constructor(t,r,n){super(t),this.description=r,this.context=n,this.type="TransportError"}}class fd extends Oe{constructor(t){super(),this.writable=!1,Ho(this,t),this.opts=t,this.query=t.query,this.socket=t.socket,this.supportsBinary=!t.forceBase64}onError(t,r,n){return super.emitReserved("error",new rS(t,r,n)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(t){this.readyState==="open"&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){const r=dd(t,this.socket.binaryType);this.onPacket(r)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,r={}){return t+"://"+this._hostname()+this._port()+this.opts.path+this._query(r)}_hostname(){const t=this.opts.hostname;return t.indexOf(":")===-1?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(t){const r=eS(t);return r.length?"?"+r:""}}class nS extends fd{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(t){this.readyState="pausing";const r=()=>{this.readyState="paused",t()};if(this._polling||!this.writable){let n=0;this._polling&&(n++,this.once("pollComplete",function(){--n||r()})),this.writable||(n++,this.once("drain",function(){--n||r()}))}else r()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){const r=n=>{if(this.readyState==="opening"&&n.type==="open"&&this.onOpen(),n.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(n)};Wk(t,this.socket.binaryType).forEach(r),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const t=()=>{this.write([{type:"close"}])};this.readyState==="open"?t():this.once("open",t)}write(t){this.writable=!1,Vk(t,r=>{this.doWrite(r,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const t=this.opts.secure?"https":"http",r=this.query||{};return this.opts.timestampRequests!==!1&&(r[this.opts.timestampParam]=lv()),!this.supportsBinary&&!r.sid&&(r.b64=1),this.createUri(t,r)}}let cv=!1;try{cv=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const iS=cv;function aS(){}class sS extends nS{constructor(t){if(super(t),typeof location<"u"){const r=location.protocol==="https:";let n=location.port;n||(n=r?"443":"80"),this.xd=typeof location<"u"&&t.hostname!==location.hostname||n!==t.port}}doWrite(t,r){const n=this.request({method:"POST",data:t});n.on("success",r),n.on("error",(i,a)=>{this.onError("xhr post error",i,a)})}doPoll(){const t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(r,n)=>{this.onError("xhr poll error",r,n)}),this.pollXhr=t}}class tr extends Oe{constructor(t,r,n){super(),this.createRequest=t,Ho(this,n),this._opts=n,this._method=n.method||"GET",this._uri=r,this._data=n.data!==void 0?n.data:null,this._create()}_create(){var t;const r=ov(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");r.xdomain=!!this._opts.xd;const n=this._xhr=this.createRequest(r);try{n.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){n.setDisableHeaderCheck&&n.setDisableHeaderCheck(!0);for(let i in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(i)&&n.setRequestHeader(i,this._opts.extraHeaders[i])}}catch{}if(this._method==="POST")try{n.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{n.setRequestHeader("Accept","*/*")}catch{}(t=this._opts.cookieJar)===null||t===void 0||t.addCookies(n),"withCredentials"in n&&(n.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(n.timeout=this._opts.requestTimeout),n.onreadystatechange=()=>{var i;n.readyState===3&&((i=this._opts.cookieJar)===null||i===void 0||i.parseCookies(n.getResponseHeader("set-cookie"))),n.readyState===4&&(n.status===200||n.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof n.status=="number"?n.status:0)},0))},n.send(this._data)}catch(i){this.setTimeoutFn(()=>{this._onError(i)},0);return}typeof document<"u"&&(this._index=tr.requestsCount++,tr.requests[this._index]=this)}_onError(t){this.emitReserved("error",t,this._xhr),this._cleanup(!0)}_cleanup(t){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=aS,t)try{this._xhr.abort()}catch{}typeof document<"u"&&delete tr.requests[this._index],this._xhr=null}}_onLoad(){const t=this._xhr.responseText;t!==null&&(this.emitReserved("data",t),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}tr.requestsCount=0;tr.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",mp);else if(typeof addEventListener=="function"){const e="onpagehide"in Et?"pagehide":"unload";addEventListener(e,mp,!1)}}function mp(){for(let e in tr.requests)tr.requests.hasOwnProperty(e)&&tr.requests[e].abort()}const oS=function(){const e=uv({xdomain:!1});return e&&e.responseType!==null}();class lS extends sS{constructor(t){super(t);const r=t&&t.forceBase64;this.supportsBinary=oS&&!r}request(t={}){return Object.assign(t,{xd:this.xd},this.opts),new tr(uv,this.uri(),t)}}function uv(e){const t=e.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!t||iS))return new XMLHttpRequest}catch{}if(!t)try{return new Et[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const dv=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class cS extends fd{get name(){return"websocket"}doOpen(){const t=this.uri(),r=this.opts.protocols,n=dv?{}:ov(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(n.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,r,n)}catch(i){return this.emitReserved("error",i)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let r=0;r<t.length;r++){const n=t[r],i=r===t.length-1;ud(n,this.supportsBinary,a=>{try{this.doWrite(n,a)}catch{}i&&qo(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const t=this.opts.secure?"wss":"ws",r=this.query||{};return this.opts.timestampRequests&&(r[this.opts.timestampParam]=lv()),this.supportsBinary||(r.b64=1),this.createUri(t,r)}}const Al=Et.WebSocket||Et.MozWebSocket;class uS extends cS{createSocket(t,r,n){return dv?new Al(t,r,n):r?new Al(t,r):new Al(t)}doWrite(t,r){this.ws.send(r)}}class dS extends fd{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this._transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(t=>{const r=Hk(Number.MAX_SAFE_INTEGER,this.socket.binaryType),n=t.readable.pipeThrough(r).getReader(),i=qk();i.readable.pipeTo(t.writable),this._writer=i.writable.getWriter();const a=()=>{n.read().then(({done:o,value:c})=>{o||(this.onPacket(c),a())}).catch(o=>{})};a();const s={type:"open"};this.query.sid&&(s.data=`{"sid":"${this.query.sid}"}`),this._writer.write(s).then(()=>this.onOpen())})})}write(t){this.writable=!1;for(let r=0;r<t.length;r++){const n=t[r],i=r===t.length-1;this._writer.write(n).then(()=>{i&&qo(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;(t=this._transport)===null||t===void 0||t.close()}}const fS={websocket:uS,webtransport:dS,polling:lS},pS=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,mS=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function Vc(e){if(e.length>8e3)throw"URI too long";const t=e,r=e.indexOf("["),n=e.indexOf("]");r!=-1&&n!=-1&&(e=e.substring(0,r)+e.substring(r,n).replace(/:/g,";")+e.substring(n,e.length));let i=pS.exec(e||""),a={},s=14;for(;s--;)a[mS[s]]=i[s]||"";return r!=-1&&n!=-1&&(a.source=t,a.host=a.host.substring(1,a.host.length-1).replace(/;/g,":"),a.authority=a.authority.replace("[","").replace("]","").replace(/;/g,":"),a.ipv6uri=!0),a.pathNames=hS(a,a.path),a.queryKey=gS(a,a.query),a}function hS(e,t){const r=/\/{2,9}/g,n=t.replace(r,"/").split("/");return(t.slice(0,1)=="/"||t.length===0)&&n.splice(0,1),t.slice(-1)=="/"&&n.splice(n.length-1,1),n}function gS(e,t){const r={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(n,i,a){i&&(r[i]=a)}),r}const Wc=typeof addEventListener=="function"&&typeof removeEventListener=="function",Ts=[];Wc&&addEventListener("offline",()=>{Ts.forEach(e=>e())},!1);class Hr extends Oe{constructor(t,r){if(super(),this.binaryType=Kk,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,t&&typeof t=="object"&&(r=t,t=null),t){const n=Vc(t);r.hostname=n.host,r.secure=n.protocol==="https"||n.protocol==="wss",r.port=n.port,n.query&&(r.query=n.query)}else r.host&&(r.hostname=Vc(r.host).host);Ho(this,r),this.secure=r.secure!=null?r.secure:typeof location<"u"&&location.protocol==="https:",r.hostname&&!r.port&&(r.port=this.secure?"443":"80"),this.hostname=r.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=r.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},r.transports.forEach(n=>{const i=n.prototype.name;this.transports.push(i),this._transportsByName[i]=n}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},r),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=tS(this.opts.query)),Wc&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},Ts.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(t){const r=Object.assign({},this.opts.query);r.EIO=sv,r.transport=t,this.id&&(r.sid=this.id);const n=Object.assign({},this.opts,{query:r,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this._transportsByName[t](n)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const t=this.opts.rememberUpgrade&&Hr.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const r=this.createTransport(t);r.open(),this.setTransport(r)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",r=>this._onClose("transport close",r))}onOpen(){this.readyState="open",Hr.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(t){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const r=new Error("server error");r.code=t.data,this._onError(r);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data);break}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this._pingInterval=t.pingInterval,this._pingTimeout=t.pingTimeout,this._maxPayload=t.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const t=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+t,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},t),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const t=this._getWritablePackets();this.transport.send(t),this._prevBufferLen=t.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let r=1;for(let n=0;n<this.writeBuffer.length;n++){const i=this.writeBuffer[n].data;if(i&&(r+=Jk(i)),n>0&&r>this._maxPayload)return this.writeBuffer.slice(0,n);r+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const t=Date.now()>this._pingTimeoutTime;return t&&(this._pingTimeoutTime=0,qo(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),t}write(t,r,n){return this._sendPacket("message",t,r,n),this}send(t,r,n){return this._sendPacket("message",t,r,n),this}_sendPacket(t,r,n,i){if(typeof r=="function"&&(i=r,r=void 0),typeof n=="function"&&(i=n,n=null),this.readyState==="closing"||this.readyState==="closed")return;n=n||{},n.compress=n.compress!==!1;const a={type:t,data:r,options:n};this.emitReserved("packetCreate",a),this.writeBuffer.push(a),i&&this.once("flush",i),this.flush()}close(){const t=()=>{this._onClose("forced close"),this.transport.close()},r=()=>{this.off("upgrade",r),this.off("upgradeError",r),t()},n=()=>{this.once("upgrade",r),this.once("upgradeError",r)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?n():t()}):this.upgrading?n():t()),this}_onError(t){if(Hr.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",t),this._onClose("transport error",t)}_onClose(t,r){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),Wc&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const n=Ts.indexOf(this._offlineEventListener);n!==-1&&Ts.splice(n,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,r),this.writeBuffer=[],this._prevBufferLen=0}}}Hr.protocol=sv;class vS extends Hr{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let t=0;t<this._upgrades.length;t++)this._probe(this._upgrades[t])}_probe(t){let r=this.createTransport(t),n=!1;Hr.priorWebsocketSuccess=!1;const i=()=>{n||(r.send([{type:"ping",data:"probe"}]),r.once("packet",f=>{if(!n)if(f.type==="pong"&&f.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",r),!r)return;Hr.priorWebsocketSuccess=r.name==="websocket",this.transport.pause(()=>{n||this.readyState!=="closed"&&(d(),this.setTransport(r),r.send([{type:"upgrade"}]),this.emitReserved("upgrade",r),r=null,this.upgrading=!1,this.flush())})}else{const h=new Error("probe error");h.transport=r.name,this.emitReserved("upgradeError",h)}}))};function a(){n||(n=!0,d(),r.close(),r=null)}const s=f=>{const h=new Error("probe error: "+f);h.transport=r.name,a(),this.emitReserved("upgradeError",h)};function o(){s("transport closed")}function c(){s("socket closed")}function u(f){r&&f.name!==r.name&&a()}const d=()=>{r.removeListener("open",i),r.removeListener("error",s),r.removeListener("close",o),this.off("close",c),this.off("upgrading",u)};r.once("open",i),r.once("error",s),r.once("close",o),this.once("close",c),this.once("upgrading",u),this._upgrades.indexOf("webtransport")!==-1&&t!=="webtransport"?this.setTimeoutFn(()=>{n||r.open()},200):r.open()}onHandshake(t){this._upgrades=this._filterUpgrades(t.upgrades),super.onHandshake(t)}_filterUpgrades(t){const r=[];for(let n=0;n<t.length;n++)~this.transports.indexOf(t[n])&&r.push(t[n]);return r}}let yS=class extends vS{constructor(t,r={}){const n=typeof t=="object"?t:r;(!n.transports||n.transports&&typeof n.transports[0]=="string")&&(n.transports=(n.transports||["polling","websocket","webtransport"]).map(i=>fS[i]).filter(i=>!!i)),super(t,n)}};function xS(e,t="",r){let n=e;r=r||typeof location<"u"&&location,e==null&&(e=r.protocol+"//"+r.host),typeof e=="string"&&(e.charAt(0)==="/"&&(e.charAt(1)==="/"?e=r.protocol+e:e=r.host+e),/^(https?|wss?):\/\//.test(e)||(typeof r<"u"?e=r.protocol+"//"+e:e="https://"+e),n=Vc(e)),n.port||(/^(http|ws)$/.test(n.protocol)?n.port="80":/^(http|ws)s$/.test(n.protocol)&&(n.port="443")),n.path=n.path||"/";const a=n.host.indexOf(":")!==-1?"["+n.host+"]":n.host;return n.id=n.protocol+"://"+a+":"+n.port+t,n.href=n.protocol+"://"+a+(r&&r.port===n.port?"":":"+n.port),n}const wS=typeof ArrayBuffer=="function",bS=e=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer,fv=Object.prototype.toString,kS=typeof Blob=="function"||typeof Blob<"u"&&fv.call(Blob)==="[object BlobConstructor]",SS=typeof File=="function"||typeof File<"u"&&fv.call(File)==="[object FileConstructor]";function pd(e){return wS&&(e instanceof ArrayBuffer||bS(e))||kS&&e instanceof Blob||SS&&e instanceof File}function Ps(e,t){if(!e||typeof e!="object")return!1;if(Array.isArray(e)){for(let r=0,n=e.length;r<n;r++)if(Ps(e[r]))return!0;return!1}if(pd(e))return!0;if(e.toJSON&&typeof e.toJSON=="function"&&arguments.length===1)return Ps(e.toJSON(),!0);for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&Ps(e[r]))return!0;return!1}function ES(e){const t=[],r=e.data,n=e;return n.data=qc(r,t),n.attachments=t.length,{packet:n,buffers:t}}function qc(e,t){if(!e)return e;if(pd(e)){const r={_placeholder:!0,num:t.length};return t.push(e),r}else if(Array.isArray(e)){const r=new Array(e.length);for(let n=0;n<e.length;n++)r[n]=qc(e[n],t);return r}else if(typeof e=="object"&&!(e instanceof Date)){const r={};for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(r[n]=qc(e[n],t));return r}return e}function jS(e,t){return e.data=Hc(e.data,t),delete e.attachments,e}function Hc(e,t){if(!e)return e;if(e&&e._placeholder===!0){if(typeof e.num=="number"&&e.num>=0&&e.num<t.length)return t[e.num];throw new Error("illegal attachments")}else if(Array.isArray(e))for(let r=0;r<e.length;r++)e[r]=Hc(e[r],t);else if(typeof e=="object")for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(e[r]=Hc(e[r],t));return e}const NS=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],CS=5;var re;(function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"})(re||(re={}));class _S{constructor(t){this.replacer=t}encode(t){return(t.type===re.EVENT||t.type===re.ACK)&&Ps(t)?this.encodeAsBinary({type:t.type===re.EVENT?re.BINARY_EVENT:re.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let r=""+t.type;return(t.type===re.BINARY_EVENT||t.type===re.BINARY_ACK)&&(r+=t.attachments+"-"),t.nsp&&t.nsp!=="/"&&(r+=t.nsp+","),t.id!=null&&(r+=t.id),t.data!=null&&(r+=JSON.stringify(t.data,this.replacer)),r}encodeAsBinary(t){const r=ES(t),n=this.encodeAsString(r.packet),i=r.buffers;return i.unshift(n),i}}function hp(e){return Object.prototype.toString.call(e)==="[object Object]"}class md extends Oe{constructor(t){super(),this.reviver=t}add(t){let r;if(typeof t=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");r=this.decodeString(t);const n=r.type===re.BINARY_EVENT;n||r.type===re.BINARY_ACK?(r.type=n?re.EVENT:re.ACK,this.reconstructor=new TS(r),r.attachments===0&&super.emitReserved("decoded",r)):super.emitReserved("decoded",r)}else if(pd(t)||t.base64)if(this.reconstructor)r=this.reconstructor.takeBinaryData(t),r&&(this.reconstructor=null,super.emitReserved("decoded",r));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+t)}decodeString(t){let r=0;const n={type:Number(t.charAt(0))};if(re[n.type]===void 0)throw new Error("unknown packet type "+n.type);if(n.type===re.BINARY_EVENT||n.type===re.BINARY_ACK){const a=r+1;for(;t.charAt(++r)!=="-"&&r!=t.length;);const s=t.substring(a,r);if(s!=Number(s)||t.charAt(r)!=="-")throw new Error("Illegal attachments");n.attachments=Number(s)}if(t.charAt(r+1)==="/"){const a=r+1;for(;++r&&!(t.charAt(r)===","||r===t.length););n.nsp=t.substring(a,r)}else n.nsp="/";const i=t.charAt(r+1);if(i!==""&&Number(i)==i){const a=r+1;for(;++r;){const s=t.charAt(r);if(s==null||Number(s)!=s){--r;break}if(r===t.length)break}n.id=Number(t.substring(a,r+1))}if(t.charAt(++r)){const a=this.tryParse(t.substr(r));if(md.isPayloadValid(n.type,a))n.data=a;else throw new Error("invalid payload")}return n}tryParse(t){try{return JSON.parse(t,this.reviver)}catch{return!1}}static isPayloadValid(t,r){switch(t){case re.CONNECT:return hp(r);case re.DISCONNECT:return r===void 0;case re.CONNECT_ERROR:return typeof r=="string"||hp(r);case re.EVENT:case re.BINARY_EVENT:return Array.isArray(r)&&(typeof r[0]=="number"||typeof r[0]=="string"&&NS.indexOf(r[0])===-1);case re.ACK:case re.BINARY_ACK:return Array.isArray(r)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class TS{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){const r=jS(this.reconPack,this.buffers);return this.finishedReconstruction(),r}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const PS=Object.freeze(Object.defineProperty({__proto__:null,Decoder:md,Encoder:_S,get PacketType(){return re},protocol:CS},Symbol.toStringTag,{value:"Module"}));function Dt(e,t,r){return e.on(t,r),function(){e.off(t,r)}}const OS=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class pv extends Oe{constructor(t,r,n){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=r,n&&n.auth&&(this.auth=n.auth),this._opts=Object.assign({},n),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const t=this.io;this.subs=[Dt(t,"open",this.onopen.bind(this)),Dt(t,"packet",this.onpacket.bind(this)),Dt(t,"error",this.onerror.bind(this)),Dt(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...t){return t.unshift("message"),this.emit.apply(this,t),this}emit(t,...r){var n,i,a;if(OS.hasOwnProperty(t))throw new Error('"'+t.toString()+'" is a reserved event name');if(r.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(r),this;const s={type:re.EVENT,data:r};if(s.options={},s.options.compress=this.flags.compress!==!1,typeof r[r.length-1]=="function"){const d=this.ids++,f=r.pop();this._registerAckCallback(d,f),s.id=d}const o=(i=(n=this.io.engine)===null||n===void 0?void 0:n.transport)===null||i===void 0?void 0:i.writable,c=this.connected&&!(!((a=this.io.engine)===null||a===void 0)&&a._hasPingExpired());return this.flags.volatile&&!o||(c?(this.notifyOutgoingListeners(s),this.packet(s)):this.sendBuffer.push(s)),this.flags={},this}_registerAckCallback(t,r){var n;const i=(n=this.flags.timeout)!==null&&n!==void 0?n:this._opts.ackTimeout;if(i===void 0){this.acks[t]=r;return}const a=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let o=0;o<this.sendBuffer.length;o++)this.sendBuffer[o].id===t&&this.sendBuffer.splice(o,1);r.call(this,new Error("operation has timed out"))},i),s=(...o)=>{this.io.clearTimeoutFn(a),r.apply(this,o)};s.withError=!0,this.acks[t]=s}emitWithAck(t,...r){return new Promise((n,i)=>{const a=(s,o)=>s?i(s):n(o);a.withError=!0,r.push(a),this.emit(t,...r)})}_addToQueue(t){let r;typeof t[t.length-1]=="function"&&(r=t.pop());const n={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push((i,...a)=>n!==this._queue[0]?void 0:(i!==null?n.tryCount>this._opts.retries&&(this._queue.shift(),r&&r(i)):(this._queue.shift(),r&&r(null,...a)),n.pending=!1,this._drainQueue())),this._queue.push(n),this._drainQueue()}_drainQueue(t=!1){if(!this.connected||this._queue.length===0)return;const r=this._queue[0];r.pending&&!t||(r.pending=!0,r.tryCount++,this.flags=r.flags,this.emit.apply(this,r.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){typeof this.auth=="function"?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:re.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,r){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,r),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(n=>String(n.id)===t)){const n=this.acks[t];delete this.acks[t],n.withError&&n.call(this,new Error("socket has been disconnected"))}})}onpacket(t){if(t.nsp===this.nsp)switch(t.type){case re.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case re.EVENT:case re.BINARY_EVENT:this.onevent(t);break;case re.ACK:case re.BINARY_ACK:this.onack(t);break;case re.DISCONNECT:this.ondisconnect();break;case re.CONNECT_ERROR:this.destroy();const n=new Error(t.data.message);n.data=t.data.data,this.emitReserved("connect_error",n);break}}onevent(t){const r=t.data||[];t.id!=null&&r.push(this.ack(t.id)),this.connected?this.emitEvent(r):this.receiveBuffer.push(Object.freeze(r))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length){const r=this._anyListeners.slice();for(const n of r)n.apply(this,t)}super.emit.apply(this,t),this._pid&&t.length&&typeof t[t.length-1]=="string"&&(this._lastOffset=t[t.length-1])}ack(t){const r=this;let n=!1;return function(...i){n||(n=!0,r.packet({type:re.ACK,id:t,data:i}))}}onack(t){const r=this.acks[t.id];typeof r=="function"&&(delete this.acks[t.id],r.withError&&t.data.unshift(null),r.apply(this,t.data))}onconnect(t,r){this.id=t,this.recovered=r&&this._pid===r,this._pid=r,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:re.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){const r=this._anyListeners;for(let n=0;n<r.length;n++)if(t===r[n])return r.splice(n,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){const r=this._anyOutgoingListeners;for(let n=0;n<r.length;n++)if(t===r[n])return r.splice(n,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const r=this._anyOutgoingListeners.slice();for(const n of r)n.apply(this,t.data)}}}function ki(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}ki.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),r=Math.floor(t*this.jitter*e);e=Math.floor(t*10)&1?e+r:e-r}return Math.min(e,this.max)|0};ki.prototype.reset=function(){this.attempts=0};ki.prototype.setMin=function(e){this.ms=e};ki.prototype.setMax=function(e){this.max=e};ki.prototype.setJitter=function(e){this.jitter=e};class Yc extends Oe{constructor(t,r){var n;super(),this.nsps={},this.subs=[],t&&typeof t=="object"&&(r=t,t=void 0),r=r||{},r.path=r.path||"/socket.io",this.opts=r,Ho(this,r),this.reconnection(r.reconnection!==!1),this.reconnectionAttempts(r.reconnectionAttempts||1/0),this.reconnectionDelay(r.reconnectionDelay||1e3),this.reconnectionDelayMax(r.reconnectionDelayMax||5e3),this.randomizationFactor((n=r.randomizationFactor)!==null&&n!==void 0?n:.5),this.backoff=new ki({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(r.timeout==null?2e4:r.timeout),this._readyState="closed",this.uri=t;const i=r.parser||PS;this.encoder=new i.Encoder,this.decoder=new i.Decoder,this._autoConnect=r.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,t||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(t){return t===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var r;return t===void 0?this._reconnectionDelay:(this._reconnectionDelay=t,(r=this.backoff)===null||r===void 0||r.setMin(t),this)}randomizationFactor(t){var r;return t===void 0?this._randomizationFactor:(this._randomizationFactor=t,(r=this.backoff)===null||r===void 0||r.setJitter(t),this)}reconnectionDelayMax(t){var r;return t===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,(r=this.backoff)===null||r===void 0||r.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new yS(this.uri,this.opts);const r=this.engine,n=this;this._readyState="opening",this.skipReconnect=!1;const i=Dt(r,"open",function(){n.onopen(),t&&t()}),a=o=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",o),t?t(o):this.maybeReconnectOnOpen()},s=Dt(r,"error",a);if(this._timeout!==!1){const o=this._timeout,c=this.setTimeoutFn(()=>{i(),a(new Error("timeout")),r.close()},o);this.opts.autoUnref&&c.unref(),this.subs.push(()=>{this.clearTimeoutFn(c)})}return this.subs.push(i),this.subs.push(s),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const t=this.engine;this.subs.push(Dt(t,"ping",this.onping.bind(this)),Dt(t,"data",this.ondata.bind(this)),Dt(t,"error",this.onerror.bind(this)),Dt(t,"close",this.onclose.bind(this)),Dt(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(r){this.onclose("parse error",r)}}ondecoded(t){qo(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,r){let n=this.nsps[t];return n?this._autoConnect&&!n.active&&n.connect():(n=new pv(this,t,r),this.nsps[t]=n),n}_destroy(t){const r=Object.keys(this.nsps);for(const n of r)if(this.nsps[n].active)return;this._close()}_packet(t){const r=this.encoder.encode(t);for(let n=0;n<r.length;n++)this.engine.write(r[n],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(t,r){var n;this.cleanup(),(n=this.engine)===null||n===void 0||n.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,r),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const r=this.backoff.duration();this._reconnecting=!0;const n=this.setTimeoutFn(()=>{t.skipReconnect||(this.emitReserved("reconnect_attempt",t.backoff.attempts),!t.skipReconnect&&t.open(i=>{i?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",i)):t.onreconnect()}))},r);this.opts.autoUnref&&n.unref(),this.subs.push(()=>{this.clearTimeoutFn(n)})}}onreconnect(){const t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}const Ii={};function Os(e,t){typeof e=="object"&&(t=e,e=void 0),t=t||{};const r=xS(e,t.path||"/socket.io"),n=r.source,i=r.id,a=r.path,s=Ii[i]&&a in Ii[i].nsps,o=t.forceNew||t["force new connection"]||t.multiplex===!1||s;let c;return o?c=new Yc(n,t):(Ii[i]||(Ii[i]=new Yc(n,t)),c=Ii[i]),r.query&&!t.query&&(t.query=r.queryKey),c.socket(r.path,t)}Object.assign(Os,{Manager:Yc,Socket:pv,io:Os,connect:Os});class RS{constructor(){an(this,"socket",null);an(this,"reconnectAttempts",0);an(this,"maxReconnectAttempts",5);an(this,"reconnectDelay",1e3)}connect(){var r;const t=localStorage.getItem("accessToken");if(!t){console.warn("No access token found, cannot connect to socket");return}if((r=this.socket)!=null&&r.connected){console.log("Socket already connected");return}this.socket=Os("/",{auth:{token:t},transports:["websocket","polling"]}),this.setupEventListeners()}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null)}setupEventListeners(){this.socket&&(this.socket.on("connect",()=>{var t;console.log("Socket connected:",(t=this.socket)==null?void 0:t.id),this.reconnectAttempts=0}),this.socket.on("disconnect",t=>{console.log("Socket disconnected:",t),t==="io server disconnect"&&this.handleReconnect()}),this.socket.on("connect_error",t=>{console.error("Socket connection error:",t),this.handleReconnect()}),this.socket.on("message:new",t=>{const{addMessage:r}=We.getState();r(t.message);const{currentChat:n}=We.getState();(n==null?void 0:n._id)!==t.chatId&&le.success(`New message from ${t.message.sender.username}`)}),this.socket.on("message:read",t=>{const{updateMessage:r}=We.getState();r(t.messageId,{status:"read",readBy:[{user:t.userId,readAt:t.readAt}]})}),this.socket.on("message:reaction",t=>{const{updateMessage:r}=We.getState();r(t.messageId,{reactions:t.reactions})}),this.socket.on("message:revoked",t=>{const{updateMessage:r}=We.getState();r(t.messageId,{isRevoked:!0,revokedAt:new Date().toISOString(),content:"This message has been revoked"})}),this.socket.on("typing:start",t=>{const{addTypingUser:r}=We.getState();r(t.chatId,t.userId)}),this.socket.on("typing:stop",t=>{const{removeTypingUser:r}=We.getState();r(t.chatId,t.userId)}),this.socket.on("user:status",t=>{const{onlineUsers:r,setOnlineUsers:n}=We.getState();t.status==="online"?r.includes(t.userId)||n([...r,t.userId]):n(r.filter(i=>i!==t.userId))}),this.socket.on("friend:request",t=>{le.success(`Friend request from ${t.fromUsername}`)}),this.socket.on("error",t=>{le.error(t.message)}))}handleReconnect(){if(this.reconnectAttempts>=this.maxReconnectAttempts){console.error("Max reconnection attempts reached");return}this.reconnectAttempts++;const t=this.reconnectDelay*Math.pow(2,this.reconnectAttempts-1);console.log(`Attempting to reconnect in ${t}ms (attempt ${this.reconnectAttempts})`),setTimeout(()=>{this.connect()},t)}sendMessage(t,r,n="text",i,a){var s;if(!((s=this.socket)!=null&&s.connected)){le.error("Not connected to server");return}this.socket.emit("message:send",{chatId:t,content:r,type:n,replyTo:i,metadata:a})}markMessageAsRead(t,r){var n;(n=this.socket)!=null&&n.connected&&this.socket.emit("message:read",{messageId:t,chatId:r})}addReaction(t,r,n){var i;(i=this.socket)!=null&&i.connected&&this.socket.emit("message:react",{messageId:t,emoji:r,chatId:n})}revokeMessage(t,r){var n;(n=this.socket)!=null&&n.connected&&this.socket.emit("message:revoke",{messageId:t,chatId:r})}startTyping(t){var r;(r=this.socket)!=null&&r.connected&&this.socket.emit("typing:start",{chatId:t})}stopTyping(t){var r;(r=this.socket)!=null&&r.connected&&this.socket.emit("typing:stop",{chatId:t})}updateStatus(t){var r;(r=this.socket)!=null&&r.connected&&this.socket.emit("user:status",{status:t})}sendFriendRequestNotification(t){var r;(r=this.socket)!=null&&r.connected&&this.socket.emit("friend:request",{toUserId:t})}isConnected(){var t;return((t=this.socket)==null?void 0:t.connected)||!1}getSocketId(){var t;return(t=this.socket)==null?void 0:t.id}}const zt=new RS;function mv(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(r=mv(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function AS(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=mv(e))&&(n&&(n+=" "),n+=t);return n}const hd="-",DS=e=>{const t=MS(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:s=>{const o=s.split(hd);return o[0]===""&&o.length!==1&&o.shift(),hv(o,t)||LS(s)},getConflictingClassGroupIds:(s,o)=>{const c=r[s]||[];return o&&n[s]?[...c,...n[s]]:c}}},hv=(e,t)=>{var s;if(e.length===0)return t.classGroupId;const r=e[0],n=t.nextPart.get(r),i=n?hv(e.slice(1),n):void 0;if(i)return i;if(t.validators.length===0)return;const a=e.join(hd);return(s=t.validators.find(({validator:o})=>o(a)))==null?void 0:s.classGroupId},gp=/^\[(.+)\]$/,LS=e=>{if(gp.test(e)){const t=gp.exec(e)[1],r=t==null?void 0:t.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},MS=e=>{const{theme:t,prefix:r}=e,n={nextPart:new Map,validators:[]};return zS(Object.entries(e.classGroups),r).forEach(([a,s])=>{Kc(s,n,a,t)}),n},Kc=(e,t,r,n)=>{e.forEach(i=>{if(typeof i=="string"){const a=i===""?t:vp(t,i);a.classGroupId=r;return}if(typeof i=="function"){if(FS(i)){Kc(i(n),t,r,n);return}t.validators.push({validator:i,classGroupId:r});return}Object.entries(i).forEach(([a,s])=>{Kc(s,vp(t,a),r,n)})})},vp=(e,t)=>{let r=e;return t.split(hd).forEach(n=>{r.nextPart.has(n)||r.nextPart.set(n,{nextPart:new Map,validators:[]}),r=r.nextPart.get(n)}),r},FS=e=>e.isThemeGetter,zS=(e,t)=>t?e.map(([r,n])=>{const i=n.map(a=>typeof a=="string"?t+a:typeof a=="object"?Object.fromEntries(Object.entries(a).map(([s,o])=>[t+s,o])):a);return[r,i]}):e,IS=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,n=new Map;const i=(a,s)=>{r.set(a,s),t++,t>e&&(t=0,n=r,r=new Map)};return{get(a){let s=r.get(a);if(s!==void 0)return s;if((s=n.get(a))!==void 0)return i(a,s),s},set(a,s){r.has(a)?r.set(a,s):i(a,s)}}},gv="!",US=e=>{const{separator:t,experimentalParseClassName:r}=e,n=t.length===1,i=t[0],a=t.length,s=o=>{const c=[];let u=0,d=0,f;for(let x=0;x<o.length;x++){let m=o[x];if(u===0){if(m===i&&(n||o.slice(x,x+a)===t)){c.push(o.slice(d,x)),d=x+a;continue}if(m==="/"){f=x;continue}}m==="["?u++:m==="]"&&u--}const h=c.length===0?o:o.substring(d),w=h.startsWith(gv),g=w?h.substring(1):h,y=f&&f>d?f-d:void 0;return{modifiers:c,hasImportantModifier:w,baseClassName:g,maybePostfixModifierPosition:y}};return r?o=>r({className:o,parseClassName:s}):s},$S=e=>{if(e.length<=1)return e;const t=[];let r=[];return e.forEach(n=>{n[0]==="["?(t.push(...r.sort(),n),r=[]):r.push(n)}),t.push(...r.sort()),t},BS=e=>({cache:IS(e.cacheSize),parseClassName:US(e),...DS(e)}),VS=/\s+/,WS=(e,t)=>{const{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:i}=t,a=[],s=e.trim().split(VS);let o="";for(let c=s.length-1;c>=0;c-=1){const u=s[c],{modifiers:d,hasImportantModifier:f,baseClassName:h,maybePostfixModifierPosition:w}=r(u);let g=!!w,y=n(g?h.substring(0,w):h);if(!y){if(!g){o=u+(o.length>0?" "+o:o);continue}if(y=n(h),!y){o=u+(o.length>0?" "+o:o);continue}g=!1}const x=$S(d).join(":"),m=f?x+gv:x,p=m+y;if(a.includes(p))continue;a.push(p);const v=i(y,g);for(let k=0;k<v.length;++k){const _=v[k];a.push(m+_)}o=u+(o.length>0?" "+o:o)}return o};function qS(){let e=0,t,r,n="";for(;e<arguments.length;)(t=arguments[e++])&&(r=vv(t))&&(n&&(n+=" "),n+=r);return n}const vv=e=>{if(typeof e=="string")return e;let t,r="";for(let n=0;n<e.length;n++)e[n]&&(t=vv(e[n]))&&(r&&(r+=" "),r+=t);return r};function HS(e,...t){let r,n,i,a=s;function s(c){const u=t.reduce((d,f)=>f(d),e());return r=BS(u),n=r.cache.get,i=r.cache.set,a=o,o(c)}function o(c){const u=n(c);if(u)return u;const d=WS(c,r);return i(c,d),d}return function(){return a(qS.apply(null,arguments))}}const he=e=>{const t=r=>r[e]||[];return t.isThemeGetter=!0,t},yv=/^\[(?:([a-z-]+):)?(.+)\]$/i,YS=/^\d+\/\d+$/,KS=new Set(["px","full","screen"]),QS=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,GS=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,XS=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,JS=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,ZS=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,sr=e=>ti(e)||KS.has(e)||YS.test(e),jr=e=>Si(e,"length",o2),ti=e=>!!e&&!Number.isNaN(Number(e)),Dl=e=>Si(e,"number",ti),Ui=e=>!!e&&Number.isInteger(Number(e)),e2=e=>e.endsWith("%")&&ti(e.slice(0,-1)),X=e=>yv.test(e),Nr=e=>QS.test(e),t2=new Set(["length","size","percentage"]),r2=e=>Si(e,t2,xv),n2=e=>Si(e,"position",xv),i2=new Set(["image","url"]),a2=e=>Si(e,i2,c2),s2=e=>Si(e,"",l2),$i=()=>!0,Si=(e,t,r)=>{const n=yv.exec(e);return n?n[1]?typeof t=="string"?n[1]===t:t.has(n[1]):r(n[2]):!1},o2=e=>GS.test(e)&&!XS.test(e),xv=()=>!1,l2=e=>JS.test(e),c2=e=>ZS.test(e),u2=()=>{const e=he("colors"),t=he("spacing"),r=he("blur"),n=he("brightness"),i=he("borderColor"),a=he("borderRadius"),s=he("borderSpacing"),o=he("borderWidth"),c=he("contrast"),u=he("grayscale"),d=he("hueRotate"),f=he("invert"),h=he("gap"),w=he("gradientColorStops"),g=he("gradientColorStopPositions"),y=he("inset"),x=he("margin"),m=he("opacity"),p=he("padding"),v=he("saturate"),k=he("scale"),_=he("sepia"),A=he("skew"),P=he("space"),O=he("translate"),L=()=>["auto","contain","none"],U=()=>["auto","hidden","clip","visible","scroll"],Z=()=>["auto",X,t],M=()=>[X,t],W=()=>["",sr,jr],V=()=>["auto",ti,X],q=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],Q=()=>["solid","dashed","dotted","double","none"],de=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],D=()=>["start","end","center","between","around","evenly","stretch"],z=()=>["","0",X],H=()=>["auto","avoid","all","avoid-page","page","left","right","column"],K=()=>[ti,X];return{cacheSize:500,separator:":",theme:{colors:[$i],spacing:[sr,jr],blur:["none","",Nr,X],brightness:K(),borderColor:[e],borderRadius:["none","","full",Nr,X],borderSpacing:M(),borderWidth:W(),contrast:K(),grayscale:z(),hueRotate:K(),invert:z(),gap:M(),gradientColorStops:[e],gradientColorStopPositions:[e2,jr],inset:Z(),margin:Z(),opacity:K(),padding:M(),saturate:K(),scale:K(),sepia:z(),skew:K(),space:M(),translate:M()},classGroups:{aspect:[{aspect:["auto","square","video",X]}],container:["container"],columns:[{columns:[Nr]}],"break-after":[{"break-after":H()}],"break-before":[{"break-before":H()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...q(),X]}],overflow:[{overflow:U()}],"overflow-x":[{"overflow-x":U()}],"overflow-y":[{"overflow-y":U()}],overscroll:[{overscroll:L()}],"overscroll-x":[{"overscroll-x":L()}],"overscroll-y":[{"overscroll-y":L()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Ui,X]}],basis:[{basis:Z()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",X]}],grow:[{grow:z()}],shrink:[{shrink:z()}],order:[{order:["first","last","none",Ui,X]}],"grid-cols":[{"grid-cols":[$i]}],"col-start-end":[{col:["auto",{span:["full",Ui,X]},X]}],"col-start":[{"col-start":V()}],"col-end":[{"col-end":V()}],"grid-rows":[{"grid-rows":[$i]}],"row-start-end":[{row:["auto",{span:[Ui,X]},X]}],"row-start":[{"row-start":V()}],"row-end":[{"row-end":V()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",X]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",X]}],gap:[{gap:[h]}],"gap-x":[{"gap-x":[h]}],"gap-y":[{"gap-y":[h]}],"justify-content":[{justify:["normal",...D()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...D(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...D(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[p]}],px:[{px:[p]}],py:[{py:[p]}],ps:[{ps:[p]}],pe:[{pe:[p]}],pt:[{pt:[p]}],pr:[{pr:[p]}],pb:[{pb:[p]}],pl:[{pl:[p]}],m:[{m:[x]}],mx:[{mx:[x]}],my:[{my:[x]}],ms:[{ms:[x]}],me:[{me:[x]}],mt:[{mt:[x]}],mr:[{mr:[x]}],mb:[{mb:[x]}],ml:[{ml:[x]}],"space-x":[{"space-x":[P]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[P]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",X,t]}],"min-w":[{"min-w":[X,t,"min","max","fit"]}],"max-w":[{"max-w":[X,t,"none","full","min","max","fit","prose",{screen:[Nr]},Nr]}],h:[{h:[X,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[X,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[X,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[X,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Nr,jr]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Dl]}],"font-family":[{font:[$i]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",X]}],"line-clamp":[{"line-clamp":["none",ti,Dl]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",sr,X]}],"list-image":[{"list-image":["none",X]}],"list-style-type":[{list:["none","disc","decimal",X]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[m]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[m]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Q(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",sr,jr]}],"underline-offset":[{"underline-offset":["auto",sr,X]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:M()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",X]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",X]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[m]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...q(),n2]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",r2]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},a2]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[g]}],"gradient-via-pos":[{via:[g]}],"gradient-to-pos":[{to:[g]}],"gradient-from":[{from:[w]}],"gradient-via":[{via:[w]}],"gradient-to":[{to:[w]}],rounded:[{rounded:[a]}],"rounded-s":[{"rounded-s":[a]}],"rounded-e":[{"rounded-e":[a]}],"rounded-t":[{"rounded-t":[a]}],"rounded-r":[{"rounded-r":[a]}],"rounded-b":[{"rounded-b":[a]}],"rounded-l":[{"rounded-l":[a]}],"rounded-ss":[{"rounded-ss":[a]}],"rounded-se":[{"rounded-se":[a]}],"rounded-ee":[{"rounded-ee":[a]}],"rounded-es":[{"rounded-es":[a]}],"rounded-tl":[{"rounded-tl":[a]}],"rounded-tr":[{"rounded-tr":[a]}],"rounded-br":[{"rounded-br":[a]}],"rounded-bl":[{"rounded-bl":[a]}],"border-w":[{border:[o]}],"border-w-x":[{"border-x":[o]}],"border-w-y":[{"border-y":[o]}],"border-w-s":[{"border-s":[o]}],"border-w-e":[{"border-e":[o]}],"border-w-t":[{"border-t":[o]}],"border-w-r":[{"border-r":[o]}],"border-w-b":[{"border-b":[o]}],"border-w-l":[{"border-l":[o]}],"border-opacity":[{"border-opacity":[m]}],"border-style":[{border:[...Q(),"hidden"]}],"divide-x":[{"divide-x":[o]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[o]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[m]}],"divide-style":[{divide:Q()}],"border-color":[{border:[i]}],"border-color-x":[{"border-x":[i]}],"border-color-y":[{"border-y":[i]}],"border-color-s":[{"border-s":[i]}],"border-color-e":[{"border-e":[i]}],"border-color-t":[{"border-t":[i]}],"border-color-r":[{"border-r":[i]}],"border-color-b":[{"border-b":[i]}],"border-color-l":[{"border-l":[i]}],"divide-color":[{divide:[i]}],"outline-style":[{outline:["",...Q()]}],"outline-offset":[{"outline-offset":[sr,X]}],"outline-w":[{outline:[sr,jr]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:W()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[m]}],"ring-offset-w":[{"ring-offset":[sr,jr]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Nr,s2]}],"shadow-color":[{shadow:[$i]}],opacity:[{opacity:[m]}],"mix-blend":[{"mix-blend":[...de(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":de()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[c]}],"drop-shadow":[{"drop-shadow":["","none",Nr,X]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[f]}],saturate:[{saturate:[v]}],sepia:[{sepia:[_]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[c]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[f]}],"backdrop-opacity":[{"backdrop-opacity":[m]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[_]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",X]}],duration:[{duration:K()}],ease:[{ease:["linear","in","out","in-out",X]}],delay:[{delay:K()}],animate:[{animate:["none","spin","ping","pulse","bounce",X]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[k]}],"scale-x":[{"scale-x":[k]}],"scale-y":[{"scale-y":[k]}],rotate:[{rotate:[Ui,X]}],"translate-x":[{"translate-x":[O]}],"translate-y":[{"translate-y":[O]}],"skew-x":[{"skew-x":[A]}],"skew-y":[{"skew-y":[A]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",X]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",X]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":M()}],"scroll-mx":[{"scroll-mx":M()}],"scroll-my":[{"scroll-my":M()}],"scroll-ms":[{"scroll-ms":M()}],"scroll-me":[{"scroll-me":M()}],"scroll-mt":[{"scroll-mt":M()}],"scroll-mr":[{"scroll-mr":M()}],"scroll-mb":[{"scroll-mb":M()}],"scroll-ml":[{"scroll-ml":M()}],"scroll-p":[{"scroll-p":M()}],"scroll-px":[{"scroll-px":M()}],"scroll-py":[{"scroll-py":M()}],"scroll-ps":[{"scroll-ps":M()}],"scroll-pe":[{"scroll-pe":M()}],"scroll-pt":[{"scroll-pt":M()}],"scroll-pr":[{"scroll-pr":M()}],"scroll-pb":[{"scroll-pb":M()}],"scroll-pl":[{"scroll-pl":M()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",X]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[sr,jr,Dl]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},d2=HS(u2);function lo(e){"@babel/helpers - typeof";return lo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},lo(e)}function yr(e){if(e===null||e===!0||e===!1)return NaN;var t=Number(e);return isNaN(t)?t:t<0?Math.ceil(t):Math.floor(t)}function oe(e,t){if(t.length<e)throw new TypeError(e+" argument"+(e>1?"s":"")+" required, but only "+t.length+" present")}function fe(e){oe(1,arguments);var t=Object.prototype.toString.call(e);return e instanceof Date||lo(e)==="object"&&t==="[object Date]"?new Date(e.getTime()):typeof e=="number"||t==="[object Number]"?new Date(e):((typeof e=="string"||t==="[object String]")&&typeof console<"u"&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(new Error().stack)),new Date(NaN))}function f2(e,t){oe(2,arguments);var r=fe(e),n=yr(t);return isNaN(n)?new Date(NaN):(n&&r.setDate(r.getDate()+n),r)}function p2(e,t){oe(2,arguments);var r=fe(e).getTime(),n=yr(t);return new Date(r+n)}var m2={};function Ma(){return m2}function Qc(e){var t=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return t.setUTCFullYear(e.getFullYear()),e.getTime()-t.getTime()}function yp(e){oe(1,arguments);var t=fe(e);return t.setHours(0,0,0,0),t}function Rs(e,t){oe(2,arguments);var r=fe(e),n=fe(t),i=r.getTime()-n.getTime();return i<0?-1:i>0?1:i}function wv(e,t){oe(2,arguments);var r=yp(e),n=yp(t);return r.getTime()===n.getTime()}function h2(e){return oe(1,arguments),e instanceof Date||lo(e)==="object"&&Object.prototype.toString.call(e)==="[object Date]"}function g2(e){if(oe(1,arguments),!h2(e)&&typeof e!="number")return!1;var t=fe(e);return!isNaN(Number(t))}function v2(e,t){oe(2,arguments);var r=fe(e),n=fe(t),i=r.getFullYear()-n.getFullYear(),a=r.getMonth()-n.getMonth();return i*12+a}function y2(e,t){return oe(2,arguments),fe(e).getTime()-fe(t).getTime()}var xp={ceil:Math.ceil,round:Math.round,floor:Math.floor,trunc:function(t){return t<0?Math.ceil(t):Math.floor(t)}},x2="trunc";function w2(e){return e?xp[e]:xp[x2]}function b2(e){oe(1,arguments);var t=fe(e);return t.setHours(23,59,59,999),t}function k2(e){oe(1,arguments);var t=fe(e),r=t.getMonth();return t.setFullYear(t.getFullYear(),r+1,0),t.setHours(23,59,59,999),t}function S2(e){oe(1,arguments);var t=fe(e);return b2(t).getTime()===k2(t).getTime()}function E2(e,t){oe(2,arguments);var r=fe(e),n=fe(t),i=Rs(r,n),a=Math.abs(v2(r,n)),s;if(a<1)s=0;else{r.getMonth()===1&&r.getDate()>27&&r.setDate(30),r.setMonth(r.getMonth()-i*a);var o=Rs(r,n)===-i;S2(fe(e))&&a===1&&Rs(e,n)===1&&(o=!1),s=i*(a-Number(o))}return s===0?0:s}function j2(e,t,r){oe(2,arguments);var n=y2(e,t)/1e3;return w2(r==null?void 0:r.roundingMethod)(n)}function N2(e,t){oe(2,arguments);var r=yr(t);return p2(e,-r)}var C2=864e5;function _2(e){oe(1,arguments);var t=fe(e),r=t.getTime();t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0);var n=t.getTime(),i=r-n;return Math.floor(i/C2)+1}function co(e){oe(1,arguments);var t=1,r=fe(e),n=r.getUTCDay(),i=(n<t?7:0)+n-t;return r.setUTCDate(r.getUTCDate()-i),r.setUTCHours(0,0,0,0),r}function bv(e){oe(1,arguments);var t=fe(e),r=t.getUTCFullYear(),n=new Date(0);n.setUTCFullYear(r+1,0,4),n.setUTCHours(0,0,0,0);var i=co(n),a=new Date(0);a.setUTCFullYear(r,0,4),a.setUTCHours(0,0,0,0);var s=co(a);return t.getTime()>=i.getTime()?r+1:t.getTime()>=s.getTime()?r:r-1}function T2(e){oe(1,arguments);var t=bv(e),r=new Date(0);r.setUTCFullYear(t,0,4),r.setUTCHours(0,0,0,0);var n=co(r);return n}var P2=6048e5;function O2(e){oe(1,arguments);var t=fe(e),r=co(t).getTime()-T2(t).getTime();return Math.round(r/P2)+1}function pi(e,t){var r,n,i,a,s,o,c,u;oe(1,arguments);var d=Ma(),f=yr((r=(n=(i=(a=t==null?void 0:t.weekStartsOn)!==null&&a!==void 0?a:t==null||(s=t.locale)===null||s===void 0||(o=s.options)===null||o===void 0?void 0:o.weekStartsOn)!==null&&i!==void 0?i:d.weekStartsOn)!==null&&n!==void 0?n:(c=d.locale)===null||c===void 0||(u=c.options)===null||u===void 0?void 0:u.weekStartsOn)!==null&&r!==void 0?r:0);if(!(f>=0&&f<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var h=fe(e),w=h.getUTCDay(),g=(w<f?7:0)+w-f;return h.setUTCDate(h.getUTCDate()-g),h.setUTCHours(0,0,0,0),h}function kv(e,t){var r,n,i,a,s,o,c,u;oe(1,arguments);var d=fe(e),f=d.getUTCFullYear(),h=Ma(),w=yr((r=(n=(i=(a=t==null?void 0:t.firstWeekContainsDate)!==null&&a!==void 0?a:t==null||(s=t.locale)===null||s===void 0||(o=s.options)===null||o===void 0?void 0:o.firstWeekContainsDate)!==null&&i!==void 0?i:h.firstWeekContainsDate)!==null&&n!==void 0?n:(c=h.locale)===null||c===void 0||(u=c.options)===null||u===void 0?void 0:u.firstWeekContainsDate)!==null&&r!==void 0?r:1);if(!(w>=1&&w<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var g=new Date(0);g.setUTCFullYear(f+1,0,w),g.setUTCHours(0,0,0,0);var y=pi(g,t),x=new Date(0);x.setUTCFullYear(f,0,w),x.setUTCHours(0,0,0,0);var m=pi(x,t);return d.getTime()>=y.getTime()?f+1:d.getTime()>=m.getTime()?f:f-1}function R2(e,t){var r,n,i,a,s,o,c,u;oe(1,arguments);var d=Ma(),f=yr((r=(n=(i=(a=t==null?void 0:t.firstWeekContainsDate)!==null&&a!==void 0?a:t==null||(s=t.locale)===null||s===void 0||(o=s.options)===null||o===void 0?void 0:o.firstWeekContainsDate)!==null&&i!==void 0?i:d.firstWeekContainsDate)!==null&&n!==void 0?n:(c=d.locale)===null||c===void 0||(u=c.options)===null||u===void 0?void 0:u.firstWeekContainsDate)!==null&&r!==void 0?r:1),h=kv(e,t),w=new Date(0);w.setUTCFullYear(h,0,f),w.setUTCHours(0,0,0,0);var g=pi(w,t);return g}var A2=6048e5;function D2(e,t){oe(1,arguments);var r=fe(e),n=pi(r,t).getTime()-R2(r,t).getTime();return Math.round(n/A2)+1}function ce(e,t){for(var r=e<0?"-":"",n=Math.abs(e).toString();n.length<t;)n="0"+n;return r+n}var L2={y:function(t,r){var n=t.getUTCFullYear(),i=n>0?n:1-n;return ce(r==="yy"?i%100:i,r.length)},M:function(t,r){var n=t.getUTCMonth();return r==="M"?String(n+1):ce(n+1,2)},d:function(t,r){return ce(t.getUTCDate(),r.length)},a:function(t,r){var n=t.getUTCHours()/12>=1?"pm":"am";switch(r){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];case"aaaa":default:return n==="am"?"a.m.":"p.m."}},h:function(t,r){return ce(t.getUTCHours()%12||12,r.length)},H:function(t,r){return ce(t.getUTCHours(),r.length)},m:function(t,r){return ce(t.getUTCMinutes(),r.length)},s:function(t,r){return ce(t.getUTCSeconds(),r.length)},S:function(t,r){var n=r.length,i=t.getUTCMilliseconds(),a=Math.floor(i*Math.pow(10,n-3));return ce(a,r.length)}};const Cr=L2;var Dn={am:"am",pm:"pm",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},M2={G:function(t,r,n){var i=t.getUTCFullYear()>0?1:0;switch(r){case"G":case"GG":case"GGG":return n.era(i,{width:"abbreviated"});case"GGGGG":return n.era(i,{width:"narrow"});case"GGGG":default:return n.era(i,{width:"wide"})}},y:function(t,r,n){if(r==="yo"){var i=t.getUTCFullYear(),a=i>0?i:1-i;return n.ordinalNumber(a,{unit:"year"})}return Cr.y(t,r)},Y:function(t,r,n,i){var a=kv(t,i),s=a>0?a:1-a;if(r==="YY"){var o=s%100;return ce(o,2)}return r==="Yo"?n.ordinalNumber(s,{unit:"year"}):ce(s,r.length)},R:function(t,r){var n=bv(t);return ce(n,r.length)},u:function(t,r){var n=t.getUTCFullYear();return ce(n,r.length)},Q:function(t,r,n){var i=Math.ceil((t.getUTCMonth()+1)/3);switch(r){case"Q":return String(i);case"QQ":return ce(i,2);case"Qo":return n.ordinalNumber(i,{unit:"quarter"});case"QQQ":return n.quarter(i,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(i,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(i,{width:"wide",context:"formatting"})}},q:function(t,r,n){var i=Math.ceil((t.getUTCMonth()+1)/3);switch(r){case"q":return String(i);case"qq":return ce(i,2);case"qo":return n.ordinalNumber(i,{unit:"quarter"});case"qqq":return n.quarter(i,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(i,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(i,{width:"wide",context:"standalone"})}},M:function(t,r,n){var i=t.getUTCMonth();switch(r){case"M":case"MM":return Cr.M(t,r);case"Mo":return n.ordinalNumber(i+1,{unit:"month"});case"MMM":return n.month(i,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(i,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(i,{width:"wide",context:"formatting"})}},L:function(t,r,n){var i=t.getUTCMonth();switch(r){case"L":return String(i+1);case"LL":return ce(i+1,2);case"Lo":return n.ordinalNumber(i+1,{unit:"month"});case"LLL":return n.month(i,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(i,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(i,{width:"wide",context:"standalone"})}},w:function(t,r,n,i){var a=D2(t,i);return r==="wo"?n.ordinalNumber(a,{unit:"week"}):ce(a,r.length)},I:function(t,r,n){var i=O2(t);return r==="Io"?n.ordinalNumber(i,{unit:"week"}):ce(i,r.length)},d:function(t,r,n){return r==="do"?n.ordinalNumber(t.getUTCDate(),{unit:"date"}):Cr.d(t,r)},D:function(t,r,n){var i=_2(t);return r==="Do"?n.ordinalNumber(i,{unit:"dayOfYear"}):ce(i,r.length)},E:function(t,r,n){var i=t.getUTCDay();switch(r){case"E":case"EE":case"EEE":return n.day(i,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(i,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(i,{width:"short",context:"formatting"});case"EEEE":default:return n.day(i,{width:"wide",context:"formatting"})}},e:function(t,r,n,i){var a=t.getUTCDay(),s=(a-i.weekStartsOn+8)%7||7;switch(r){case"e":return String(s);case"ee":return ce(s,2);case"eo":return n.ordinalNumber(s,{unit:"day"});case"eee":return n.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(a,{width:"short",context:"formatting"});case"eeee":default:return n.day(a,{width:"wide",context:"formatting"})}},c:function(t,r,n,i){var a=t.getUTCDay(),s=(a-i.weekStartsOn+8)%7||7;switch(r){case"c":return String(s);case"cc":return ce(s,r.length);case"co":return n.ordinalNumber(s,{unit:"day"});case"ccc":return n.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(a,{width:"narrow",context:"standalone"});case"cccccc":return n.day(a,{width:"short",context:"standalone"});case"cccc":default:return n.day(a,{width:"wide",context:"standalone"})}},i:function(t,r,n){var i=t.getUTCDay(),a=i===0?7:i;switch(r){case"i":return String(a);case"ii":return ce(a,r.length);case"io":return n.ordinalNumber(a,{unit:"day"});case"iii":return n.day(i,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(i,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(i,{width:"short",context:"formatting"});case"iiii":default:return n.day(i,{width:"wide",context:"formatting"})}},a:function(t,r,n){var i=t.getUTCHours(),a=i/12>=1?"pm":"am";switch(r){case"a":case"aa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(t,r,n){var i=t.getUTCHours(),a;switch(i===12?a=Dn.noon:i===0?a=Dn.midnight:a=i/12>=1?"pm":"am",r){case"b":case"bb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(t,r,n){var i=t.getUTCHours(),a;switch(i>=17?a=Dn.evening:i>=12?a=Dn.afternoon:i>=4?a=Dn.morning:a=Dn.night,r){case"B":case"BB":case"BBB":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(t,r,n){if(r==="ho"){var i=t.getUTCHours()%12;return i===0&&(i=12),n.ordinalNumber(i,{unit:"hour"})}return Cr.h(t,r)},H:function(t,r,n){return r==="Ho"?n.ordinalNumber(t.getUTCHours(),{unit:"hour"}):Cr.H(t,r)},K:function(t,r,n){var i=t.getUTCHours()%12;return r==="Ko"?n.ordinalNumber(i,{unit:"hour"}):ce(i,r.length)},k:function(t,r,n){var i=t.getUTCHours();return i===0&&(i=24),r==="ko"?n.ordinalNumber(i,{unit:"hour"}):ce(i,r.length)},m:function(t,r,n){return r==="mo"?n.ordinalNumber(t.getUTCMinutes(),{unit:"minute"}):Cr.m(t,r)},s:function(t,r,n){return r==="so"?n.ordinalNumber(t.getUTCSeconds(),{unit:"second"}):Cr.s(t,r)},S:function(t,r){return Cr.S(t,r)},X:function(t,r,n,i){var a=i._originalDate||t,s=a.getTimezoneOffset();if(s===0)return"Z";switch(r){case"X":return bp(s);case"XXXX":case"XX":return cn(s);case"XXXXX":case"XXX":default:return cn(s,":")}},x:function(t,r,n,i){var a=i._originalDate||t,s=a.getTimezoneOffset();switch(r){case"x":return bp(s);case"xxxx":case"xx":return cn(s);case"xxxxx":case"xxx":default:return cn(s,":")}},O:function(t,r,n,i){var a=i._originalDate||t,s=a.getTimezoneOffset();switch(r){case"O":case"OO":case"OOO":return"GMT"+wp(s,":");case"OOOO":default:return"GMT"+cn(s,":")}},z:function(t,r,n,i){var a=i._originalDate||t,s=a.getTimezoneOffset();switch(r){case"z":case"zz":case"zzz":return"GMT"+wp(s,":");case"zzzz":default:return"GMT"+cn(s,":")}},t:function(t,r,n,i){var a=i._originalDate||t,s=Math.floor(a.getTime()/1e3);return ce(s,r.length)},T:function(t,r,n,i){var a=i._originalDate||t,s=a.getTime();return ce(s,r.length)}};function wp(e,t){var r=e>0?"-":"+",n=Math.abs(e),i=Math.floor(n/60),a=n%60;if(a===0)return r+String(i);var s=t||"";return r+String(i)+s+ce(a,2)}function bp(e,t){if(e%60===0){var r=e>0?"-":"+";return r+ce(Math.abs(e)/60,2)}return cn(e,t)}function cn(e,t){var r=t||"",n=e>0?"-":"+",i=Math.abs(e),a=ce(Math.floor(i/60),2),s=ce(i%60,2);return n+a+r+s}const F2=M2;var kp=function(t,r){switch(t){case"P":return r.date({width:"short"});case"PP":return r.date({width:"medium"});case"PPP":return r.date({width:"long"});case"PPPP":default:return r.date({width:"full"})}},Sv=function(t,r){switch(t){case"p":return r.time({width:"short"});case"pp":return r.time({width:"medium"});case"ppp":return r.time({width:"long"});case"pppp":default:return r.time({width:"full"})}},z2=function(t,r){var n=t.match(/(P+)(p+)?/)||[],i=n[1],a=n[2];if(!a)return kp(t,r);var s;switch(i){case"P":s=r.dateTime({width:"short"});break;case"PP":s=r.dateTime({width:"medium"});break;case"PPP":s=r.dateTime({width:"long"});break;case"PPPP":default:s=r.dateTime({width:"full"});break}return s.replace("{{date}}",kp(i,r)).replace("{{time}}",Sv(a,r))},I2={p:Sv,P:z2};const U2=I2;var $2=["D","DD"],B2=["YY","YYYY"];function V2(e){return $2.indexOf(e)!==-1}function W2(e){return B2.indexOf(e)!==-1}function Sp(e,t,r){if(e==="YYYY")throw new RangeError("Use `yyyy` instead of `YYYY` (in `".concat(t,"`) for formatting years to the input `").concat(r,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(e==="YY")throw new RangeError("Use `yy` instead of `YY` (in `".concat(t,"`) for formatting years to the input `").concat(r,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(e==="D")throw new RangeError("Use `d` instead of `D` (in `".concat(t,"`) for formatting days of the month to the input `").concat(r,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(e==="DD")throw new RangeError("Use `dd` instead of `DD` (in `".concat(t,"`) for formatting days of the month to the input `").concat(r,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"))}var q2={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},H2=function(t,r,n){var i,a=q2[t];return typeof a=="string"?i=a:r===1?i=a.one:i=a.other.replace("{{count}}",r.toString()),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?"in "+i:i+" ago":i};const Y2=H2;function ri(e){return function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=t.width?String(t.width):e.defaultWidth,n=e.formats[r]||e.formats[e.defaultWidth];return n}}var K2={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Q2={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},G2={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},X2={date:ri({formats:K2,defaultWidth:"full"}),time:ri({formats:Q2,defaultWidth:"full"}),dateTime:ri({formats:G2,defaultWidth:"full"})};const J2=X2;var Z2={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},eE=function(t,r,n,i){return Z2[t]};const tE=eE;function Gt(e){return function(t,r){var n=r!=null&&r.context?String(r.context):"standalone",i;if(n==="formatting"&&e.formattingValues){var a=e.defaultFormattingWidth||e.defaultWidth,s=r!=null&&r.width?String(r.width):a;i=e.formattingValues[s]||e.formattingValues[a]}else{var o=e.defaultWidth,c=r!=null&&r.width?String(r.width):e.defaultWidth;i=e.values[c]||e.values[o]}var u=e.argumentCallback?e.argumentCallback(t):t;return i[u]}}var rE={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},nE={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},iE={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},aE={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},sE={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},oE={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},lE=function(t,r){var n=Number(t),i=n%100;if(i>20||i<10)switch(i%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},cE={ordinalNumber:lE,era:Gt({values:rE,defaultWidth:"wide"}),quarter:Gt({values:nE,defaultWidth:"wide",argumentCallback:function(t){return t-1}}),month:Gt({values:iE,defaultWidth:"wide"}),day:Gt({values:aE,defaultWidth:"wide"}),dayPeriod:Gt({values:sE,defaultWidth:"wide",formattingValues:oE,defaultFormattingWidth:"wide"})};const uE=cE;function Xt(e){return function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=r.width,i=n&&e.matchPatterns[n]||e.matchPatterns[e.defaultMatchWidth],a=t.match(i);if(!a)return null;var s=a[0],o=n&&e.parsePatterns[n]||e.parsePatterns[e.defaultParseWidth],c=Array.isArray(o)?fE(o,function(f){return f.test(s)}):dE(o,function(f){return f.test(s)}),u;u=e.valueCallback?e.valueCallback(c):c,u=r.valueCallback?r.valueCallback(u):u;var d=t.slice(s.length);return{value:u,rest:d}}}function dE(e,t){for(var r in e)if(e.hasOwnProperty(r)&&t(e[r]))return r}function fE(e,t){for(var r=0;r<e.length;r++)if(t(e[r]))return r}function Ev(e){return function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.match(e.matchPattern);if(!n)return null;var i=n[0],a=t.match(e.parsePattern);if(!a)return null;var s=e.valueCallback?e.valueCallback(a[0]):a[0];s=r.valueCallback?r.valueCallback(s):s;var o=t.slice(i.length);return{value:s,rest:o}}}var pE=/^(\d+)(th|st|nd|rd)?/i,mE=/\d+/i,hE={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},gE={any:[/^b/i,/^(a|c)/i]},vE={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},yE={any:[/1/i,/2/i,/3/i,/4/i]},xE={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},wE={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},bE={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},kE={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},SE={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},EE={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},jE={ordinalNumber:Ev({matchPattern:pE,parsePattern:mE,valueCallback:function(t){return parseInt(t,10)}}),era:Xt({matchPatterns:hE,defaultMatchWidth:"wide",parsePatterns:gE,defaultParseWidth:"any"}),quarter:Xt({matchPatterns:vE,defaultMatchWidth:"wide",parsePatterns:yE,defaultParseWidth:"any",valueCallback:function(t){return t+1}}),month:Xt({matchPatterns:xE,defaultMatchWidth:"wide",parsePatterns:wE,defaultParseWidth:"any"}),day:Xt({matchPatterns:bE,defaultMatchWidth:"wide",parsePatterns:kE,defaultParseWidth:"any"}),dayPeriod:Xt({matchPatterns:SE,defaultMatchWidth:"any",parsePatterns:EE,defaultParseWidth:"any"})};const NE=jE;var CE={code:"en-US",formatDistance:Y2,formatLong:J2,formatRelative:tE,localize:uE,match:NE,options:{weekStartsOn:0,firstWeekContainsDate:1}};const Yo=CE;var _E=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,TE=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,PE=/^'([^]*?)'?$/,OE=/''/g,RE=/[a-zA-Z]/;function Ep(e,t,r){var n,i,a,s,o,c,u,d,f,h,w,g,y,x,m,p,v,k;oe(2,arguments);var _=String(t),A=Ma(),P=(n=(i=r==null?void 0:r.locale)!==null&&i!==void 0?i:A.locale)!==null&&n!==void 0?n:Yo,O=yr((a=(s=(o=(c=r==null?void 0:r.firstWeekContainsDate)!==null&&c!==void 0?c:r==null||(u=r.locale)===null||u===void 0||(d=u.options)===null||d===void 0?void 0:d.firstWeekContainsDate)!==null&&o!==void 0?o:A.firstWeekContainsDate)!==null&&s!==void 0?s:(f=A.locale)===null||f===void 0||(h=f.options)===null||h===void 0?void 0:h.firstWeekContainsDate)!==null&&a!==void 0?a:1);if(!(O>=1&&O<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var L=yr((w=(g=(y=(x=r==null?void 0:r.weekStartsOn)!==null&&x!==void 0?x:r==null||(m=r.locale)===null||m===void 0||(p=m.options)===null||p===void 0?void 0:p.weekStartsOn)!==null&&y!==void 0?y:A.weekStartsOn)!==null&&g!==void 0?g:(v=A.locale)===null||v===void 0||(k=v.options)===null||k===void 0?void 0:k.weekStartsOn)!==null&&w!==void 0?w:0);if(!(L>=0&&L<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!P.localize)throw new RangeError("locale must contain localize property");if(!P.formatLong)throw new RangeError("locale must contain formatLong property");var U=fe(e);if(!g2(U))throw new RangeError("Invalid time value");var Z=Qc(U),M=N2(U,Z),W={firstWeekContainsDate:O,weekStartsOn:L,locale:P,_originalDate:U},V=_.match(TE).map(function(q){var Q=q[0];if(Q==="p"||Q==="P"){var de=U2[Q];return de(q,P.formatLong)}return q}).join("").match(_E).map(function(q){if(q==="''")return"'";var Q=q[0];if(Q==="'")return AE(q);var de=F2[Q];if(de)return!(r!=null&&r.useAdditionalWeekYearTokens)&&W2(q)&&Sp(q,t,String(e)),!(r!=null&&r.useAdditionalDayOfYearTokens)&&V2(q)&&Sp(q,t,String(e)),de(M,q,P.localize,W);if(Q.match(RE))throw new RangeError("Format string contains an unescaped latin alphabet character `"+Q+"`");return q}).join("");return V}function AE(e){var t=e.match(PE);return t?t[1].replace(OE,"'"):e}function jv(e,t){if(e==null)throw new TypeError("assign requires that input parameter not be null or undefined");for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}function DE(e){return jv({},e)}var jp=1440,LE=2520,Ll=43200,ME=86400;function FE(e,t,r){var n,i;oe(2,arguments);var a=Ma(),s=(n=(i=r==null?void 0:r.locale)!==null&&i!==void 0?i:a.locale)!==null&&n!==void 0?n:Yo;if(!s.formatDistance)throw new RangeError("locale must contain formatDistance property");var o=Rs(e,t);if(isNaN(o))throw new RangeError("Invalid time value");var c=jv(DE(r),{addSuffix:!!(r!=null&&r.addSuffix),comparison:o}),u,d;o>0?(u=fe(t),d=fe(e)):(u=fe(e),d=fe(t));var f=j2(d,u),h=(Qc(d)-Qc(u))/1e3,w=Math.round((f-h)/60),g;if(w<2)return r!=null&&r.includeSeconds?f<5?s.formatDistance("lessThanXSeconds",5,c):f<10?s.formatDistance("lessThanXSeconds",10,c):f<20?s.formatDistance("lessThanXSeconds",20,c):f<40?s.formatDistance("halfAMinute",0,c):f<60?s.formatDistance("lessThanXMinutes",1,c):s.formatDistance("xMinutes",1,c):w===0?s.formatDistance("lessThanXMinutes",1,c):s.formatDistance("xMinutes",w,c);if(w<45)return s.formatDistance("xMinutes",w,c);if(w<90)return s.formatDistance("aboutXHours",1,c);if(w<jp){var y=Math.round(w/60);return s.formatDistance("aboutXHours",y,c)}else{if(w<LE)return s.formatDistance("xDays",1,c);if(w<Ll){var x=Math.round(w/jp);return s.formatDistance("xDays",x,c)}else if(w<ME)return g=Math.round(w/Ll),s.formatDistance("aboutXMonths",g,c)}if(g=E2(d,u),g<12){var m=Math.round(w/Ll);return s.formatDistance("xMonths",m,c)}else{var p=g%12,v=Math.floor(g/12);return p<3?s.formatDistance("aboutXYears",v,c):p<9?s.formatDistance("overXYears",v,c):s.formatDistance("almostXYears",v+1,c)}}function zE(e,t){return oe(1,arguments),FE(e,Date.now(),t)}function Nv(e){return oe(1,arguments),wv(e,Date.now())}function IE(e,t){oe(2,arguments);var r=yr(t);return f2(e,-r)}function Cv(e){return oe(1,arguments),wv(e,IE(Date.now(),1))}function UE(e,t,r){oe(2,arguments);var n=pi(e,r),i=pi(t,r);return n.getTime()===i.getTime()}var $E={lessThanXSeconds:{one:"不到 1 秒",other:"不到 {{count}} 秒"},xSeconds:{one:"1 秒",other:"{{count}} 秒"},halfAMinute:"半分钟",lessThanXMinutes:{one:"不到 1 分钟",other:"不到 {{count}} 分钟"},xMinutes:{one:"1 分钟",other:"{{count}} 分钟"},xHours:{one:"1 小时",other:"{{count}} 小时"},aboutXHours:{one:"大约 1 小时",other:"大约 {{count}} 小时"},xDays:{one:"1 天",other:"{{count}} 天"},aboutXWeeks:{one:"大约 1 个星期",other:"大约 {{count}} 个星期"},xWeeks:{one:"1 个星期",other:"{{count}} 个星期"},aboutXMonths:{one:"大约 1 个月",other:"大约 {{count}} 个月"},xMonths:{one:"1 个月",other:"{{count}} 个月"},aboutXYears:{one:"大约 1 年",other:"大约 {{count}} 年"},xYears:{one:"1 年",other:"{{count}} 年"},overXYears:{one:"超过 1 年",other:"超过 {{count}} 年"},almostXYears:{one:"将近 1 年",other:"将近 {{count}} 年"}},BE=function(t,r,n){var i,a=$E[t];return typeof a=="string"?i=a:r===1?i=a.one:i=a.other.replace("{{count}}",String(r)),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?i+"内":i+"前":i};const VE=BE;var WE={full:"y'年'M'月'd'日' EEEE",long:"y'年'M'月'd'日'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},qE={full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},HE={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},YE={date:ri({formats:WE,defaultWidth:"full"}),time:ri({formats:qE,defaultWidth:"full"}),dateTime:ri({formats:HE,defaultWidth:"full"})};const KE=YE;function Np(e,t,r){var n="eeee p";return UE(e,t,r)?n:e.getTime()>t.getTime()?"'下个'"+n:"'上个'"+n}var QE={lastWeek:Np,yesterday:"'昨天' p",today:"'今天' p",tomorrow:"'明天' p",nextWeek:Np,other:"PP p"},GE=function(t,r,n,i){var a=QE[t];return typeof a=="function"?a(r,n,i):a};const XE=GE;var JE={narrow:["前","公元"],abbreviated:["前","公元"],wide:["公元前","公元"]},ZE={narrow:["1","2","3","4"],abbreviated:["第一季","第二季","第三季","第四季"],wide:["第一季度","第二季度","第三季度","第四季度"]},ej={narrow:["一","二","三","四","五","六","七","八","九","十","十一","十二"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},tj={narrow:["日","一","二","三","四","五","六"],short:["日","一","二","三","四","五","六"],abbreviated:["周日","周一","周二","周三","周四","周五","周六"],wide:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"]},rj={narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},nj={narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},ij=function(t,r){var n=Number(t);switch(r==null?void 0:r.unit){case"date":return n.toString()+"日";case"hour":return n.toString()+"时";case"minute":return n.toString()+"分";case"second":return n.toString()+"秒";default:return"第 "+n.toString()}},aj={ordinalNumber:ij,era:Gt({values:JE,defaultWidth:"wide"}),quarter:Gt({values:ZE,defaultWidth:"wide",argumentCallback:function(t){return t-1}}),month:Gt({values:ej,defaultWidth:"wide"}),day:Gt({values:tj,defaultWidth:"wide"}),dayPeriod:Gt({values:rj,defaultWidth:"wide",formattingValues:nj,defaultFormattingWidth:"wide"})};const sj=aj;var oj=/^(第\s*)?\d+(日|时|分|秒)?/i,lj=/\d+/i,cj={narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},uj={any:[/^(前)/i,/^(公元)/i]},dj={narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻钟/i},fj={any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},pj={narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},mj={narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},hj={narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^周[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},gj={any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},vj={any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨|)/i},yj={any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},xj={ordinalNumber:Ev({matchPattern:oj,parsePattern:lj,valueCallback:function(t){return parseInt(t,10)}}),era:Xt({matchPatterns:cj,defaultMatchWidth:"wide",parsePatterns:uj,defaultParseWidth:"any"}),quarter:Xt({matchPatterns:dj,defaultMatchWidth:"wide",parsePatterns:fj,defaultParseWidth:"any",valueCallback:function(t){return t+1}}),month:Xt({matchPatterns:pj,defaultMatchWidth:"wide",parsePatterns:mj,defaultParseWidth:"any"}),day:Xt({matchPatterns:hj,defaultMatchWidth:"wide",parsePatterns:gj,defaultParseWidth:"any"}),dayPeriod:Xt({matchPatterns:vj,defaultMatchWidth:"any",parsePatterns:yj,defaultParseWidth:"any"})};const wj=xj;var bj={code:"zh-CN",formatDistance:VE,formatLong:KE,formatRelative:XE,localize:sj,match:wj,options:{weekStartsOn:1,firstWeekContainsDate:4}};const _v=bj;function se(...e){return d2(AS(e))}function Tv(e,t="en"){const r=new Date(e),n=t==="zh"?_v:Yo;return Nv(r)?Ep(r,"HH:mm",{locale:n}):Cv(r)?t==="zh"?"昨天":"Yesterday":Ep(r,"MM/dd",{locale:n})}function kj(e,t="en"){const r=new Date(e),n=t==="zh"?_v:Yo;return Nv(r)?t==="zh"?"今天":"Today":Cv(r)?t==="zh"?"昨天":"Yesterday":zE(r,{addSuffix:!0,locale:n})}function Pv(e){if(e===0)return"0 Bytes";const t=1024,r=["Bytes","KB","MB","GB"],n=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,n)).toFixed(2))+" "+r[n]}function tn(e){return e.split(" ").map(t=>t.charAt(0)).join("").toUpperCase().slice(0,2)}function Sj(e){if(navigator.clipboard&&window.isSecureContext)return navigator.clipboard.writeText(e);{const t=document.createElement("textarea");return t.value=e,t.style.position="fixed",t.style.left="-999999px",t.style.top="-999999px",document.body.appendChild(t),t.focus(),t.select(),new Promise((r,n)=>{document.execCommand("copy")?r():n(new Error("Failed to copy text")),document.body.removeChild(t)})}}function gd(e){var t;return((t=e.split(".").pop())==null?void 0:t.toLowerCase())||""}function Ov(e){return["jpg","jpeg","png","gif","webp","svg"].includes(gd(e))}function Rv(e){return["mp4","avi","mov","wmv","flv","webm"].includes(gd(e))}function Av(e){return["mp3","wav","ogg","aac","flac"].includes(gd(e))}function Fa(e){switch(e){case"online":return"bg-green-500";case"away":return"bg-yellow-500";case"dnd":return"bg-red-500";case"offline":return"bg-gray-400";default:return"bg-gray-400"}}function za(e,t="en"){if(t==="zh")switch(e){case"online":return"在线";case"away":return"离开";case"dnd":return"勿扰";case"offline":return"离线";default:return"离线"}else switch(e){case"online":return"Online";case"away":return"Away";case"dnd":return"Do Not Disturb";case"offline":return"Offline";default:return"Offline"}}function vd({size:e="md",className:t}){const r={sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"};return l.jsx("div",{className:se("flex items-center justify-center",t),children:l.jsx("div",{className:se("animate-spin rounded-full border-2 border-gray-300 border-t-primary-600",r[e])})})}var Ia=e=>e.type==="checkbox",hn=e=>e instanceof Date,tt=e=>e==null;const Dv=e=>typeof e=="object";var Re=e=>!tt(e)&&!Array.isArray(e)&&Dv(e)&&!hn(e),Ej=e=>Re(e)&&e.target?Ia(e.target)?e.target.checked:e.target.value:e,jj=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,Nj=(e,t)=>e.has(jj(t)),Cj=e=>{const t=e.constructor&&e.constructor.prototype;return Re(t)&&t.hasOwnProperty("isPrototypeOf")},yd=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function ze(e){let t;const r=Array.isArray(e),n=typeof FileList<"u"?e instanceof FileList:!1;if(e instanceof Date)t=new Date(e);else if(!(yd&&(e instanceof Blob||n))&&(r||Re(e)))if(t=r?[]:Object.create(Object.getPrototypeOf(e)),!r&&!Cj(e))t=e;else for(const i in e)e.hasOwnProperty(i)&&(t[i]=ze(e[i]));else return e;return t}var Ko=e=>/^\w*$/.test(e),Te=e=>e===void 0,xd=e=>Array.isArray(e)?e.filter(Boolean):[],wd=e=>xd(e.replace(/["|']|\]/g,"").split(/\.|\[/)),B=(e,t,r)=>{if(!t||!Re(e))return r;const n=(Ko(t)?[t]:wd(t)).reduce((i,a)=>tt(i)?i:i[a],e);return Te(n)||n===e?Te(e[t])?r:e[t]:n},Kt=e=>typeof e=="boolean",ye=(e,t,r)=>{let n=-1;const i=Ko(t)?[t]:wd(t),a=i.length,s=a-1;for(;++n<a;){const o=i[n];let c=r;if(n!==s){const u=e[o];c=Re(u)||Array.isArray(u)?u:isNaN(+i[n+1])?{}:[]}if(o==="__proto__"||o==="constructor"||o==="prototype")return;e[o]=c,e=e[o]}};const Cp={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},Mt={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},or={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},_j=Ve.createContext(null);_j.displayName="HookFormContext";var Tj=(e,t,r,n=!0)=>{const i={defaultValues:t._defaultValues};for(const a in e)Object.defineProperty(i,a,{get:()=>{const s=a;return t._proxyFormState[s]!==Mt.all&&(t._proxyFormState[s]=!n||Mt.all),r&&(r[s]=!0),e[s]}});return i};const Pj=typeof window<"u"?Ve.useLayoutEffect:Ve.useEffect;var mt=e=>typeof e=="string",Oj=(e,t,r,n,i)=>mt(e)?(n&&t.watch.add(e),B(r,e,i)):Array.isArray(e)?e.map(a=>(n&&t.watch.add(a),B(r,a))):(n&&(t.watchAll=!0),r),Gc=e=>tt(e)||!Dv(e);function Ar(e,t,r=new WeakSet){if(Gc(e)||Gc(t))return e===t;if(hn(e)&&hn(t))return e.getTime()===t.getTime();const n=Object.keys(e),i=Object.keys(t);if(n.length!==i.length)return!1;if(r.has(e)||r.has(t))return!0;r.add(e),r.add(t);for(const a of n){const s=e[a];if(!i.includes(a))return!1;if(a!=="ref"){const o=t[a];if(hn(s)&&hn(o)||Re(s)&&Re(o)||Array.isArray(s)&&Array.isArray(o)?!Ar(s,o,r):s!==o)return!1}}return!0}var Rj=(e,t,r,n,i)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[n]:i||!0}}:{},na=e=>Array.isArray(e)?e:[e],_p=()=>{let e=[];return{get observers(){return e},next:i=>{for(const a of e)a.next&&a.next(i)},subscribe:i=>(e.push(i),{unsubscribe:()=>{e=e.filter(a=>a!==i)}}),unsubscribe:()=>{e=[]}}};function Lv(e,t){const r={};for(const n in e)if(e.hasOwnProperty(n)){const i=e[n],a=t[n];if(i&&Re(i)&&a){const s=Lv(i,a);Re(s)&&(r[n]=s)}else e[n]&&(r[n]=a)}return r}var st=e=>Re(e)&&!Object.keys(e).length,bd=e=>e.type==="file",Ft=e=>typeof e=="function",uo=e=>{if(!yd)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},Mv=e=>e.type==="select-multiple",kd=e=>e.type==="radio",Aj=e=>kd(e)||Ia(e),Ml=e=>uo(e)&&e.isConnected;function Dj(e,t){const r=t.slice(0,-1).length;let n=0;for(;n<r;)e=Te(e)?n++:e[t[n++]];return e}function Lj(e){for(const t in e)if(e.hasOwnProperty(t)&&!Te(e[t]))return!1;return!0}function _e(e,t){const r=Array.isArray(t)?t:Ko(t)?[t]:wd(t),n=r.length===1?e:Dj(e,r),i=r.length-1,a=r[i];return n&&delete n[a],i!==0&&(Re(n)&&st(n)||Array.isArray(n)&&Lj(n))&&_e(e,r.slice(0,-1)),e}var Mj=e=>{for(const t in e)if(Ft(e[t]))return!0;return!1};function Fv(e){return Array.isArray(e)||Re(e)&&!Mj(e)}function Xc(e,t={}){for(const r in e)Fv(e[r])?(t[r]=Array.isArray(e[r])?[]:{},Xc(e[r],t[r])):tt(e[r])||(t[r]=!0);return t}function Mn(e,t,r){r||(r=Xc(t));for(const n in e)Fv(e[n])?Te(t)||Gc(r[n])?r[n]=Xc(e[n],Array.isArray(e[n])?[]:{}):Mn(e[n],tt(t)?{}:t[n],r[n]):r[n]=!Ar(e[n],t[n]);return r}const Tp={value:!1,isValid:!1},Pp={value:!0,isValid:!0};var zv=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(r=>r&&r.checked&&!r.disabled).map(r=>r.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!Te(e[0].attributes.value)?Te(e[0].value)||e[0].value===""?Pp:{value:e[0].value,isValid:!0}:Pp:Tp}return Tp},Iv=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:n})=>Te(e)?e:t?e===""?NaN:e&&+e:r&&mt(e)?new Date(e):n?n(e):e;const Op={isValid:!1,value:null};var Uv=e=>Array.isArray(e)?e.reduce((t,r)=>r&&r.checked&&!r.disabled?{isValid:!0,value:r.value}:t,Op):Op;function Rp(e){const t=e.ref;return bd(t)?t.files:kd(t)?Uv(e.refs).value:Mv(t)?[...t.selectedOptions].map(({value:r})=>r):Ia(t)?zv(e.refs).value:Iv(Te(t.value)?e.ref.value:t.value,e)}var Fj=(e,t,r,n)=>{const i={};for(const a of e){const s=B(t,a);s&&ye(i,a,s._f)}return{criteriaMode:r,names:[...e],fields:i,shouldUseNativeValidation:n}},fo=e=>e instanceof RegExp,Bi=e=>Te(e)?e:fo(e)?e.source:Re(e)?fo(e.value)?e.value.source:e.value:e,Ap=e=>({isOnSubmit:!e||e===Mt.onSubmit,isOnBlur:e===Mt.onBlur,isOnChange:e===Mt.onChange,isOnAll:e===Mt.all,isOnTouch:e===Mt.onTouched});const Dp="AsyncFunction";var zj=e=>!!e&&!!e.validate&&!!(Ft(e.validate)&&e.validate.constructor.name===Dp||Re(e.validate)&&Object.values(e.validate).find(t=>t.constructor.name===Dp)),Ij=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),Lp=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(n=>e.startsWith(n)&&/^\.\w+/.test(e.slice(n.length))));const ia=(e,t,r,n)=>{for(const i of r||Object.keys(e)){const a=B(e,i);if(a){const{_f:s,...o}=a;if(s){if(s.refs&&s.refs[0]&&t(s.refs[0],i)&&!n)return!0;if(s.ref&&t(s.ref,s.name)&&!n)return!0;if(ia(o,t))break}else if(Re(o)&&ia(o,t))break}}};function Mp(e,t,r){const n=B(e,r);if(n||Ko(r))return{error:n,name:r};const i=r.split(".");for(;i.length;){const a=i.join("."),s=B(t,a),o=B(e,a);if(s&&!Array.isArray(s)&&r!==a)return{name:r};if(o&&o.type)return{name:a,error:o};if(o&&o.root&&o.root.type)return{name:`${a}.root`,error:o.root};i.pop()}return{name:r}}var Uj=(e,t,r,n)=>{r(e);const{name:i,...a}=e;return st(a)||Object.keys(a).length>=Object.keys(t).length||Object.keys(a).find(s=>t[s]===(!n||Mt.all))},$j=(e,t,r)=>!e||!t||e===t||na(e).some(n=>n&&(r?n===t:n.startsWith(t)||t.startsWith(n))),Bj=(e,t,r,n,i)=>i.isOnAll?!1:!r&&i.isOnTouch?!(t||e):(r?n.isOnBlur:i.isOnBlur)?!e:(r?n.isOnChange:i.isOnChange)?e:!0,Vj=(e,t)=>!xd(B(e,t)).length&&_e(e,t),Wj=(e,t,r)=>{const n=na(B(e,r));return ye(n,"root",t[r]),ye(e,r,n),e};function Fp(e,t,r="validate"){if(mt(e)||Array.isArray(e)&&e.every(mt)||Kt(e)&&!e)return{type:r,message:mt(e)?e:"",ref:t}}var Ln=e=>Re(e)&&!fo(e)?e:{value:e,message:""},zp=async(e,t,r,n,i,a)=>{const{ref:s,refs:o,required:c,maxLength:u,minLength:d,min:f,max:h,pattern:w,validate:g,name:y,valueAsNumber:x,mount:m}=e._f,p=B(r,y);if(!m||t.has(y))return{};const v=o?o[0]:s,k=M=>{i&&v.reportValidity&&(v.setCustomValidity(Kt(M)?"":M||""),v.reportValidity())},_={},A=kd(s),P=Ia(s),O=A||P,L=(x||bd(s))&&Te(s.value)&&Te(p)||uo(s)&&s.value===""||p===""||Array.isArray(p)&&!p.length,U=Rj.bind(null,y,n,_),Z=(M,W,V,q=or.maxLength,Q=or.minLength)=>{const de=M?W:V;_[y]={type:M?q:Q,message:de,ref:s,...U(M?q:Q,de)}};if(a?!Array.isArray(p)||!p.length:c&&(!O&&(L||tt(p))||Kt(p)&&!p||P&&!zv(o).isValid||A&&!Uv(o).isValid)){const{value:M,message:W}=mt(c)?{value:!!c,message:c}:Ln(c);if(M&&(_[y]={type:or.required,message:W,ref:v,...U(or.required,W)},!n))return k(W),_}if(!L&&(!tt(f)||!tt(h))){let M,W;const V=Ln(h),q=Ln(f);if(!tt(p)&&!isNaN(p)){const Q=s.valueAsNumber||p&&+p;tt(V.value)||(M=Q>V.value),tt(q.value)||(W=Q<q.value)}else{const Q=s.valueAsDate||new Date(p),de=H=>new Date(new Date().toDateString()+" "+H),D=s.type=="time",z=s.type=="week";mt(V.value)&&p&&(M=D?de(p)>de(V.value):z?p>V.value:Q>new Date(V.value)),mt(q.value)&&p&&(W=D?de(p)<de(q.value):z?p<q.value:Q<new Date(q.value))}if((M||W)&&(Z(!!M,V.message,q.message,or.max,or.min),!n))return k(_[y].message),_}if((u||d)&&!L&&(mt(p)||a&&Array.isArray(p))){const M=Ln(u),W=Ln(d),V=!tt(M.value)&&p.length>+M.value,q=!tt(W.value)&&p.length<+W.value;if((V||q)&&(Z(V,M.message,W.message),!n))return k(_[y].message),_}if(w&&!L&&mt(p)){const{value:M,message:W}=Ln(w);if(fo(M)&&!p.match(M)&&(_[y]={type:or.pattern,message:W,ref:s,...U(or.pattern,W)},!n))return k(W),_}if(g){if(Ft(g)){const M=await g(p,r),W=Fp(M,v);if(W&&(_[y]={...W,...U(or.validate,W.message)},!n))return k(W.message),_}else if(Re(g)){let M={};for(const W in g){if(!st(M)&&!n)break;const V=Fp(await g[W](p,r),v,W);V&&(M={...V,...U(W,V.message)},k(V.message),n&&(_[y]=M))}if(!st(M)&&(_[y]={ref:v,...M},!n))return _}}return k(!0),_};const qj={mode:Mt.onSubmit,reValidateMode:Mt.onChange,shouldFocusError:!0};function Hj(e={}){let t={...qj,...e},r={submitCount:0,isDirty:!1,isReady:!1,isLoading:Ft(t.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1},n={},i=Re(t.defaultValues)||Re(t.values)?ze(t.defaultValues||t.values)||{}:{},a=t.shouldUnregister?{}:ze(i),s={action:!1,mount:!1,watch:!1},o={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},c,u=0;const d={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let f={...d};const h={array:_p(),state:_p()},w=t.criteriaMode===Mt.all,g=b=>E=>{clearTimeout(u),u=setTimeout(b,E)},y=async b=>{if(!t.disabled&&(d.isValid||f.isValid||b)){const E=t.resolver?st((await P()).errors):await L(n,!0);E!==r.isValid&&h.state.next({isValid:E})}},x=(b,E)=>{!t.disabled&&(d.isValidating||d.validatingFields||f.isValidating||f.validatingFields)&&((b||Array.from(o.mount)).forEach(T=>{T&&(E?ye(r.validatingFields,T,E):_e(r.validatingFields,T))}),h.state.next({validatingFields:r.validatingFields,isValidating:!st(r.validatingFields)}))},m=(b,E=[],T,I,j=!0,C=!0)=>{if(I&&T&&!t.disabled){if(s.action=!0,C&&Array.isArray(B(n,b))){const F=T(B(n,b),I.argA,I.argB);j&&ye(n,b,F)}if(C&&Array.isArray(B(r.errors,b))){const F=T(B(r.errors,b),I.argA,I.argB);j&&ye(r.errors,b,F),Vj(r.errors,b)}if((d.touchedFields||f.touchedFields)&&C&&Array.isArray(B(r.touchedFields,b))){const F=T(B(r.touchedFields,b),I.argA,I.argB);j&&ye(r.touchedFields,b,F)}(d.dirtyFields||f.dirtyFields)&&(r.dirtyFields=Mn(i,a)),h.state.next({name:b,isDirty:Z(b,E),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else ye(a,b,E)},p=(b,E)=>{ye(r.errors,b,E),h.state.next({errors:r.errors})},v=b=>{r.errors=b,h.state.next({errors:r.errors,isValid:!1})},k=(b,E,T,I)=>{const j=B(n,b);if(j){const C=B(a,b,Te(T)?B(i,b):T);Te(C)||I&&I.defaultChecked||E?ye(a,b,E?C:Rp(j._f)):V(b,C),s.mount&&y()}},_=(b,E,T,I,j)=>{let C=!1,F=!1;const Y={name:b};if(!t.disabled){if(!T||I){(d.isDirty||f.isDirty)&&(F=r.isDirty,r.isDirty=Y.isDirty=Z(),C=F!==Y.isDirty);const J=Ar(B(i,b),E);F=!!B(r.dirtyFields,b),J?_e(r.dirtyFields,b):ye(r.dirtyFields,b,!0),Y.dirtyFields=r.dirtyFields,C=C||(d.dirtyFields||f.dirtyFields)&&F!==!J}if(T){const J=B(r.touchedFields,b);J||(ye(r.touchedFields,b,T),Y.touchedFields=r.touchedFields,C=C||(d.touchedFields||f.touchedFields)&&J!==T)}C&&j&&h.state.next(Y)}return C?Y:{}},A=(b,E,T,I)=>{const j=B(r.errors,b),C=(d.isValid||f.isValid)&&Kt(E)&&r.isValid!==E;if(t.delayError&&T?(c=g(()=>p(b,T)),c(t.delayError)):(clearTimeout(u),c=null,T?ye(r.errors,b,T):_e(r.errors,b)),(T?!Ar(j,T):j)||!st(I)||C){const F={...I,...C&&Kt(E)?{isValid:E}:{},errors:r.errors,name:b};r={...r,...F},h.state.next(F)}},P=async b=>{x(b,!0);const E=await t.resolver(a,t.context,Fj(b||o.mount,n,t.criteriaMode,t.shouldUseNativeValidation));return x(b),E},O=async b=>{const{errors:E}=await P(b);if(b)for(const T of b){const I=B(E,T);I?ye(r.errors,T,I):_e(r.errors,T)}else r.errors=E;return E},L=async(b,E,T={valid:!0})=>{for(const I in b){const j=b[I];if(j){const{_f:C,...F}=j;if(C){const Y=o.array.has(C.name),J=j._f&&zj(j._f);J&&d.validatingFields&&x([C.name],!0);const Fe=await zp(j,o.disabled,a,w,t.shouldUseNativeValidation&&!E,Y);if(J&&d.validatingFields&&x([C.name]),Fe[C.name]&&(T.valid=!1,E))break;!E&&(B(Fe,C.name)?Y?Wj(r.errors,Fe,C.name):ye(r.errors,C.name,Fe[C.name]):_e(r.errors,C.name))}!st(F)&&await L(F,E,T)}}return T.valid},U=()=>{for(const b of o.unMount){const E=B(n,b);E&&(E._f.refs?E._f.refs.every(T=>!Ml(T)):!Ml(E._f.ref))&&Tt(b)}o.unMount=new Set},Z=(b,E)=>!t.disabled&&(b&&E&&ye(a,b,E),!Ar(H(),i)),M=(b,E,T)=>Oj(b,o,{...s.mount?a:Te(E)?i:mt(b)?{[b]:E}:E},T,E),W=b=>xd(B(s.mount?a:i,b,t.shouldUnregister?B(i,b,[]):[])),V=(b,E,T={})=>{const I=B(n,b);let j=E;if(I){const C=I._f;C&&(!C.disabled&&ye(a,b,Iv(E,C)),j=uo(C.ref)&&tt(E)?"":E,Mv(C.ref)?[...C.ref.options].forEach(F=>F.selected=j.includes(F.value)):C.refs?Ia(C.ref)?C.refs.forEach(F=>{(!F.defaultChecked||!F.disabled)&&(Array.isArray(j)?F.checked=!!j.find(Y=>Y===F.value):F.checked=j===F.value||!!j)}):C.refs.forEach(F=>F.checked=F.value===j):bd(C.ref)?C.ref.value="":(C.ref.value=j,C.ref.type||h.state.next({name:b,values:ze(a)})))}(T.shouldDirty||T.shouldTouch)&&_(b,j,T.shouldTouch,T.shouldDirty,!0),T.shouldValidate&&z(b)},q=(b,E,T)=>{for(const I in E){if(!E.hasOwnProperty(I))return;const j=E[I],C=b+"."+I,F=B(n,C);(o.array.has(b)||Re(j)||F&&!F._f)&&!hn(j)?q(C,j,T):V(C,j,T)}},Q=(b,E,T={})=>{const I=B(n,b),j=o.array.has(b),C=ze(E);ye(a,b,C),j?(h.array.next({name:b,values:ze(a)}),(d.isDirty||d.dirtyFields||f.isDirty||f.dirtyFields)&&T.shouldDirty&&h.state.next({name:b,dirtyFields:Mn(i,a),isDirty:Z(b,C)})):I&&!I._f&&!tt(C)?q(b,C,T):V(b,C,T),Lp(b,o)&&h.state.next({...r,name:b}),h.state.next({name:s.mount?b:void 0,values:ze(a)})},de=async b=>{s.mount=!0;const E=b.target;let T=E.name,I=!0;const j=B(n,T),C=J=>{I=Number.isNaN(J)||hn(J)&&isNaN(J.getTime())||Ar(J,B(a,T,J))},F=Ap(t.mode),Y=Ap(t.reValidateMode);if(j){let J,Fe;const Pt=E.type?Rp(j._f):Ej(b),bt=b.type===Cp.BLUR||b.type===Cp.FOCUS_OUT,Ci=!Ij(j._f)&&!t.resolver&&!B(r.errors,T)&&!j._f.deps||Bj(bt,B(r.touchedFields,T),r.isSubmitted,Y,F),nn=Lp(T,o,bt);ye(a,T,Pt),bt?(!E||!E.readOnly)&&(j._f.onBlur&&j._f.onBlur(b),c&&c(0)):j._f.onChange&&j._f.onChange(b);const Sr=_(T,Pt,bt),_i=!st(Sr)||nn;if(!bt&&h.state.next({name:T,type:b.type,values:ze(a)}),Ci)return(d.isValid||f.isValid)&&(t.mode==="onBlur"?bt&&y():bt||y()),_i&&h.state.next({name:T,...nn?{}:Sr});if(!bt&&nn&&h.state.next({...r}),t.resolver){const{errors:Pn}=await P([T]);if(C(Pt),I){const Ti=Mp(r.errors,n,T),On=Mp(Pn,n,Ti.name||T);J=On.error,T=On.name,Fe=st(Pn)}}else x([T],!0),J=(await zp(j,o.disabled,a,w,t.shouldUseNativeValidation))[T],x([T]),C(Pt),I&&(J?Fe=!1:(d.isValid||f.isValid)&&(Fe=await L(n,!0)));I&&(j._f.deps&&(!Array.isArray(j._f.deps)||j._f.deps.length>0)&&z(j._f.deps),A(T,Fe,J,Sr))}},D=(b,E)=>{if(B(r.errors,E)&&b.focus)return b.focus(),1},z=async(b,E={})=>{let T,I;const j=na(b);if(t.resolver){const C=await O(Te(b)?b:j);T=st(C),I=b?!j.some(F=>B(C,F)):T}else b?(I=(await Promise.all(j.map(async C=>{const F=B(n,C);return await L(F&&F._f?{[C]:F}:F)}))).every(Boolean),!(!I&&!r.isValid)&&y()):I=T=await L(n);return h.state.next({...!mt(b)||(d.isValid||f.isValid)&&T!==r.isValid?{}:{name:b},...t.resolver||!b?{isValid:T}:{},errors:r.errors}),E.shouldFocus&&!I&&ia(n,D,b?j:o.mount),I},H=(b,E)=>{let T={...s.mount?a:i};return E&&(T=Lv(E.dirtyFields?r.dirtyFields:r.touchedFields,T)),Te(b)?T:mt(b)?B(T,b):b.map(I=>B(T,I))},K=(b,E)=>({invalid:!!B((E||r).errors,b),isDirty:!!B((E||r).dirtyFields,b),error:B((E||r).errors,b),isValidating:!!B(r.validatingFields,b),isTouched:!!B((E||r).touchedFields,b)}),ae=b=>{b&&na(b).forEach(E=>_e(r.errors,E)),h.state.next({errors:b?r.errors:{}})},at=(b,E,T)=>{const I=(B(n,b,{_f:{}})._f||{}).ref,j=B(r.errors,b)||{},{ref:C,message:F,type:Y,...J}=j;ye(r.errors,b,{...J,...E,ref:I}),h.state.next({name:b,errors:r.errors,isValid:!1}),T&&T.shouldFocus&&I&&I.focus&&I.focus()},je=(b,E)=>Ft(b)?h.state.subscribe({next:T=>"values"in T&&b(M(void 0,E),T)}):M(b,E,!0),wt=b=>h.state.subscribe({next:E=>{$j(b.name,E.name,b.exact)&&Uj(E,b.formState||d,kr,b.reRenderRoot)&&b.callback({values:{...a},...r,...E,defaultValues:i})}}).unsubscribe,$e=b=>(s.mount=!0,f={...f,...b.formState},wt({...b,formState:f})),Tt=(b,E={})=>{for(const T of b?na(b):o.mount)o.mount.delete(T),o.array.delete(T),E.keepValue||(_e(n,T),_e(a,T)),!E.keepError&&_e(r.errors,T),!E.keepDirty&&_e(r.dirtyFields,T),!E.keepTouched&&_e(r.touchedFields,T),!E.keepIsValidating&&_e(r.validatingFields,T),!t.shouldUnregister&&!E.keepDefaultValue&&_e(i,T);h.state.next({values:ze(a)}),h.state.next({...r,...E.keepDirty?{isDirty:Z()}:{}}),!E.keepIsValid&&y()},Ei=({disabled:b,name:E})=>{(Kt(b)&&s.mount||b||o.disabled.has(E))&&(b?o.disabled.add(E):o.disabled.delete(E))},_n=(b,E={})=>{let T=B(n,b);const I=Kt(E.disabled)||Kt(t.disabled);return ye(n,b,{...T||{},_f:{...T&&T._f?T._f:{ref:{name:b}},name:b,mount:!0,...E}}),o.mount.add(b),T?Ei({disabled:Kt(E.disabled)?E.disabled:t.disabled,name:b}):k(b,!0,E.value),{...I?{disabled:E.disabled||t.disabled}:{},...t.progressive?{required:!!E.required,min:Bi(E.min),max:Bi(E.max),minLength:Bi(E.minLength),maxLength:Bi(E.maxLength),pattern:Bi(E.pattern)}:{},name:b,onChange:de,onBlur:de,ref:j=>{if(j){_n(b,E),T=B(n,b);const C=Te(j.value)&&j.querySelectorAll&&j.querySelectorAll("input,select,textarea")[0]||j,F=Aj(C),Y=T._f.refs||[];if(F?Y.find(J=>J===C):C===T._f.ref)return;ye(n,b,{_f:{...T._f,...F?{refs:[...Y.filter(Ml),C,...Array.isArray(B(i,b))?[{}]:[]],ref:{type:C.type,name:b}}:{ref:C}}}),k(b,!1,void 0,C)}else T=B(n,b,{}),T._f&&(T._f.mount=!1),(t.shouldUnregister||E.shouldUnregister)&&!(Nj(o.array,b)&&s.action)&&o.unMount.add(b)}}},br=()=>t.shouldFocusError&&ia(n,D,o.mount),Tn=b=>{Kt(b)&&(h.state.next({disabled:b}),ia(n,(E,T)=>{const I=B(n,T);I&&(E.disabled=I._f.disabled||b,Array.isArray(I._f.refs)&&I._f.refs.forEach(j=>{j.disabled=I._f.disabled||b}))},0,!1))},ir=(b,E)=>async T=>{let I;T&&(T.preventDefault&&T.preventDefault(),T.persist&&T.persist());let j=ze(a);if(h.state.next({isSubmitting:!0}),t.resolver){const{errors:C,values:F}=await P();r.errors=C,j=ze(F)}else await L(n);if(o.disabled.size)for(const C of o.disabled)_e(j,C);if(_e(r.errors,"root"),st(r.errors)){h.state.next({errors:{}});try{await b(j,T)}catch(C){I=C}}else E&&await E({...r.errors},T),br(),setTimeout(br);if(h.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:st(r.errors)&&!I,submitCount:r.submitCount+1,errors:r.errors}),I)throw I},Ua=(b,E={})=>{B(n,b)&&(Te(E.defaultValue)?Q(b,ze(B(i,b))):(Q(b,E.defaultValue),ye(i,b,ze(E.defaultValue))),E.keepTouched||_e(r.touchedFields,b),E.keepDirty||(_e(r.dirtyFields,b),r.isDirty=E.defaultValue?Z(b,ze(B(i,b))):Z()),E.keepError||(_e(r.errors,b),d.isValid&&y()),h.state.next({...r}))},ji=(b,E={})=>{const T=b?ze(b):i,I=ze(T),j=st(b),C=j?i:I;if(E.keepDefaultValues||(i=T),!E.keepValues){if(E.keepDirtyValues){const F=new Set([...o.mount,...Object.keys(Mn(i,a))]);for(const Y of Array.from(F))B(r.dirtyFields,Y)?ye(C,Y,B(a,Y)):Q(Y,B(C,Y))}else{if(yd&&Te(b))for(const F of o.mount){const Y=B(n,F);if(Y&&Y._f){const J=Array.isArray(Y._f.refs)?Y._f.refs[0]:Y._f.ref;if(uo(J)){const Fe=J.closest("form");if(Fe){Fe.reset();break}}}}if(E.keepFieldsRef)for(const F of o.mount)Q(F,B(C,F));else n={}}a=t.shouldUnregister?E.keepDefaultValues?ze(i):{}:ze(C),h.array.next({values:{...C}}),h.state.next({values:{...C}})}o={mount:E.keepDirtyValues?o.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},s.mount=!d.isValid||!!E.keepIsValid||!!E.keepDirtyValues,s.watch=!!t.shouldUnregister,h.state.next({submitCount:E.keepSubmitCount?r.submitCount:0,isDirty:j?!1:E.keepDirty?r.isDirty:!!(E.keepDefaultValues&&!Ar(b,i)),isSubmitted:E.keepIsSubmitted?r.isSubmitted:!1,dirtyFields:j?{}:E.keepDirtyValues?E.keepDefaultValues&&a?Mn(i,a):r.dirtyFields:E.keepDefaultValues&&b?Mn(i,b):E.keepDirty?r.dirtyFields:{},touchedFields:E.keepTouched?r.touchedFields:{},errors:E.keepErrors?r.errors:{},isSubmitSuccessful:E.keepIsSubmitSuccessful?r.isSubmitSuccessful:!1,isSubmitting:!1,defaultValues:i})},Ni=(b,E)=>ji(Ft(b)?b(a):b,E),$a=(b,E={})=>{const T=B(n,b),I=T&&T._f;if(I){const j=I.refs?I.refs[0]:I.ref;j.focus&&(j.focus(),E.shouldSelect&&Ft(j.select)&&j.select())}},kr=b=>{r={...r,...b}},rn={control:{register:_n,unregister:Tt,getFieldState:K,handleSubmit:ir,setError:at,_subscribe:wt,_runSchema:P,_focusError:br,_getWatch:M,_getDirty:Z,_setValid:y,_setFieldArray:m,_setDisabledField:Ei,_setErrors:v,_getFieldArray:W,_reset:ji,_resetDefaultValues:()=>Ft(t.defaultValues)&&t.defaultValues().then(b=>{Ni(b,t.resetOptions),h.state.next({isLoading:!1})}),_removeUnmounted:U,_disableForm:Tn,_subjects:h,_proxyFormState:d,get _fields(){return n},get _formValues(){return a},get _state(){return s},set _state(b){s=b},get _defaultValues(){return i},get _names(){return o},set _names(b){o=b},get _formState(){return r},get _options(){return t},set _options(b){t={...t,...b}}},subscribe:$e,trigger:z,register:_n,handleSubmit:ir,watch:je,setValue:Q,getValues:H,reset:Ni,resetField:Ua,clearErrors:ae,unregister:Tt,setError:at,setFocus:$a,getFieldState:K};return{...rn,formControl:rn}}function Qo(e={}){const t=Ve.useRef(void 0),r=Ve.useRef(void 0),[n,i]=Ve.useState({isDirty:!1,isValidating:!1,isLoading:Ft(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:Ft(e.defaultValues)?void 0:e.defaultValues});if(!t.current)if(e.formControl)t.current={...e.formControl,formState:n},e.defaultValues&&!Ft(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{const{formControl:s,...o}=Hj(e);t.current={...o,formState:n}}const a=t.current.control;return a._options=e,Pj(()=>{const s=a._subscribe({formState:a._proxyFormState,callback:()=>i({...a._formState}),reRenderRoot:!0});return i(o=>({...o,isReady:!0})),a._formState.isReady=!0,s},[a]),Ve.useEffect(()=>a._disableForm(e.disabled),[a,e.disabled]),Ve.useEffect(()=>{e.mode&&(a._options.mode=e.mode),e.reValidateMode&&(a._options.reValidateMode=e.reValidateMode)},[a,e.mode,e.reValidateMode]),Ve.useEffect(()=>{e.errors&&(a._setErrors(e.errors),a._focusError())},[a,e.errors]),Ve.useEffect(()=>{e.shouldUnregister&&a._subjects.state.next({values:a._getWatch()})},[a,e.shouldUnregister]),Ve.useEffect(()=>{if(a._proxyFormState.isDirty){const s=a._getDirty();s!==n.isDirty&&a._subjects.state.next({isDirty:s})}},[a,n.isDirty]),Ve.useEffect(()=>{e.values&&!Ar(e.values,r.current)?(a._reset(e.values,{keepFieldsRef:!0,...a._options.resetOptions}),r.current=e.values,i(s=>({...s}))):a._resetDefaultValues()},[a,e.values]),Ve.useEffect(()=>{a._state.mount||(a._setValid(),a._state.mount=!0),a._state.watch&&(a._state.watch=!1,a._subjects.state.next({...a._formState})),a._removeUnmounted()}),t.current.formState=Tj(n,a),t.current}const ne=S.forwardRef(({className:e,variant:t="primary",size:r="md",loading:n=!1,children:i,disabled:a,...s},o)=>{const c="btn inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",u={primary:"bg-primary-600 text-white hover:bg-primary-700 focus-visible:ring-primary-500",secondary:"bg-gray-200 text-gray-900 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800",danger:"bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500"},d={sm:"h-8 px-3 text-xs",md:"h-10 px-4 py-2",lg:"h-12 px-8 text-base"};return l.jsxs("button",{className:se(c,u[t],d[r],e),ref:o,disabled:a||n,...s,children:[n&&l.jsx(vd,{size:"sm",className:"mr-2"}),i]})});ne.displayName="Button";const qe=S.forwardRef(({className:e,type:t,label:r,error:n,helperText:i,leftIcon:a,rightIcon:s,id:o,...c},u)=>{const d=o||`input-${Math.random().toString(36).substr(2,9)}`;return l.jsxs("div",{className:"space-y-1",children:[r&&l.jsx("label",{htmlFor:d,className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:r}),l.jsxs("div",{className:"relative",children:[a&&l.jsx("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",children:a}),l.jsx("input",{type:t,className:se("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:placeholder:text-gray-400",a&&"pl-10",s&&"pr-10",n&&"border-red-500 focus-visible:ring-red-500",e),ref:u,id:d,...c}),s&&l.jsx("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400",children:s})]}),n&&l.jsx("p",{className:"text-sm text-red-600 dark:text-red-400",children:n}),i&&!n&&l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:i})]})});qe.displayName="Input";/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Yj={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kj=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),te=(e,t)=>{const r=S.forwardRef(({color:n="currentColor",size:i=24,strokeWidth:a=2,absoluteStrokeWidth:s,className:o="",children:c,...u},d)=>S.createElement("svg",{ref:d,...Yj,width:i,height:i,stroke:n,strokeWidth:s?Number(a)*24/Number(i):a,className:["lucide",`lucide-${Kj(e)}`,o].join(" "),...u},[...t.map(([f,h])=>S.createElement(f,h)),...Array.isArray(c)?c:[c]]));return r.displayName=`${e}`,r};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $v=te("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qj=te("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gj=te("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ip=te("CheckCheck",[["path",{d:"M18 6 7 17l-5-5",key:"116fxf"}],["path",{d:"m22 10-7.5 7.5L13 16",key:"ke71qq"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bv=te("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xj=te("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jj=te("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vv=te("File",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wv=te("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zj=te("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jc=te("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const po=te("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Go=te("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qv=te("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xr=te("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eN=te("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hv=te("MoreVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yv=te("Music",[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Up=te("Paperclip",[["path",{d:"m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48",key:"1u3ebp"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tN=te("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kv=te("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rN=te("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nN=te("Reply",[["polyline",{points:"9 17 4 12 9 7",key:"hvgpf2"}],["path",{d:"M20 18v-2a4 4 0 0 0-4-4H4",key:"5vmcpk"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iN=te("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const En=te("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aN=te("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sd=te("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qv=te("Smile",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 14s1.5 2 4 2 4-2 4-2",key:"1y1vjs"}],["line",{x1:"9",x2:"9.01",y1:"9",y2:"9",key:"yxxnd0"}],["line",{x1:"15",x2:"15.01",y1:"9",y2:"9",key:"1p4y9e"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sN=te("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oN=te("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lN=te("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cN=te("UserMinus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ed=te("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xo=te("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jo=te("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zo=te("Video",[["path",{d:"m22 8-6 4 6 4V8Z",key:"50v9me"}],["rect",{width:"14",height:"12",x:"2",y:"6",rx:"2",ry:"2",key:"1rqjg6"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mo=te("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function uN(){var c,u;const[e,t]=S.useState(!1),{login:r}=nr(),n=Lo(),{register:i,handleSubmit:a,formState:{errors:s}}=Qo(),o=async d=>{t(!0);try{await r(d.email,d.password),le.success("Login successful!"),n("/")}catch(f){le.error(f.message||"Login failed")}finally{t(!1)}};return l.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8",children:l.jsxs("div",{className:"max-w-md w-full space-y-8",children:[l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"flex justify-center",children:l.jsx("div",{className:"bg-primary-600 p-3 rounded-full",children:l.jsx(xr,{className:"h-8 w-8 text-white"})})}),l.jsx("h2",{className:"mt-6 text-3xl font-bold text-gray-900 dark:text-white",children:"Welcome back"}),l.jsx("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:"Sign in to your KingChat account"})]}),l.jsxs("form",{className:"mt-8 space-y-6",onSubmit:a(o),children:[l.jsxs("div",{className:"space-y-4",children:[l.jsx("div",{children:l.jsx(qe,{...i("email",{required:"Email is required",pattern:{value:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,message:"Please enter a valid email"}}),type:"email",label:"Email address",placeholder:"Enter your email",error:(c=s.email)==null?void 0:c.message,leftIcon:l.jsx(Go,{className:"h-4 w-4"})})}),l.jsx("div",{children:l.jsx(qe,{...i("password",{required:"Password is required",minLength:{value:6,message:"Password must be at least 6 characters"}}),type:"password",label:"Password",placeholder:"Enter your password",error:(u=s.password)==null?void 0:u.message,leftIcon:l.jsx(Jc,{className:"h-4 w-4"})})})]}),l.jsx("div",{className:"flex items-center justify-between",children:l.jsx("div",{className:"text-sm",children:l.jsx(ui,{to:"/forgot-password",className:"font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400",children:"Forgot your password?"})})}),l.jsx("div",{children:l.jsx(ne,{type:"submit",className:"w-full",loading:e,disabled:e,children:"Sign in"})}),l.jsx("div",{className:"text-center",children:l.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Don't have an account?"," ",l.jsx(ui,{to:"/register",className:"font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400",children:"Sign up"})]})})]})]})})}function dN(){var d,f,h,w;const[e,t]=S.useState(!1),{register:r}=nr(),n=Lo(),{register:i,handleSubmit:a,watch:s,formState:{errors:o}}=Qo(),c=s("password"),u=async g=>{t(!0);try{await r(g.username,g.email,g.password),le.success("Registration successful!"),n("/")}catch(y){le.error(y.message||"Registration failed")}finally{t(!1)}};return l.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8",children:l.jsxs("div",{className:"max-w-md w-full space-y-8",children:[l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"flex justify-center",children:l.jsx("div",{className:"bg-primary-600 p-3 rounded-full",children:l.jsx(xr,{className:"h-8 w-8 text-white"})})}),l.jsx("h2",{className:"mt-6 text-3xl font-bold text-gray-900 dark:text-white",children:"Create your account"}),l.jsx("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:"Join KingChat and start chatting with friends"})]}),l.jsxs("form",{className:"mt-8 space-y-6",onSubmit:a(u),children:[l.jsxs("div",{className:"space-y-4",children:[l.jsx("div",{children:l.jsx(qe,{...i("username",{required:"Username is required",minLength:{value:3,message:"Username must be at least 3 characters"},maxLength:{value:20,message:"Username cannot exceed 20 characters"},pattern:{value:/^[a-zA-Z0-9_]+$/,message:"Username can only contain letters, numbers, and underscores"}}),type:"text",label:"Username",placeholder:"Choose a username",error:(d=o.username)==null?void 0:d.message,leftIcon:l.jsx(Xo,{className:"h-4 w-4"})})}),l.jsx("div",{children:l.jsx(qe,{...i("email",{required:"Email is required",pattern:{value:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,message:"Please enter a valid email"}}),type:"email",label:"Email address",placeholder:"Enter your email",error:(f=o.email)==null?void 0:f.message,leftIcon:l.jsx(Go,{className:"h-4 w-4"})})}),l.jsx("div",{children:l.jsx(qe,{...i("password",{required:"Password is required",minLength:{value:6,message:"Password must be at least 6 characters"}}),type:"password",label:"Password",placeholder:"Create a password",error:(h=o.password)==null?void 0:h.message,leftIcon:l.jsx(Jc,{className:"h-4 w-4"})})}),l.jsx("div",{children:l.jsx(qe,{...i("confirmPassword",{required:"Please confirm your password",validate:g=>g===c||"Passwords do not match"}),type:"password",label:"Confirm password",placeholder:"Confirm your password",error:(w=o.confirmPassword)==null?void 0:w.message,leftIcon:l.jsx(Jc,{className:"h-4 w-4"})})})]}),l.jsx("div",{children:l.jsx(ne,{type:"submit",className:"w-full",loading:e,disabled:e,children:"Create account"})}),l.jsx("div",{className:"text-center",children:l.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Already have an account?"," ",l.jsx(ui,{to:"/login",className:"font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400",children:"Sign in"})]})})]})]})})}function fN({chats:e,currentChat:t,onSelectChat:r,onCreateChat:n}){const{user:i}=nr(),[a,s]=S.useState(""),[o,c]=S.useState("recent"),u=g=>{if(g.type==="private"){const y=g.members.find(x=>x._id!==(i==null?void 0:i._id));return(y==null?void 0:y.username)||"Unknown User"}return g.name||"Unnamed Group"},d=g=>{if(g.type==="private"){const y=g.members.find(x=>x._id!==(i==null?void 0:i._id));return y==null?void 0:y.avatarUrl}return g.avatarUrl},f=g=>{if(g.type==="private"){const y=g.members.find(x=>x._id!==(i==null?void 0:i._id));return y==null?void 0:y.status}},h=g=>Math.floor(Math.random()*10),w=S.useMemo(()=>{let g=e.filter(y=>u(y).toLowerCase().includes(a.toLowerCase()));return g.sort((y,x)=>{var m,p,v,k;switch(o){case"recent":const _=new Date(((m=y.lastMessage)==null?void 0:m.timestamp)||y.updatedAt).getTime();return new Date(((p=x.lastMessage)==null?void 0:p.timestamp)||x.updatedAt).getTime()-_;case"unread":const P=h(),O=h();if(P!==O)return O-P;const L=new Date(((v=y.lastMessage)==null?void 0:v.timestamp)||y.updatedAt).getTime();return new Date(((k=x.lastMessage)==null?void 0:k.timestamp)||x.updatedAt).getTime()-L;case"alphabetical":const Z=u(y),M=u(x);return Z.localeCompare(M);default:return 0}}),g},[e,a,o,i==null?void 0:i._id]);return l.jsxs("div",{className:"flex flex-col h-full",children:[l.jsx("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700",children:l.jsxs("div",{className:"space-y-3",children:[l.jsxs("div",{className:"relative",children:[l.jsx(En,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),l.jsx(qe,{placeholder:"Search chats...",value:a,onChange:g=>s(g.target.value),className:"pl-10"})]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsxs("select",{value:o,onChange:g=>c(g.target.value),className:"text-xs bg-gray-100 dark:bg-gray-700 border-0 rounded px-2 py-1",children:[l.jsx("option",{value:"recent",children:"Recent"}),l.jsx("option",{value:"unread",children:"Unread"}),l.jsx("option",{value:"alphabetical",children:"A-Z"})]}),l.jsx("button",{onClick:n,className:"ml-auto p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300",title:"New chat",children:l.jsx(xr,{className:"h-4 w-4"})})]})]})}),l.jsx("div",{className:"flex-1 overflow-y-auto",children:w.length===0?l.jsx("div",{className:"flex-1 flex items-center justify-center p-4",children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4",children:l.jsx(xr,{className:"w-8 h-8 text-gray-400"})}),l.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:a?"No chats found":"No chats yet"}),l.jsx("p",{className:"text-gray-400 dark:text-gray-500 text-xs mt-1",children:a?"Try a different search term":"Start a conversation with friends"})]})}):w.map(g=>{const y=(t==null?void 0:t._id)===g._id,x=u(g),m=d(g),p=f(g),v=g.lastMessage,k=h();return l.jsx("div",{onClick:()=>r(g),className:se("p-4 border-b border-gray-100 dark:border-gray-700 cursor-pointer transition-colors hover:bg-gray-50 dark:hover:bg-gray-700 relative",y&&"bg-primary-50 dark:bg-primary-900/20 border-primary-200 dark:border-primary-700"),children:l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsxs("div",{className:"relative",children:[m?l.jsx("img",{src:m,alt:x,className:"w-12 h-12 rounded-full object-cover"}):l.jsx("div",{className:"w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center",children:l.jsx("span",{className:"text-white font-medium text-sm",children:tn(x)})}),g.type==="private"&&p&&l.jsx("div",{className:se("absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-gray-800",p==="online"&&"bg-green-500",p==="away"&&"bg-yellow-500",p==="dnd"&&"bg-red-500",p==="offline"&&"bg-gray-400")}),g.type==="group"&&l.jsx("div",{className:"absolute -bottom-1 -right-1 w-4 h-4 bg-blue-500 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center",children:l.jsx(Jo,{className:"h-2 w-2 text-white"})})]}),l.jsxs("div",{className:"flex-1 min-w-0",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("h3",{className:se("text-sm font-medium truncate",y?"text-primary-900 dark:text-primary-100":"text-gray-900 dark:text-white"),children:x}),l.jsxs("div",{className:"flex items-center space-x-2",children:[v&&l.jsx("span",{className:se("text-xs",y?"text-primary-600 dark:text-primary-400":"text-gray-500 dark:text-gray-400"),children:Tv(v.timestamp)}),k>0&&l.jsx("span",{className:"bg-primary-600 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center",children:k>99?"99+":k})]})]}),v&&l.jsx("p",{className:se("text-sm truncate mt-1",y?"text-primary-700 dark:text-primary-300":"text-gray-600 dark:text-gray-400"),children:v.type==="text"?v.content:v.type==="image"?"📷 Image":v.type==="file"?"📎 File":"System message"})]})]})},g._id)})})]})}function pN(){const{friends:e,loadFriends:t,createPrivateChat:r}=We(),[n,i]=S.useState(""),[a,s]=S.useState(!1),[o,c]=S.useState("status"),[u,d]=S.useState(null);S.useEffect(()=>{t()},[t]);const f=e.filter(m=>m.username.toLowerCase().includes(n.toLowerCase())).sort((m,p)=>{switch(o){case"status":const v={online:0,away:1,dnd:2,offline:3};return v[m.status]-v[p.status];case"name":return m.username.localeCompare(p.username);case"recent":return new Date(p.lastSeen).getTime()-new Date(m.lastSeen).getTime();default:return 0}}),h=async m=>{s(!0);try{await r(m._id),le.success(`Started chat with ${m.username}`)}catch(p){le.error(p.message||"Failed to start chat")}finally{s(!1)}},w=async m=>{if(window.confirm(`Are you sure you want to remove ${m.username} from your friends?`))try{le.success(`Removed ${m.username} from friends`)}catch(p){le.error(p.message||"Failed to remove friend")}},g=m=>{le(`Calling ${m.username}...`,{icon:"📞"})},y=m=>{le(`Starting video call with ${m.username}...`,{icon:"📹"})},x=m=>{le(`Viewing ${m.username}'s profile`,{icon:"👤"})};return e.length===0?l.jsx("div",{className:"flex-1 flex items-center justify-center p-4",children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4",children:l.jsx(Ed,{className:"w-8 h-8 text-gray-400"})}),l.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:"No friends yet"}),l.jsx("p",{className:"text-gray-400 dark:text-gray-500 text-xs mt-1",children:"Add friends to start chatting"})]})}):l.jsxs("div",{className:"flex-1 flex flex-col",children:[l.jsx("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700",children:l.jsxs("div",{className:"space-y-3",children:[l.jsx("div",{className:"relative",children:l.jsx(qe,{type:"text",placeholder:"Search friends...",value:n,onChange:m=>i(m.target.value),leftIcon:l.jsx(En,{className:"h-4 w-4"})})}),l.jsx("div",{className:"flex items-center space-x-2",children:l.jsxs("select",{value:o,onChange:m=>c(m.target.value),className:"text-xs bg-gray-100 dark:bg-gray-700 border-0 rounded px-2 py-1",children:[l.jsx("option",{value:"status",children:"Status"}),l.jsx("option",{value:"name",children:"Name"}),l.jsx("option",{value:"recent",children:"Recent"})]})})]})}),l.jsx("div",{className:"flex-1 overflow-y-auto",children:f.length===0?l.jsx("div",{className:"flex items-center justify-center p-4",children:l.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:n?"No friends found":"No friends yet"})}):f.map(m=>l.jsx("div",{className:"p-4 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors group",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsxs("div",{className:"relative",children:[m.avatarUrl?l.jsx("img",{src:m.avatarUrl,alt:m.username,className:"w-10 h-10 rounded-full object-cover"}):l.jsx("div",{className:"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center",children:l.jsx("span",{className:"text-white font-medium text-sm",children:tn(m.username)})}),l.jsx("div",{className:se("absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-gray-800",Fa(m.status))})]}),l.jsxs("div",{className:"flex-1 min-w-0",children:[l.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:m.username}),l.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:m.status==="offline"?kj(m.lastSeen):za(m.status)})]})]}),l.jsxs("div",{className:"flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[l.jsx(ne,{variant:"ghost",size:"sm",onClick:()=>h(m),disabled:a,className:"text-primary-600 hover:text-primary-700",title:"Start chat",children:l.jsx(xr,{className:"h-4 w-4"})}),l.jsx(ne,{variant:"ghost",size:"sm",onClick:()=>g(m),className:"text-green-600 hover:text-green-700",title:"Voice call",children:l.jsx(Kv,{className:"h-4 w-4"})}),l.jsx(ne,{variant:"ghost",size:"sm",onClick:()=>y(m),className:"text-blue-600 hover:text-blue-700",title:"Video call",children:l.jsx(Zo,{className:"h-4 w-4"})}),l.jsxs("div",{className:"relative",children:[l.jsx(ne,{variant:"ghost",size:"sm",onClick:()=>d(u===m._id?null:m._id),className:"text-gray-600 hover:text-gray-700",title:"More options",children:l.jsx(Hv,{className:"h-4 w-4"})}),u===m._id&&l.jsxs("div",{className:"absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-10",children:[l.jsxs("button",{onClick:()=>{x(m),d(null)},className:"w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2",children:[l.jsx(Zj,{className:"h-4 w-4"}),l.jsx("span",{children:"View Profile"})]}),l.jsxs("button",{onClick:()=>{w(m),d(null)},className:"w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2",children:[l.jsx(cN,{className:"h-4 w-4"}),l.jsx("span",{children:"Remove Friend"})]})]})]})]})]})},m._id))})]})}function mN(){const{user:e,logout:t}=nr(),r=async()=>{try{await t()}catch(n){console.error("Logout error:",n)}};return e?l.jsxs("div",{className:"flex-1 flex flex-col",children:[l.jsxs("div",{className:"p-6 text-center border-b border-gray-200 dark:border-gray-700",children:[l.jsxs("div",{className:"relative inline-block",children:[e.avatarUrl?l.jsx("img",{src:e.avatarUrl,alt:e.username,className:"w-20 h-20 rounded-full object-cover mx-auto"}):l.jsx("div",{className:"w-20 h-20 bg-primary-600 rounded-full flex items-center justify-center mx-auto",children:l.jsx("span",{className:"text-white font-medium text-2xl",children:tn(e.username)})}),l.jsx("div",{className:se("absolute -bottom-1 -right-1 w-6 h-6 rounded-full border-4 border-white dark:border-gray-800 flex items-center justify-center",Fa(e.status)),children:l.jsx("div",{className:"w-2 h-2 bg-white rounded-full"})})]}),l.jsx("h2",{className:"mt-4 text-xl font-bold text-gray-900 dark:text-white",children:e.username}),l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:za(e.status)})]}),l.jsx("div",{className:"flex-1 p-4 space-y-4",children:l.jsxs("div",{className:"space-y-3",children:[l.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[l.jsx(Go,{className:"h-5 w-5 text-gray-400"}),l.jsxs("div",{children:[l.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Email"}),l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.email})]})]}),l.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[l.jsx(Gj,{className:"h-5 w-5 text-gray-400"}),l.jsxs("div",{children:[l.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Member since"}),l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:new Date(e.createdAt).toLocaleDateString()})]})]}),l.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[l.jsx(Xo,{className:"h-5 w-5 text-gray-400"}),l.jsxs("div",{children:[l.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Username"}),l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.username})]})]})]})}),l.jsxs("div",{className:"p-4 border-t border-gray-200 dark:border-gray-700 space-y-3",children:[l.jsx(ui,{to:"/settings",children:l.jsxs(ne,{variant:"ghost",className:"w-full justify-start",children:[l.jsx(Sd,{className:"h-4 w-4 mr-3"}),"Settings"]})}),l.jsxs(ne,{variant:"ghost",className:"w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20",onClick:r,children:[l.jsx(po,{className:"h-4 w-4 mr-3"}),"Logout"]})]})]}):l.jsx("div",{className:"flex-1 flex items-center justify-center p-4",children:l.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:"User not found"})})}function Gv({isOpen:e,onClose:t,onCreateChat:r}){const{friends:n,loadFriends:i}=We(),[a,s]=S.useState(""),[o,c]=S.useState(null),[u,d]=S.useState(!1);S.useEffect(()=>{e&&i()},[e,i]);const f=n.filter(w=>w.username.toLowerCase().includes(a.toLowerCase())),h=async()=>{if(o){d(!0);try{const{createPrivateChat:w,setCurrentChat:g}=We.getState(),y=await w(o._id);g(y),r(y),t(),le.success(`Started chat with ${o.username}`)}catch(w){le.error(w.message||"Failed to create chat")}finally{d(!1)}}};return e?l.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:l.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md max-h-[80vh] flex flex-col",children:[l.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[l.jsx("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Start New Chat"}),l.jsx("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:l.jsx(mo,{className:"h-6 w-6"})})]}),l.jsx("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:l.jsxs("div",{className:"relative",children:[l.jsx(En,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),l.jsx(qe,{placeholder:"Search friends...",value:a,onChange:w=>s(w.target.value),className:"pl-10"})]})}),l.jsx("div",{className:"flex-1 overflow-y-auto p-6",children:n.length===0?l.jsxs("div",{className:"text-center py-8",children:[l.jsx(Ed,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),l.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:"No friends yet"}),l.jsx("p",{className:"text-gray-400 dark:text-gray-500 text-xs mt-1",children:"Add friends to start chatting"})]}):f.length===0?l.jsxs("div",{className:"text-center py-8",children:[l.jsx(En,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),l.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:"No friends found"}),l.jsx("p",{className:"text-gray-400 dark:text-gray-500 text-xs mt-1",children:"Try a different search term"})]}):l.jsx("div",{className:"space-y-2",children:f.map(w=>l.jsxs("div",{onClick:()=>c(w),className:se("flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors",(o==null?void 0:o._id)===w._id?"bg-primary-100 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-700":"hover:bg-gray-100 dark:hover:bg-gray-700"),children:[l.jsxs("div",{className:"relative",children:[w.avatarUrl?l.jsx("img",{src:w.avatarUrl,alt:w.username,className:"w-10 h-10 rounded-full object-cover"}):l.jsx("div",{className:"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center",children:l.jsx("span",{className:"text-white font-medium text-sm",children:tn(w.username)})}),l.jsx("div",{className:se("absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-gray-800",Fa(w.status))})]}),l.jsxs("div",{className:"flex-1 min-w-0",children:[l.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:w.username}),l.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:za(w.status)})]}),(o==null?void 0:o._id)===w._id&&l.jsx("div",{className:"w-5 h-5 bg-primary-600 rounded-full flex items-center justify-center",children:l.jsx(xr,{className:"h-3 w-3 text-white"})})]},w._id))})}),l.jsx("div",{className:"p-6 border-t border-gray-200 dark:border-gray-700",children:l.jsxs("div",{className:"flex space-x-3",children:[l.jsx(ne,{variant:"secondary",onClick:t,className:"flex-1",children:"Cancel"}),l.jsx(ne,{onClick:h,disabled:!o||u,className:"flex-1",children:u?"Creating...":"Start Chat"})]})})]})}):null}function Xv({isOpen:e,onClose:t,onCreateGroup:r}){const{friends:n,loadFriends:i}=We(),[a,s]=S.useState(""),[o,c]=S.useState([]),[u,d]=S.useState(""),[f,h]=S.useState(""),[w,g]=S.useState(!1);S.useEffect(()=>{e&&i()},[e,i]);const y=n.filter(k=>k.username.toLowerCase().includes(a.toLowerCase())),x=k=>{c(_=>_.some(P=>P._id===k._id)?_.filter(P=>P._id!==k._id):[..._,k])},m=async()=>{if(!u.trim()||o.length===0){le.error("Please enter a group name and select at least one member");return}g(!0);try{const{createGroupChat:k,setCurrentChat:_}=We.getState(),A=o.map(O=>O._id),P=await k(u.trim(),A,f.trim()||void 0);_(P),r(P),t(),le.success(`Created group "${u}"`)}catch(k){le.error(k.message||"Failed to create group")}finally{g(!1)}},p=()=>{d(""),h(""),c([]),s("")},v=()=>{p(),t()};return e?l.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:l.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-lg max-h-[80vh] flex flex-col",children:[l.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[l.jsx("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Create Group Chat"}),l.jsx("button",{onClick:v,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:l.jsx(mo,{className:"h-6 w-6"})})]}),l.jsxs("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700 space-y-4",children:[l.jsx("div",{children:l.jsx(qe,{placeholder:"Group name",value:u,onChange:k=>d(k.target.value),maxLength:50})}),l.jsx("div",{children:l.jsx(qe,{placeholder:"Group description (optional)",value:f,onChange:k=>h(k.target.value),maxLength:200})})]}),o.length>0&&l.jsxs("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:[l.jsxs("h3",{className:"text-sm font-medium text-gray-900 dark:text-white mb-3",children:["Selected Members (",o.length,")"]}),l.jsx("div",{className:"flex flex-wrap gap-2",children:o.map(k=>l.jsxs("div",{className:"flex items-center space-x-2 bg-primary-100 dark:bg-primary-900/20 rounded-full px-3 py-1",children:[l.jsx("span",{className:"text-sm text-primary-700 dark:text-primary-300",children:k.username}),l.jsx("button",{onClick:()=>x(k),className:"text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-200",children:l.jsx(mo,{className:"h-3 w-3"})})]},k._id))})]}),l.jsx("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:l.jsxs("div",{className:"relative",children:[l.jsx(En,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),l.jsx(qe,{placeholder:"Search friends to add...",value:a,onChange:k=>s(k.target.value),className:"pl-10"})]})}),l.jsx("div",{className:"flex-1 overflow-y-auto p-6",children:n.length===0?l.jsxs("div",{className:"text-center py-8",children:[l.jsx(Ed,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),l.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:"No friends yet"}),l.jsx("p",{className:"text-gray-400 dark:text-gray-500 text-xs mt-1",children:"Add friends to create groups"})]}):y.length===0?l.jsxs("div",{className:"text-center py-8",children:[l.jsx(En,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),l.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:"No friends found"}),l.jsx("p",{className:"text-gray-400 dark:text-gray-500 text-xs mt-1",children:"Try a different search term"})]}):l.jsx("div",{className:"space-y-2",children:y.map(k=>{const _=o.some(A=>A._id===k._id);return l.jsxs("div",{onClick:()=>x(k),className:se("flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors",_?"bg-primary-100 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-700":"hover:bg-gray-100 dark:hover:bg-gray-700"),children:[l.jsxs("div",{className:"relative",children:[k.avatarUrl?l.jsx("img",{src:k.avatarUrl,alt:k.username,className:"w-10 h-10 rounded-full object-cover"}):l.jsx("div",{className:"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center",children:l.jsx("span",{className:"text-white font-medium text-sm",children:tn(k.username)})}),l.jsx("div",{className:se("absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-gray-800",Fa(k.status))})]}),l.jsxs("div",{className:"flex-1 min-w-0",children:[l.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:k.username}),l.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:za(k.status)})]}),_&&l.jsx("div",{className:"w-5 h-5 bg-primary-600 rounded-full flex items-center justify-center",children:l.jsx(Bv,{className:"h-3 w-3 text-white"})})]},k._id)})})}),l.jsx("div",{className:"p-6 border-t border-gray-200 dark:border-gray-700",children:l.jsxs("div",{className:"flex space-x-3",children:[l.jsx(ne,{variant:"secondary",onClick:v,className:"flex-1",children:"Cancel"}),l.jsx(ne,{onClick:m,disabled:!u.trim()||o.length===0||w,className:"flex-1",children:w?"Creating...":`Create Group (${o.length})`})]})})]})}):null}function hN({chats:e,currentChat:t,onToggleSidebar:r}){var x;const{user:n,logout:i}=nr(),{setCurrentChat:a}=We(),[s,o]=S.useState("chats"),[c,u]=S.useState(""),[d,f]=S.useState(!1),[h,w]=S.useState(!1),g=async()=>{try{await i()}catch(m){console.error("Logout error:",m)}},y=e.filter(m=>{var v;if(!c)return!0;const p=c.toLowerCase();if(m.type==="private"){const k=m.members.find(_=>_._id!==(n==null?void 0:n._id));return(k==null?void 0:k.username.toLowerCase().includes(p))||!1}else return((v=m.name)==null?void 0:v.toLowerCase().includes(p))||!1});return l.jsxs("div",{className:"h-full flex flex-col bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700",children:[l.jsxs("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700",children:[l.jsxs("div",{className:"flex items-center justify-between mb-4",children:[l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("div",{className:"bg-primary-600 p-2 rounded-lg",children:l.jsx(xr,{className:"h-5 w-5 text-white"})}),l.jsx("h1",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"KingChat"})]}),l.jsx(ne,{variant:"ghost",size:"sm",onClick:r,className:"lg:hidden",children:l.jsx(qv,{className:"h-4 w-4"})})]}),l.jsx("div",{className:"relative",children:l.jsx(qe,{type:"text",placeholder:"Search chats...",value:c,onChange:m=>u(m.target.value),leftIcon:l.jsx(En,{className:"h-4 w-4"}),className:"pr-10"})})]}),l.jsxs("div",{className:"flex border-b border-gray-200 dark:border-gray-700",children:[l.jsx("button",{onClick:()=>o("chats"),className:se("flex-1 px-4 py-3 text-sm font-medium transition-colors",s==="chats"?"text-primary-600 border-b-2 border-primary-600 bg-primary-50 dark:bg-primary-900/20":"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"),children:l.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[l.jsx(xr,{className:"h-4 w-4"}),l.jsx("span",{children:"Chats"})]})}),l.jsx("button",{onClick:()=>o("friends"),className:se("flex-1 px-4 py-3 text-sm font-medium transition-colors",s==="friends"?"text-primary-600 border-b-2 border-primary-600 bg-primary-50 dark:bg-primary-900/20":"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"),children:l.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[l.jsx(Jo,{className:"h-4 w-4"}),l.jsx("span",{children:"Friends"})]})}),l.jsx("button",{onClick:()=>o("profile"),className:se("flex-1 px-4 py-3 text-sm font-medium transition-colors",s==="profile"?"text-primary-600 border-b-2 border-primary-600 bg-primary-50 dark:bg-primary-900/20":"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"),children:l.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[l.jsx(Xo,{className:"h-4 w-4"}),l.jsx("span",{children:"Profile"})]})})]}),l.jsxs("div",{className:"flex-1 overflow-hidden",children:[s==="chats"&&l.jsx(fN,{chats:y,currentChat:t,onSelectChat:a,onCreateChat:()=>f(!0)}),s==="friends"&&l.jsx(pN,{}),s==="profile"&&l.jsx(mN,{})]}),l.jsx("div",{className:"p-4 border-t border-gray-200 dark:border-gray-700",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("div",{className:"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center",children:l.jsx("span",{className:"text-white text-sm font-medium",children:(x=n==null?void 0:n.username)==null?void 0:x.charAt(0).toUpperCase()})}),l.jsxs("div",{children:[l.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:n==null?void 0:n.username}),l.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:n==null?void 0:n.status})]})]}),l.jsxs("div",{className:"flex items-center space-x-1",children:[l.jsx(ne,{variant:"ghost",size:"sm",onClick:()=>{},children:l.jsx(Sd,{className:"h-4 w-4"})}),l.jsx(ne,{variant:"ghost",size:"sm",onClick:g,children:l.jsx(po,{className:"h-4 w-4"})})]})]})}),l.jsx(Gv,{isOpen:d,onClose:()=>f(!1),onCreateChat:()=>f(!1)}),l.jsx(Xv,{isOpen:h,onClose:()=>w(!1),onCreateGroup:()=>w(!1)})]})}function gN({message:e,isCurrentUser:t,showTimestamp:r,onReply:n,onEdit:i}){const[a,s]=S.useState(!1),[o,c]=S.useState(!1),u=()=>{if(!t)return null;switch(e.status){case"sent":return l.jsx(Bv,{className:"h-3 w-3 text-gray-400"});case"delivered":return l.jsx(Ip,{className:"h-3 w-3 text-gray-400"});case"read":return l.jsx(Ip,{className:"h-3 w-3 text-blue-500"});default:return null}},d=x=>Ov(x)?l.jsx(Wv,{className:"h-5 w-5 text-blue-500"}):Rv(x)?l.jsx(Zo,{className:"h-5 w-5 text-purple-500"}):Av(x)?l.jsx(Yv,{className:"h-5 w-5 text-green-500"}):l.jsx(Vv,{className:"h-5 w-5 text-gray-500"}),f=async()=>{try{await Sj(e.content),le.success("Message copied to clipboard")}catch{le.error("Failed to copy message")}},h=()=>{window.confirm("Are you sure you want to delete this message?")&&zt.revokeMessage(e._id,e.chatId)},w=x=>{zt.addReaction(e._id,x,e.chatId),c(!1)},g=["👍","❤️","😂","😮","😢","😡"],y=()=>{var x,m,p,v;if(e.isRevoked)return l.jsx("div",{className:"flex items-center space-x-2 text-gray-500 dark:text-gray-400 italic",children:l.jsx("span",{children:"This message has been revoked"})});switch(e.type){case"text":return l.jsx("div",{className:"whitespace-pre-wrap break-words",children:e.content});case"image":return l.jsxs("div",{className:"space-y-2",children:[l.jsx("img",{src:(x=e.metadata)==null?void 0:x.url,alt:"Shared image",className:"max-w-full h-auto rounded-lg cursor-pointer hover:opacity-90 transition-opacity",onClick:()=>{var k;return window.open((k=e.metadata)==null?void 0:k.url,"_blank")}}),e.content&&l.jsx("div",{className:"whitespace-pre-wrap break-words",children:e.content})]});case"file":return l.jsxs("div",{className:"space-y-2",children:[l.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-gray-100 dark:bg-gray-700 rounded-lg",children:[l.jsx("div",{className:"flex-shrink-0",children:d(((m=e.metadata)==null?void 0:m.filename)||"")}),l.jsxs("div",{className:"flex-1 min-w-0",children:[l.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:((p=e.metadata)==null?void 0:p.filename)||"File"}),((v=e.metadata)==null?void 0:v.size)&&l.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:Pv(e.metadata.size)})]}),l.jsx("button",{onClick:()=>{var k;return window.open((k=e.metadata)==null?void 0:k.url,"_blank")},className:"flex-shrink-0 text-primary-600 hover:text-primary-700",children:l.jsx(Jj,{className:"h-4 w-4"})})]}),e.content&&l.jsx("div",{className:"whitespace-pre-wrap break-words",children:e.content})]});default:return l.jsx("div",{children:e.content})}};return l.jsxs("div",{className:se("group relative max-w-xs lg:max-w-md",t?"ml-auto":"mr-auto"),onMouseEnter:()=>s(!0),onMouseLeave:()=>s(!1),children:[l.jsx("div",{className:se("rounded-lg px-4 py-2 text-sm",t?"bg-primary-600 text-white":"bg-gray-200 text-gray-900 dark:bg-gray-700 dark:text-gray-100"),children:y()}),l.jsxs("div",{className:se("flex items-center space-x-1 mt-1",t?"justify-end":"justify-start"),children:[r&&l.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:Tv(e.createdAt)}),u()]}),e.reactions&&e.reactions.length>0&&l.jsx("div",{className:se("flex flex-wrap gap-1 mt-1",t?"justify-end":"justify-start"),children:e.reactions.map((x,m)=>l.jsxs("span",{className:"inline-flex items-center space-x-1 px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full text-xs",children:[l.jsx("span",{children:x.emoji}),l.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:x.user})]},m))}),a&&!e.isRevoked&&l.jsxs("div",{className:se("absolute top-0 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity",t?"left-0 transform -translate-x-full":"right-0 transform translate-x-full"),children:[l.jsx("button",{onClick:()=>n==null?void 0:n(e),className:"p-1 rounded-full bg-white dark:bg-gray-800 shadow-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",title:"Reply",children:l.jsx(nN,{className:"h-3 w-3 text-gray-500"})}),l.jsxs("div",{className:"relative",children:[l.jsx("button",{onClick:()=>c(!o),className:"p-1 rounded-full bg-white dark:bg-gray-800 shadow-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",title:"React",children:l.jsx(Qv,{className:"h-3 w-3 text-gray-500"})}),o&&l.jsx("div",{className:se("absolute top-0 flex space-x-1 p-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700",t?"right-full mr-1":"left-full ml-1"),children:g.map(x=>l.jsx("button",{onClick:()=>w(x),className:"p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors",children:x},x))})]}),l.jsx("button",{onClick:f,className:"p-1 rounded-full bg-white dark:bg-gray-800 shadow-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",title:"Copy",children:l.jsx(Xj,{className:"h-3 w-3 text-gray-500"})}),t&&l.jsxs(l.Fragment,{children:[l.jsx("button",{onClick:()=>i==null?void 0:i(e),className:"p-1 rounded-full bg-white dark:bg-gray-800 shadow-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",title:"Edit",children:l.jsx(tN,{className:"h-3 w-3 text-gray-500"})}),l.jsx("button",{onClick:h,className:"p-1 rounded-full bg-white dark:bg-gray-800 shadow-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",title:"Delete",children:l.jsx(oN,{className:"h-3 w-3 text-red-500"})})]})]})]})}function vN({messages:e,currentUserId:t}){return e.length===0?l.jsx("div",{className:"flex-1 flex items-center justify-center p-8",children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4",children:l.jsx("svg",{className:"w-8 h-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),l.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:"No messages yet"}),l.jsx("p",{className:"text-gray-400 dark:text-gray-500 text-xs mt-1",children:"Start the conversation"})]})}):l.jsx("div",{className:"flex-1 p-4 space-y-4",children:e.map((r,n)=>{const i=r.sender._id===t,a=n>0?e[n-1]:null,s=n<e.length-1?e[n+1]:null,o=!s||s.sender._id!==r.sender._id,c=!a||new Date(r.createdAt).getTime()-new Date(a.createdAt).getTime()>5*60*1e3;return l.jsx("div",{className:se("flex",i?"justify-end":"justify-start"),children:l.jsxs("div",{className:se("flex max-w-xs lg:max-w-md",i?"flex-row-reverse":"flex-row"),children:[!i&&l.jsx("div",{className:"flex-shrink-0 mr-2",children:o?l.jsx("div",{className:"w-8 h-8",children:r.sender.avatarUrl?l.jsx("img",{src:r.sender.avatarUrl,alt:r.sender.username,className:"w-8 h-8 rounded-full object-cover"}):l.jsx("div",{className:"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center",children:l.jsx("span",{className:"text-white text-xs font-medium",children:tn(r.sender.username)})})}):l.jsx("div",{className:"w-8 h-8"})}),l.jsxs("div",{className:se("flex flex-col",i?"items-end":"items-start"),children:[!i&&o&&l.jsxs("div",{className:"flex items-center space-x-2 mb-1",children:[l.jsx("span",{className:"text-xs font-medium text-gray-900 dark:text-white",children:r.sender.username}),c&&l.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:new Date(r.createdAt).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]}),l.jsx(gN,{message:r,isCurrentUser:i,showTimestamp:c})]})]})},r._id)})})}const $p={smileys:["😀","😃","😄","😁","😆","😅","😂","🤣","😊","😇","🙂","🙃","😉","😌","😍","🥰","😘","😗","😙","😚","😋","😛","😝","😜","🤪","🤨","🧐","🤓","😎","🤩","🥳"],people:["👶","🧒","👦","👧","🧑","👨","👩","🧓","👴","👵","👤","👥","🫂","👪","👨‍👩‍👧","👨‍👩‍👧‍👦","👨‍👩‍👦‍👦","👨‍👩‍👧‍👧","👨‍👨‍👦","👨‍👨‍👧","👩‍👩‍👦","👩‍👩‍👧"],animals:["🐶","🐱","🐭","🐹","🐰","🦊","🐻","🐼","🐨","🐯","🦁","🐮","🐷","🐸","🐵","🙈","🙉","🙊","🐒","🦍","🦧","🐕","🐩","🦮","🐕‍🦺","🐈","🐈‍⬛","🦄","🐎","🦓","🦌"],food:["🍎","🍐","🍊","🍋","🍌","🍉","🍇","🍓","🫐","🍈","🍒","🍑","🥭","🍍","🥥","🥝","🍅","🍆","🥑","🥦","🥬","🥒","🌶️","🫑","🌽","🥕","🫒","🧄","🧅","🥔"],activities:["⚽","🏀","🏈","⚾","🥎","🎾","🏐","🏉","🎱","🪀","🏓","🏸","🏒","🏑","🥍","🏏","🪃","🥅","⛳","🪁","🏹","🎣","🤿","🥊","🥋","🎽","🛹","🛷","⛸️","🥌"],travel:["🚗","🚕","🚙","🚌","🚎","🏎️","🚓","🚑","🚒","🚐","🛻","🚚","🚛","🚜","🏍️","🛵","🚲","🛴","🛹","🛼","🚁","✈️","🛩️","🛫","🛬","🪂","💺","🚀","🛸","🚉"],objects:["⌚","📱","📲","💻","⌨️","🖥️","🖨️","🖱️","🖲️","🕹️","🗜️","💽","💾","💿","📀","📼","📷","📸","📹","🎥","📽️","🎞️","📞","☎️","📟","📠","📺","📻","🎙️","🎚️","🎛️"],symbols:["❤️","🧡","💛","💚","💙","💜","🖤","🤍","🤎","💔","❣️","💕","💞","💓","💗","💖","💘","💝","💟","☮️","✝️","☪️","🕉️","☸️","✡️","🔯","🕎","☯️","☦️","🛐","⛎"],flags:["🏁","🚩","🎌","🏴","🏳️","🏳️‍🌈","🏳️‍⚧️","🏴‍☠️","🇦🇨","🇦🇩","🇦🇪","🇦🇫","🇦🇬","🇦🇮","🇦🇱","🇦🇲","🇦🇴","🇦🇶","🇦🇷","🇦🇸","🇦🇹","🇦🇺","🇦🇼","🇦🇽","🇦🇿","🇧🇦","🇧🇧","🇧🇩","🇧🇪","🇧🇫"]};function yN({onEmojiSelect:e,className:t}){const[r,n]=S.useState("smileys");return l.jsxs("div",{className:se("bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 w-80 h-96 flex flex-col",t),children:[l.jsx("div",{className:"flex space-x-1 mb-3 border-b border-gray-200 dark:border-gray-700 pb-2",children:Object.keys($p).map(i=>l.jsx("button",{onClick:()=>n(i),className:se("px-2 py-1 text-xs rounded transition-colors",r===i?"bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300":"text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"),children:i},i))}),l.jsx("div",{className:"flex-1 overflow-y-auto",children:l.jsx("div",{className:"grid grid-cols-8 gap-1",children:$p[r].map((i,a)=>l.jsx("button",{onClick:()=>e(i),className:"p-2 text-lg hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors",title:i,children:i},a))})})]})}var Jv={exports:{}},xN="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",wN=xN,bN=wN;function Zv(){}function ey(){}ey.resetWarningCache=Zv;var kN=function(){function e(n,i,a,s,o,c){if(c!==bN){var u=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}e.isRequired=e;function t(){return e}var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:ey,resetWarningCache:Zv};return r.PropTypes=r,r};Jv.exports=kN();var SN=Jv.exports;const pe=iu(SN);function Cn(e,t,r,n){function i(a){return a instanceof r?a:new r(function(s){s(a)})}return new(r||(r=Promise))(function(a,s){function o(d){try{u(n.next(d))}catch(f){s(f)}}function c(d){try{u(n.throw(d))}catch(f){s(f)}}function u(d){d.done?a(d.value):i(d.value).then(o,c)}u((n=n.apply(e,t||[])).next())})}const EN=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function mi(e,t,r){const n=jN(e),{webkitRelativePath:i}=e,a=typeof t=="string"?t:typeof i=="string"&&i.length>0?i:`./${e.name}`;return typeof n.path!="string"&&Bp(n,"path",a),r!==void 0&&Object.defineProperty(n,"handle",{value:r,writable:!1,configurable:!1,enumerable:!0}),Bp(n,"relativePath",a),n}function jN(e){const{name:t}=e;if(t&&t.lastIndexOf(".")!==-1&&!e.type){const n=t.split(".").pop().toLowerCase(),i=EN.get(n);i&&Object.defineProperty(e,"type",{value:i,writable:!1,configurable:!1,enumerable:!0})}return e}function Bp(e,t,r){Object.defineProperty(e,t,{value:r,writable:!1,configurable:!1,enumerable:!0})}const NN=[".DS_Store","Thumbs.db"];function CN(e){return Cn(this,void 0,void 0,function*(){return ho(e)&&_N(e.dataTransfer)?RN(e.dataTransfer,e.type):TN(e)?PN(e):Array.isArray(e)&&e.every(t=>"getFile"in t&&typeof t.getFile=="function")?ON(e):[]})}function _N(e){return ho(e)}function TN(e){return ho(e)&&ho(e.target)}function ho(e){return typeof e=="object"&&e!==null}function PN(e){return Zc(e.target.files).map(t=>mi(t))}function ON(e){return Cn(this,void 0,void 0,function*(){return(yield Promise.all(e.map(r=>r.getFile()))).map(r=>mi(r))})}function RN(e,t){return Cn(this,void 0,void 0,function*(){if(e.items){const r=Zc(e.items).filter(i=>i.kind==="file");if(t!=="drop")return r;const n=yield Promise.all(r.map(AN));return Vp(ty(n))}return Vp(Zc(e.files).map(r=>mi(r)))})}function Vp(e){return e.filter(t=>NN.indexOf(t.name)===-1)}function Zc(e){if(e===null)return[];const t=[];for(let r=0;r<e.length;r++){const n=e[r];t.push(n)}return t}function AN(e){if(typeof e.webkitGetAsEntry!="function")return Wp(e);const t=e.webkitGetAsEntry();return t&&t.isDirectory?ry(t):Wp(e,t)}function ty(e){return e.reduce((t,r)=>[...t,...Array.isArray(r)?ty(r):[r]],[])}function Wp(e,t){return Cn(this,void 0,void 0,function*(){var r;if(globalThis.isSecureContext&&typeof e.getAsFileSystemHandle=="function"){const a=yield e.getAsFileSystemHandle();if(a===null)throw new Error(`${e} is not a File`);if(a!==void 0){const s=yield a.getFile();return s.handle=a,mi(s)}}const n=e.getAsFile();if(!n)throw new Error(`${e} is not a File`);return mi(n,(r=t==null?void 0:t.fullPath)!==null&&r!==void 0?r:void 0)})}function DN(e){return Cn(this,void 0,void 0,function*(){return e.isDirectory?ry(e):LN(e)})}function ry(e){const t=e.createReader();return new Promise((r,n)=>{const i=[];function a(){t.readEntries(s=>Cn(this,void 0,void 0,function*(){if(s.length){const o=Promise.all(s.map(DN));i.push(o),a()}else try{const o=yield Promise.all(i);r(o)}catch(o){n(o)}}),s=>{n(s)})}a()})}function LN(e){return Cn(this,void 0,void 0,function*(){return new Promise((t,r)=>{e.file(n=>{const i=mi(n,e.fullPath);t(i)},n=>{r(n)})})})}var Fl=function(e,t){if(e&&t){var r=Array.isArray(t)?t:t.split(",");if(r.length===0)return!0;var n=e.name||"",i=(e.type||"").toLowerCase(),a=i.replace(/\/.*$/,"");return r.some(function(s){var o=s.trim().toLowerCase();return o.charAt(0)==="."?n.toLowerCase().endsWith(o):o.endsWith("/*")?a===o.replace(/\/.*$/,""):i===o})}return!0};function qp(e){return zN(e)||FN(e)||iy(e)||MN()}function MN(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function FN(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function zN(e){if(Array.isArray(e))return eu(e)}function Hp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Yp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Hp(Object(r),!0).forEach(function(n){ny(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Hp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ny(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ja(e,t){return $N(e)||UN(e,t)||iy(e,t)||IN()}function IN(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function iy(e,t){if(e){if(typeof e=="string")return eu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return eu(e,t)}}function eu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function UN(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n=[],i=!0,a=!1,s,o;try{for(r=r.call(e);!(i=(s=r.next()).done)&&(n.push(s.value),!(t&&n.length===t));i=!0);}catch(c){a=!0,o=c}finally{try{!i&&r.return!=null&&r.return()}finally{if(a)throw o}}return n}}function $N(e){if(Array.isArray(e))return e}var BN=typeof Fl=="function"?Fl:Fl.default,VN="file-invalid-type",WN="file-too-large",qN="file-too-small",HN="too-many-files",YN=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",r=t.split(","),n=r.length>1?"one of ".concat(r.join(", ")):r[0];return{code:VN,message:"File type must be ".concat(n)}},Kp=function(t){return{code:WN,message:"File is larger than ".concat(t," ").concat(t===1?"byte":"bytes")}},Qp=function(t){return{code:qN,message:"File is smaller than ".concat(t," ").concat(t===1?"byte":"bytes")}},KN={code:HN,message:"Too many files"};function ay(e,t){var r=e.type==="application/x-moz-file"||BN(e,t);return[r,r?null:YN(t)]}function sy(e,t,r){if(un(e.size))if(un(t)&&un(r)){if(e.size>r)return[!1,Kp(r)];if(e.size<t)return[!1,Qp(t)]}else{if(un(t)&&e.size<t)return[!1,Qp(t)];if(un(r)&&e.size>r)return[!1,Kp(r)]}return[!0,null]}function un(e){return e!=null}function QN(e){var t=e.files,r=e.accept,n=e.minSize,i=e.maxSize,a=e.multiple,s=e.maxFiles,o=e.validator;return!a&&t.length>1||a&&s>=1&&t.length>s?!1:t.every(function(c){var u=ay(c,r),d=ja(u,1),f=d[0],h=sy(c,n,i),w=ja(h,1),g=w[0],y=o?o(c):null;return f&&g&&!y})}function go(e){return typeof e.isPropagationStopped=="function"?e.isPropagationStopped():typeof e.cancelBubble<"u"?e.cancelBubble:!1}function us(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(t){return t==="Files"||t==="application/x-moz-file"}):!!e.target&&!!e.target.files}function Gp(e){e.preventDefault()}function GN(e){return e.indexOf("MSIE")!==-1||e.indexOf("Trident/")!==-1}function XN(e){return e.indexOf("Edge/")!==-1}function JN(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.navigator.userAgent;return GN(e)||XN(e)}function Ht(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(n){for(var i=arguments.length,a=new Array(i>1?i-1:0),s=1;s<i;s++)a[s-1]=arguments[s];return t.some(function(o){return!go(n)&&o&&o.apply(void 0,[n].concat(a)),go(n)})}}function ZN(){return"showOpenFilePicker"in window}function eC(e){if(un(e)){var t=Object.entries(e).filter(function(r){var n=ja(r,2),i=n[0],a=n[1],s=!0;return oy(i)||(console.warn('Skipped "'.concat(i,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),s=!1),(!Array.isArray(a)||!a.every(ly))&&(console.warn('Skipped "'.concat(i,'" because an invalid file extension was provided.')),s=!1),s}).reduce(function(r,n){var i=ja(n,2),a=i[0],s=i[1];return Yp(Yp({},r),{},ny({},a,s))},{});return[{description:"Files",accept:t}]}return e}function tC(e){if(un(e))return Object.entries(e).reduce(function(t,r){var n=ja(r,2),i=n[0],a=n[1];return[].concat(qp(t),[i],qp(a))},[]).filter(function(t){return oy(t)||ly(t)}).join(",")}function rC(e){return e instanceof DOMException&&(e.name==="AbortError"||e.code===e.ABORT_ERR)}function nC(e){return e instanceof DOMException&&(e.name==="SecurityError"||e.code===e.SECURITY_ERR)}function oy(e){return e==="audio/*"||e==="video/*"||e==="image/*"||e==="text/*"||e==="application/*"||/\w+\/[-+.\w]+/g.test(e)}function ly(e){return/^.*\.[\w]+$/.test(e)}var iC=["children"],aC=["open"],sC=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],oC=["refKey","onChange","onClick"];function lC(e){return dC(e)||uC(e)||cy(e)||cC()}function cC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function uC(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function dC(e){if(Array.isArray(e))return tu(e)}function zl(e,t){return mC(e)||pC(e,t)||cy(e,t)||fC()}function fC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function cy(e,t){if(e){if(typeof e=="string")return tu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tu(e,t)}}function tu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function pC(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n=[],i=!0,a=!1,s,o;try{for(r=r.call(e);!(i=(s=r.next()).done)&&(n.push(s.value),!(t&&n.length===t));i=!0);}catch(c){a=!0,o=c}finally{try{!i&&r.return!=null&&r.return()}finally{if(a)throw o}}return n}}function mC(e){if(Array.isArray(e))return e}function Xp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function we(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xp(Object(r),!0).forEach(function(n){ru(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ru(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function vo(e,t){if(e==null)return{};var r=hC(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function hC(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}var jd=S.forwardRef(function(e,t){var r=e.children,n=vo(e,iC),i=dy(n),a=i.open,s=vo(i,aC);return S.useImperativeHandle(t,function(){return{open:a}},[a]),Ve.createElement(S.Fragment,null,r(we(we({},s),{},{open:a})))});jd.displayName="Dropzone";var uy={disabled:!1,getFilesFromEvent:CN,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};jd.defaultProps=uy;jd.propTypes={children:pe.func,accept:pe.objectOf(pe.arrayOf(pe.string)),multiple:pe.bool,preventDropOnDocument:pe.bool,noClick:pe.bool,noKeyboard:pe.bool,noDrag:pe.bool,noDragEventsBubbling:pe.bool,minSize:pe.number,maxSize:pe.number,maxFiles:pe.number,disabled:pe.bool,getFilesFromEvent:pe.func,onFileDialogCancel:pe.func,onFileDialogOpen:pe.func,useFsAccessApi:pe.bool,autoFocus:pe.bool,onDragEnter:pe.func,onDragLeave:pe.func,onDragOver:pe.func,onDrop:pe.func,onDropAccepted:pe.func,onDropRejected:pe.func,onError:pe.func,validator:pe.func};var nu={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function dy(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=we(we({},uy),e),r=t.accept,n=t.disabled,i=t.getFilesFromEvent,a=t.maxSize,s=t.minSize,o=t.multiple,c=t.maxFiles,u=t.onDragEnter,d=t.onDragLeave,f=t.onDragOver,h=t.onDrop,w=t.onDropAccepted,g=t.onDropRejected,y=t.onFileDialogCancel,x=t.onFileDialogOpen,m=t.useFsAccessApi,p=t.autoFocus,v=t.preventDropOnDocument,k=t.noClick,_=t.noKeyboard,A=t.noDrag,P=t.noDragEventsBubbling,O=t.onError,L=t.validator,U=S.useMemo(function(){return tC(r)},[r]),Z=S.useMemo(function(){return eC(r)},[r]),M=S.useMemo(function(){return typeof x=="function"?x:Jp},[x]),W=S.useMemo(function(){return typeof y=="function"?y:Jp},[y]),V=S.useRef(null),q=S.useRef(null),Q=S.useReducer(gC,nu),de=zl(Q,2),D=de[0],z=de[1],H=D.isFocused,K=D.isFileDialogActive,ae=S.useRef(typeof window<"u"&&window.isSecureContext&&m&&ZN()),at=function(){!ae.current&&K&&setTimeout(function(){if(q.current){var C=q.current.files;C.length||(z({type:"closeDialog"}),W())}},300)};S.useEffect(function(){return window.addEventListener("focus",at,!1),function(){window.removeEventListener("focus",at,!1)}},[q,K,W,ae]);var je=S.useRef([]),wt=function(C){V.current&&V.current.contains(C.target)||(C.preventDefault(),je.current=[])};S.useEffect(function(){return v&&(document.addEventListener("dragover",Gp,!1),document.addEventListener("drop",wt,!1)),function(){v&&(document.removeEventListener("dragover",Gp),document.removeEventListener("drop",wt))}},[V,v]),S.useEffect(function(){return!n&&p&&V.current&&V.current.focus(),function(){}},[V,p,n]);var $e=S.useCallback(function(j){O?O(j):console.error(j)},[O]),Tt=S.useCallback(function(j){j.preventDefault(),j.persist(),b(j),je.current=[].concat(lC(je.current),[j.target]),us(j)&&Promise.resolve(i(j)).then(function(C){if(!(go(j)&&!P)){var F=C.length,Y=F>0&&QN({files:C,accept:U,minSize:s,maxSize:a,multiple:o,maxFiles:c,validator:L}),J=F>0&&!Y;z({isDragAccept:Y,isDragReject:J,isDragActive:!0,type:"setDraggedFiles"}),u&&u(j)}}).catch(function(C){return $e(C)})},[i,u,$e,P,U,s,a,o,c,L]),Ei=S.useCallback(function(j){j.preventDefault(),j.persist(),b(j);var C=us(j);if(C&&j.dataTransfer)try{j.dataTransfer.dropEffect="copy"}catch{}return C&&f&&f(j),!1},[f,P]),_n=S.useCallback(function(j){j.preventDefault(),j.persist(),b(j);var C=je.current.filter(function(Y){return V.current&&V.current.contains(Y)}),F=C.indexOf(j.target);F!==-1&&C.splice(F,1),je.current=C,!(C.length>0)&&(z({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),us(j)&&d&&d(j))},[V,d,P]),br=S.useCallback(function(j,C){var F=[],Y=[];j.forEach(function(J){var Fe=ay(J,U),Pt=zl(Fe,2),bt=Pt[0],Ci=Pt[1],nn=sy(J,s,a),Sr=zl(nn,2),_i=Sr[0],Pn=Sr[1],Ti=L?L(J):null;if(bt&&_i&&!Ti)F.push(J);else{var On=[Ci,Pn];Ti&&(On=On.concat(Ti)),Y.push({file:J,errors:On.filter(function(fy){return fy})})}}),(!o&&F.length>1||o&&c>=1&&F.length>c)&&(F.forEach(function(J){Y.push({file:J,errors:[KN]})}),F.splice(0)),z({acceptedFiles:F,fileRejections:Y,isDragReject:Y.length>0,type:"setFiles"}),h&&h(F,Y,C),Y.length>0&&g&&g(Y,C),F.length>0&&w&&w(F,C)},[z,o,U,s,a,c,h,w,g,L]),Tn=S.useCallback(function(j){j.preventDefault(),j.persist(),b(j),je.current=[],us(j)&&Promise.resolve(i(j)).then(function(C){go(j)&&!P||br(C,j)}).catch(function(C){return $e(C)}),z({type:"reset"})},[i,br,$e,P]),ir=S.useCallback(function(){if(ae.current){z({type:"openDialog"}),M();var j={multiple:o,types:Z};window.showOpenFilePicker(j).then(function(C){return i(C)}).then(function(C){br(C,null),z({type:"closeDialog"})}).catch(function(C){rC(C)?(W(C),z({type:"closeDialog"})):nC(C)?(ae.current=!1,q.current?(q.current.value=null,q.current.click()):$e(new Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):$e(C)});return}q.current&&(z({type:"openDialog"}),M(),q.current.value=null,q.current.click())},[z,M,W,m,br,$e,Z,o]),Ua=S.useCallback(function(j){!V.current||!V.current.isEqualNode(j.target)||(j.key===" "||j.key==="Enter"||j.keyCode===32||j.keyCode===13)&&(j.preventDefault(),ir())},[V,ir]),ji=S.useCallback(function(){z({type:"focus"})},[]),Ni=S.useCallback(function(){z({type:"blur"})},[]),$a=S.useCallback(function(){k||(JN()?setTimeout(ir,0):ir())},[k,ir]),kr=function(C){return n?null:C},Ba=function(C){return _?null:kr(C)},rn=function(C){return A?null:kr(C)},b=function(C){P&&C.stopPropagation()},E=S.useMemo(function(){return function(){var j=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=j.refKey,F=C===void 0?"ref":C,Y=j.role,J=j.onKeyDown,Fe=j.onFocus,Pt=j.onBlur,bt=j.onClick,Ci=j.onDragEnter,nn=j.onDragOver,Sr=j.onDragLeave,_i=j.onDrop,Pn=vo(j,sC);return we(we(ru({onKeyDown:Ba(Ht(J,Ua)),onFocus:Ba(Ht(Fe,ji)),onBlur:Ba(Ht(Pt,Ni)),onClick:kr(Ht(bt,$a)),onDragEnter:rn(Ht(Ci,Tt)),onDragOver:rn(Ht(nn,Ei)),onDragLeave:rn(Ht(Sr,_n)),onDrop:rn(Ht(_i,Tn)),role:typeof Y=="string"&&Y!==""?Y:"presentation"},F,V),!n&&!_?{tabIndex:0}:{}),Pn)}},[V,Ua,ji,Ni,$a,Tt,Ei,_n,Tn,_,A,n]),T=S.useCallback(function(j){j.stopPropagation()},[]),I=S.useMemo(function(){return function(){var j=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=j.refKey,F=C===void 0?"ref":C,Y=j.onChange,J=j.onClick,Fe=vo(j,oC),Pt=ru({accept:U,multiple:o,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:kr(Ht(Y,Tn)),onClick:kr(Ht(J,T)),tabIndex:-1},F,q);return we(we({},Pt),Fe)}},[q,r,o,Tn,n]);return we(we({},D),{},{isFocused:H&&!n,getRootProps:E,getInputProps:I,rootRef:V,inputRef:q,open:kr(ir)})}function gC(e,t){switch(t.type){case"focus":return we(we({},e),{},{isFocused:!0});case"blur":return we(we({},e),{},{isFocused:!1});case"openDialog":return we(we({},nu),{},{isFileDialogActive:!0});case"closeDialog":return we(we({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return we(we({},e),{},{isDragActive:t.isDragActive,isDragAccept:t.isDragAccept,isDragReject:t.isDragReject});case"setFiles":return we(we({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections,isDragReject:t.isDragReject});case"reset":return we({},nu);default:return e}}function Jp(){}function vC({chatId:e,onSendMessage:t}){const[r,n]=S.useState(""),[i,a]=S.useState(!1),[s,o]=S.useState([]),[c,u]=S.useState(!1),[d,f]=S.useState(!1),h=S.useRef(null),w=S.useRef(),g=S.useRef(null),{getRootProps:y,getInputProps:x,isDragActive:m}=dy({onDrop:O=>{o(L=>[...L,...O])},accept:{"image/*":[".png",".jpg",".jpeg",".gif",".webp"],"application/*":[".pdf",".doc",".docx",".txt"],"video/*":[".mp4",".avi",".mov"],"audio/*":[".mp3",".wav",".ogg"]},maxSize:100*1024*1024,multiple:!0}),p=O=>{n(O.target.value),i||(a(!0),zt.startTyping(e)),w.current&&clearTimeout(w.current),w.current=setTimeout(()=>{a(!1),zt.stopTyping(e)},1e3)},v=async()=>{if(!(!r.trim()&&s.length===0))try{let O=[];if(s.length>0){u(!0);for(const L of s)try{if(L.type.startsWith("image/")){const U=await et.uploadImage(L);O.push({...U,type:"image"})}else{const U=await et.uploadFile(L);O.push({...U,type:"file"})}}catch{le.error(`Failed to upload ${L.name}`)}u(!1)}if(O.length>0)for(const L of O)t(r||(L.type==="image"?"📷 Image":"📎 File"),L.type,void 0,L);else t(r,"text");n(""),o([]),i&&(a(!1),zt.stopTyping(e))}catch{le.error("Failed to send message")}},k=O=>{O.key==="Enter"&&!O.shiftKey&&(O.preventDefault(),v())};S.useEffect(()=>()=>{w.current&&clearTimeout(w.current),i&&zt.stopTyping(e)},[e,i]);const _=O=>{o(L=>L.filter((U,Z)=>Z!==O))},A=O=>{var L;n(U=>U+O),f(!1),(L=h.current)==null||L.focus()},P=O=>Ov(O.name)?l.jsx(Wv,{className:"h-4 w-4 text-blue-500"}):Rv(O.name)?l.jsx(Zo,{className:"h-4 w-4 text-purple-500"}):Av(O.name)?l.jsx(Yv,{className:"h-4 w-4 text-green-500"}):l.jsx(Vv,{className:"h-4 w-4 text-gray-500"});return S.useEffect(()=>{const O=L=>{g.current&&!g.current.contains(L.target)&&f(!1)};return document.addEventListener("mousedown",O),()=>{document.removeEventListener("mousedown",O)}},[]),l.jsxs("div",{className:"space-y-3",children:[s.length>0&&l.jsx("div",{className:"flex flex-wrap gap-2",children:s.map((O,L)=>l.jsxs("div",{className:"relative flex items-center space-x-2 bg-gray-100 dark:bg-gray-700 rounded-lg p-2",children:[P(O),l.jsxs("div",{className:"flex-1 min-w-0",children:[l.jsx("span",{className:"text-sm text-gray-700 dark:text-gray-300 truncate block",children:O.name}),l.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:Pv(O.size)})]}),l.jsx("button",{onClick:()=>_(L),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-200",children:l.jsx(mo,{className:"h-3 w-3"})})]},L))}),l.jsxs("div",{...y(),className:se("relative border border-gray-300 dark:border-gray-600 rounded-lg p-3 transition-colors",m&&"border-primary-500 bg-primary-50 dark:bg-primary-900/20"),children:[l.jsx("input",{...x(),className:"hidden"}),l.jsxs("div",{className:"flex items-end space-x-2",children:[l.jsx(ne,{variant:"ghost",size:"sm",type:"button",className:"flex-shrink-0",children:l.jsx(Up,{className:"h-4 w-4"})}),l.jsx("div",{className:"flex-1",children:l.jsx("textarea",{ref:h,value:r,onChange:p,onKeyPress:k,placeholder:"Type a message...",className:"w-full resize-none border-0 bg-transparent text-sm placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-0",rows:1,style:{minHeight:"20px",maxHeight:"120px",height:"auto"},onInput:O=>{const L=O.target;L.style.height="auto",L.style.height=L.scrollHeight+"px"}})}),l.jsxs("div",{className:"relative",ref:g,children:[l.jsx(ne,{variant:"ghost",size:"sm",type:"button",className:"flex-shrink-0",onClick:()=>f(!d),children:l.jsx(Qv,{className:"h-4 w-4"})}),d&&l.jsx("div",{className:"absolute bottom-full right-0 mb-2 z-50",children:l.jsx(yN,{onEmojiSelect:A})})]}),l.jsx(ne,{onClick:v,disabled:!r.trim()&&s.length===0||c,size:"sm",className:"flex-shrink-0",children:l.jsx(aN,{className:"h-4 w-4"})})]}),m&&l.jsx("div",{className:"absolute inset-0 bg-primary-500/10 rounded-lg flex items-center justify-center",children:l.jsxs("div",{className:"text-center",children:[l.jsx(Up,{className:"h-8 w-8 text-primary-600 mx-auto mb-2"}),l.jsx("p",{className:"text-sm text-primary-600 font-medium",children:"Drop files here to attach"})]})})]})]})}function yC({chat:e,onToggleSidebar:t}){const{user:r}=nr(),n=()=>{if(e.type==="private"){const u=e.members.find(d=>d._id!==(r==null?void 0:r._id));return(u==null?void 0:u.username)||"Unknown User"}return e.name||"Unnamed Group"},i=()=>{if(e.type==="private"){const u=e.members.find(d=>d._id!==(r==null?void 0:r._id));return u==null?void 0:u.avatarUrl}return e.avatarUrl},a=()=>{if(e.type==="private"){const u=e.members.find(d=>d._id!==(r==null?void 0:r._id));return u==null?void 0:u.status}},s=n(),o=i(),c=a();return l.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800",children:[l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx(ne,{variant:"ghost",size:"sm",onClick:t,className:"lg:hidden",children:l.jsx(qv,{className:"h-5 w-5"})}),l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsxs("div",{className:"relative",children:[o?l.jsx("img",{src:o,alt:s,className:"w-10 h-10 rounded-full object-cover"}):l.jsx("div",{className:"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center",children:l.jsx("span",{className:"text-white font-medium text-sm",children:tn(s)})}),e.type==="private"&&c&&l.jsx("div",{className:se("absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-gray-800",Fa(c))})]}),l.jsxs("div",{children:[l.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:s}),e.type==="private"&&c&&l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:za(c)}),e.type==="group"&&l.jsxs("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:[e.members.length," members"]})]})]})]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[e.type==="private"&&l.jsxs(l.Fragment,{children:[l.jsx(ne,{variant:"ghost",size:"sm",children:l.jsx(Kv,{className:"h-5 w-5"})}),l.jsx(ne,{variant:"ghost",size:"sm",children:l.jsx(Zo,{className:"h-5 w-5"})})]}),e.type==="group"&&l.jsx(ne,{variant:"ghost",size:"sm",children:l.jsx(Jo,{className:"h-5 w-5"})}),l.jsx(ne,{variant:"ghost",size:"sm",children:l.jsx(Sd,{className:"h-5 w-5"})}),l.jsx(ne,{variant:"ghost",size:"sm",children:l.jsx(Hv,{className:"h-5 w-5"})})]})]})}function xC({chat:e,onToggleSidebar:t,sidebarOpen:r}){const{user:n}=nr(),{messages:i,loadMessages:a,typingUsers:s}=We(),[o,c]=S.useState(!1),[u,d]=S.useState(!0),f=S.useRef(null),h=i[e._id]||[];S.useEffect(()=>{(async()=>{c(!0);try{const m=await a(e._id,1);d(m)}catch(m){console.error("Failed to load messages:",m)}finally{c(!1)}})()},[e._id,a]),S.useEffect(()=>{w()},[h]);const w=()=>{var x;(x=f.current)==null||x.scrollIntoView({behavior:"smooth"})},g=async()=>{if(!(o||!u)){c(!0);try{const x=await a(e._id,Math.ceil(h.length/50)+1);d(x)}catch(x){console.error("Failed to load more messages:",x)}finally{c(!1)}}},y=s[e._id]||[];return l.jsxs("div",{className:"flex-1 flex flex-col bg-white dark:bg-gray-800",children:[l.jsx(yC,{chat:e,onToggleSidebar:t,sidebarOpen:r}),l.jsx("div",{className:"flex-1 overflow-y-auto",children:o&&h.length===0?l.jsx("div",{className:"flex items-center justify-center h-full",children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"}),l.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"Loading messages..."})]})}):l.jsxs(l.Fragment,{children:[u&&l.jsx("div",{className:"p-4 text-center",children:l.jsx(ne,{variant:"ghost",size:"sm",onClick:g,disabled:o,children:o?"Loading...":"Load more messages"})}),l.jsx(vN,{messages:h,currentUserId:(n==null?void 0:n._id)||""}),y.length>0&&l.jsx("div",{className:"px-4 py-2",children:l.jsxs("div",{className:"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400",children:[l.jsxs("div",{className:"flex space-x-1",children:[l.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),l.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),l.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),l.jsx("span",{children:y.length===1?`${y[0]} is typing...`:`${y.length} people are typing...`})]})}),l.jsx("div",{ref:f})]})}),l.jsx("div",{className:"border-t border-gray-200 dark:border-gray-700 p-4",children:l.jsx(vC,{chatId:e._id,onSendMessage:(x,m,p,v)=>{zt.sendMessage(e._id,x,m,p,v)}})})]})}function wC({}){const[e,t]=S.useState(!1),[r,n]=S.useState(!1);return l.jsxs("div",{className:"flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:[l.jsxs("div",{className:"text-center max-w-md mx-auto px-4",children:[l.jsx("div",{className:"w-24 h-24 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center mx-auto mb-6",children:l.jsx(xr,{className:"w-12 h-12 text-primary-600 dark:text-primary-400"})}),l.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Welcome to KingChat"}),l.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-400 mb-8",children:"Start a conversation with your friends or create a group chat to stay connected."}),l.jsxs("div",{className:"space-y-4",children:[l.jsxs(ne,{size:"lg",className:"w-full",onClick:()=>t(!0),children:[l.jsx(rN,{className:"w-5 h-5 mr-2"}),"Start New Chat"]}),l.jsxs(ne,{variant:"secondary",size:"lg",className:"w-full",onClick:()=>n(!0),children:[l.jsx(Jo,{className:"w-5 h-5 mr-2"}),"Create Group"]})]}),l.jsxs("div",{className:"mt-12 text-left",children:[l.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-white mb-3",children:"Quick Tips:"}),l.jsxs("ul",{className:"text-sm text-gray-600 dark:text-gray-400 space-y-2",children:[l.jsxs("li",{className:"flex items-start",children:[l.jsx("span",{className:"w-1.5 h-1.5 bg-primary-600 rounded-full mt-2 mr-3 flex-shrink-0"}),"Select a chat from the sidebar to start messaging"]}),l.jsxs("li",{className:"flex items-start",children:[l.jsx("span",{className:"w-1.5 h-1.5 bg-primary-600 rounded-full mt-2 mr-3 flex-shrink-0"}),"Add friends to expand your network"]}),l.jsxs("li",{className:"flex items-start",children:[l.jsx("span",{className:"w-1.5 h-1.5 bg-primary-600 rounded-full mt-2 mr-3 flex-shrink-0"}),"Create groups for team discussions"]}),l.jsxs("li",{className:"flex items-start",children:[l.jsx("span",{className:"w-1.5 h-1.5 bg-primary-600 rounded-full mt-2 mr-3 flex-shrink-0"}),"Use the search bar to find specific chats"]})]})]})]}),l.jsx(Gv,{isOpen:e,onClose:()=>t(!1),onCreateChat:()=>t(!1)}),l.jsx(Xv,{isOpen:r,onClose:()=>n(!1),onCreateGroup:()=>n(!1)})]})}function bC(){const{chats:e,currentChat:t,loadChats:r,isLoading:n}=We(),[i,a]=S.useState(!0);return S.useEffect(()=>{r()},[r]),n?l.jsx("div",{className:"h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:l.jsx(vd,{size:"lg"})}):l.jsxs("div",{className:"h-screen flex bg-gray-50 dark:bg-gray-900",children:[l.jsx("div",{className:`${i?"w-80":"w-0"} transition-all duration-300 overflow-hidden`,children:l.jsx(hN,{chats:e,currentChat:t,onToggleSidebar:()=>a(!i)})}),l.jsx("div",{className:"flex-1 flex flex-col",children:t?l.jsx(xC,{chat:t,onToggleSidebar:()=>a(!i),sidebarOpen:i}):l.jsx(wC,{onToggleSidebar:()=>a(!i),sidebarOpen:i})})]})}function kC(){var c,u;const{user:e,updateProfile:t}=nr(),[r,n]=S.useState(!1),{register:i,handleSubmit:a,formState:{errors:s}}=Qo({defaultValues:{username:(e==null?void 0:e.username)||"",avatarUrl:(e==null?void 0:e.avatarUrl)||""}}),o=async d=>{n(!0);try{await t(d),le.success("Profile updated successfully!")}catch(f){le.error(f.message||"Failed to update profile")}finally{n(!1)}};return e?l.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[l.jsx("div",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:l.jsx("div",{className:"max-w-4xl mx-auto px-4 py-4",children:l.jsxs("div",{className:"flex items-center space-x-4",children:[l.jsx(ui,{to:"/",children:l.jsxs(ne,{variant:"ghost",size:"sm",children:[l.jsx($v,{className:"h-4 w-4 mr-2"}),"Back to Chat"]})}),l.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Profile Settings"})]})})}),l.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:l.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[l.jsx("div",{className:"lg:col-span-1",children:l.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6",children:[l.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Profile Picture"}),l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"relative inline-block",children:e.avatarUrl?l.jsx("img",{src:e.avatarUrl,alt:e.username,className:"w-32 h-32 rounded-full object-cover mx-auto"}):l.jsx("div",{className:"w-32 h-32 bg-primary-600 rounded-full flex items-center justify-center mx-auto",children:l.jsx("span",{className:"text-white font-medium text-3xl",children:tn(e.username)})})}),l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-4",children:e.username}),l.jsxs(ne,{variant:"secondary",size:"sm",className:"mt-4",disabled:!0,children:[l.jsx(lN,{className:"h-4 w-4 mr-2"}),"Change Photo"]})]})]})}),l.jsxs("div",{className:"lg:col-span-2",children:[l.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6",children:[l.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:"Profile Information"}),l.jsxs("form",{onSubmit:a(o),className:"space-y-6",children:[l.jsx("div",{children:l.jsx(qe,{...i("username",{required:"Username is required",minLength:{value:3,message:"Username must be at least 3 characters"},maxLength:{value:20,message:"Username cannot exceed 20 characters"},pattern:{value:/^[a-zA-Z0-9_]+$/,message:"Username can only contain letters, numbers, and underscores"}}),type:"text",label:"Username",placeholder:"Enter your username",error:(c=s.username)==null?void 0:c.message,leftIcon:l.jsx(Xo,{className:"h-4 w-4"})})}),l.jsx("div",{children:l.jsx(qe,{type:"email",label:"Email",value:e.email,disabled:!0,leftIcon:l.jsx(Go,{className:"h-4 w-4"}),helperText:"Email cannot be changed"})}),l.jsx("div",{children:l.jsx(qe,{...i("avatarUrl",{pattern:{value:/^https?:\/\/.+/,message:"Please enter a valid URL"}}),type:"url",label:"Avatar URL",placeholder:"https://example.com/avatar.jpg",error:(u=s.avatarUrl)==null?void 0:u.message,helperText:"Enter a URL to your profile picture"})}),l.jsx("div",{className:"flex justify-end",children:l.jsxs(ne,{type:"submit",loading:r,disabled:r,children:[l.jsx(iN,{className:"h-4 w-4 mr-2"}),"Save Changes"]})})]})]}),l.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mt-6",children:[l.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Account Information"}),l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"flex justify-between items-center py-2",children:[l.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Member since"}),l.jsx("span",{className:"text-sm text-gray-900 dark:text-white",children:new Date(e.createdAt).toLocaleDateString()})]}),l.jsxs("div",{className:"flex justify-between items-center py-2",children:[l.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Last updated"}),l.jsx("span",{className:"text-sm text-gray-900 dark:text-white",children:new Date(e.updatedAt).toLocaleDateString()})]}),l.jsxs("div",{className:"flex justify-between items-center py-2",children:[l.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Email verified"}),l.jsx("span",{className:se("text-sm font-medium",e.isEmailVerified?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"),children:e.isEmailVerified?"Verified":"Not verified"})]})]})]})]})]})})]}):l.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:l.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"User not found"})})}function SC(){const{user:e,updateSettings:t,logout:r}=nr(),[n,i]=S.useState(!1),{register:a,handleSubmit:s,watch:o,formState:{}}=Qo({defaultValues:{theme:(e==null?void 0:e.settings.theme)||"light",language:(e==null?void 0:e.settings.language)||"en",notifications:(e==null?void 0:e.settings.notifications)??!0,soundEnabled:(e==null?void 0:e.settings.soundEnabled)??!0}}),c=o("theme"),u=async f=>{i(!0);try{await t(f),f.theme==="dark"?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"),le.success("Settings updated successfully!")}catch(h){le.error(h.message||"Failed to update settings")}finally{i(!1)}},d=async()=>{try{await r(),le.success("Logged out successfully")}catch(f){le.error(f.message||"Failed to logout")}};return e?l.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[l.jsx("div",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:l.jsx("div",{className:"max-w-4xl mx-auto px-4 py-4",children:l.jsxs("div",{className:"flex items-center space-x-4",children:[l.jsx(ui,{to:"/",children:l.jsxs(ne,{variant:"ghost",size:"sm",children:[l.jsx($v,{className:"h-4 w-4 mr-2"}),"Back to Chat"]})}),l.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Settings"})]})})}),l.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6",children:[l.jsxs("div",{className:"flex items-center space-x-3 mb-6",children:[l.jsx("div",{className:"p-2 bg-primary-100 dark:bg-primary-900/20 rounded-lg",children:c==="dark"?l.jsx(eN,{className:"h-5 w-5 text-primary-600 dark:text-primary-400"}):l.jsx(sN,{className:"h-5 w-5 text-primary-600 dark:text-primary-400"})}),l.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Appearance"})]}),l.jsxs("form",{onSubmit:s(u),className:"space-y-6",children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Theme"}),l.jsxs("select",{...a("theme"),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500",children:[l.jsx("option",{value:"light",children:"Light"}),l.jsx("option",{value:"dark",children:"Dark"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Language"}),l.jsxs("select",{...a("language"),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500",children:[l.jsx("option",{value:"en",children:"English"}),l.jsx("option",{value:"zh",children:"中文"})]})]})]}),l.jsx("div",{className:"flex justify-end",children:l.jsx(ne,{type:"submit",loading:n,disabled:n,children:"Save Changes"})})]})]}),l.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6",children:[l.jsxs("div",{className:"flex items-center space-x-3 mb-6",children:[l.jsx("div",{className:"p-2 bg-primary-100 dark:bg-primary-900/20 rounded-lg",children:l.jsx(Qj,{className:"h-5 w-5 text-primary-600 dark:text-primary-400"})}),l.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Notifications"})]}),l.jsxs("form",{onSubmit:s(u),className:"space-y-6",children:[l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Push Notifications"}),l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Receive notifications for new messages"})]}),l.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[l.jsx("input",{...a("notifications"),type:"checkbox",className:"sr-only peer"}),l.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"})]})]}),l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Sound Notifications"}),l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Play sound when receiving messages"})]}),l.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[l.jsx("input",{...a("soundEnabled"),type:"checkbox",className:"sr-only peer"}),l.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"})]})]})]}),l.jsx("div",{className:"flex justify-end",children:l.jsx(ne,{type:"submit",loading:n,disabled:n,children:"Save Changes"})})]})]}),l.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6",children:[l.jsxs("div",{className:"flex items-center space-x-3 mb-6",children:[l.jsx("div",{className:"p-2 bg-red-100 dark:bg-red-900/20 rounded-lg",children:l.jsx(po,{className:"h-5 w-5 text-red-600 dark:text-red-400"})}),l.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Account Actions"})]}),l.jsx("div",{className:"space-y-4",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Logout"}),l.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Sign out of your account"})]}),l.jsxs(ne,{variant:"danger",onClick:d,children:[l.jsx(po,{className:"h-4 w-4 mr-2"}),"Logout"]})]})})]})]})})]}):l.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:l.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"User not found"})})}function EC(){const{user:e,isAuthenticated:t,isLoading:r,loadUser:n}=nr(),{loadChats:i,loadFriends:a,clearChats:s}=We();return S.useEffect(()=>{n()},[n]),S.useEffect(()=>(t&&e?(zt.connect(),i(),a(),zt.updateStatus("online")):(zt.disconnect(),s()),()=>{t&&zt.updateStatus("offline")}),[t,e,i,a,s]),r?l.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:l.jsx(vd,{size:"lg"})}):l.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:l.jsxs(Rw,{children:[l.jsx(ln,{path:"/login",element:t?l.jsx(An,{to:"/",replace:!0}):l.jsx(uN,{})}),l.jsx(ln,{path:"/register",element:t?l.jsx(An,{to:"/",replace:!0}):l.jsx(dN,{})}),l.jsx(ln,{path:"/",element:t?l.jsx(bC,{}):l.jsx(An,{to:"/login",replace:!0})}),l.jsx(ln,{path:"/profile",element:t?l.jsx(kC,{}):l.jsx(An,{to:"/login",replace:!0})}),l.jsx(ln,{path:"/settings",element:t?l.jsx(SC,{}):l.jsx(An,{to:"/login",replace:!0})}),l.jsx(ln,{path:"*",element:l.jsx(An,{to:"/",replace:!0})})]})})}Il.createRoot(document.getElementById("root")).render(l.jsx(Ve.StrictMode,{children:l.jsxs(Iw,{children:[l.jsx(EC,{}),l.jsx(P1,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#10b981",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})}));
