import axios, { AxiosInstance } from 'axios'
import toast from 'react-hot-toast'
import {
  ApiResponse,
  AuthResponse,
  LoginRequest,
  RegisterRequest,
  RefreshTokenResponse,
  UserProfile,
  Chat,
  FriendRequest,
  Message,
  FriendRequest,
  PaginatedResponse
} from '@/types'

class ApiClient {
  private client: AxiosInstance
  private refreshToken: string | null = null

  constructor() {
    this.client = axios.create({
      baseURL: '/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Load tokens from localStorage
    this.loadTokens()

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('accessToken')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // Response interceptor to handle token refresh
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true

          try {
            await this.refreshAccessToken()
            const newToken = localStorage.getItem('accessToken')
            if (newToken) {
              originalRequest.headers.Authorization = `Bearer ${newToken}`
              return this.client(originalRequest)
            }
          } catch (refreshError) {
            this.logout()
            window.location.href = '/login'
            return Promise.reject(refreshError)
          }
        }

        return Promise.reject(error)
      }
    )
  }

  private loadTokens() {
    this.refreshToken = localStorage.getItem('refreshToken')
  }

  private async refreshAccessToken() {
    if (!this.refreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await axios.post<ApiResponse<RefreshTokenResponse>>('/api/auth/refresh', {
      refreshToken: this.refreshToken
    })

    if (response.data.success && response.data.data) {
      localStorage.setItem('accessToken', response.data.data.accessToken)
      localStorage.setItem('refreshToken', response.data.data.refreshToken)
      this.refreshToken = response.data.data.refreshToken
    } else {
      throw new Error('Failed to refresh token')
    }
  }

  private handleError(error: any) {
    const message = error.response?.data?.message || error.message || 'An error occurred'
    toast.error(message)
    return Promise.reject(error)
  }

  // Auth methods
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    try {
      const response = await this.client.post<ApiResponse<AuthResponse>>('/auth/login', credentials)
      
      if (response.data.success && response.data.data) {
        localStorage.setItem('accessToken', response.data.data.accessToken)
        localStorage.setItem('refreshToken', response.data.data.refreshToken)
        this.refreshToken = response.data.data.refreshToken
        return response.data.data
      } else {
        throw new Error(response.data.message || 'Login failed')
      }
    } catch (error) {
      return this.handleError(error)
    }
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    try {
      const response = await this.client.post<ApiResponse<AuthResponse>>('/auth/register', userData)
      
      if (response.data.success && response.data.data) {
        localStorage.setItem('accessToken', response.data.data.accessToken)
        localStorage.setItem('refreshToken', response.data.data.refreshToken)
        this.refreshToken = response.data.data.refreshToken
        return response.data.data
      } else {
        throw new Error(response.data.message || 'Registration failed')
      }
    } catch (error) {
      return this.handleError(error)
    }
  }

  async logout(): Promise<void> {
    try {
      if (this.refreshToken) {
        await this.client.post('/auth/logout', { refreshToken: this.refreshToken })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      localStorage.removeItem('accessToken')
      localStorage.removeItem('refreshToken')
      this.refreshToken = null
    }
  }

  async logoutAll(): Promise<void> {
    try {
      if (this.refreshToken) {
        await this.client.post('/auth/logout-all', { refreshToken: this.refreshToken })
      }
    } catch (error) {
      console.error('Logout all error:', error)
    } finally {
      localStorage.removeItem('accessToken')
      localStorage.removeItem('refreshToken')
      this.refreshToken = null
    }
  }

  // User methods
  async getProfile(): Promise<UserProfile> {
    try {
      const response = await this.client.get<ApiResponse<{ user: UserProfile }>>('/user/profile')
      
      if (response.data.success && response.data.data) {
        return response.data.data.user
      } else {
        throw new Error(response.data.message || 'Failed to get profile')
      }
    } catch (error) {
      return this.handleError(error)
    }
  }

  async updateProfile(profileData: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const response = await this.client.put<ApiResponse<{ user: UserProfile }>>('/user/profile', profileData)
      
      if (response.data.success && response.data.data) {
        return response.data.data.user
      } else {
        throw new Error(response.data.message || 'Failed to update profile')
      }
    } catch (error) {
      return this.handleError(error)
    }
  }

  async updateSettings(settings: any): Promise<UserProfile> {
    try {
      const response = await this.client.put<ApiResponse<{ user: UserProfile }>>('/user/settings', settings)
      
      if (response.data.success && response.data.data) {
        return response.data.data.user
      } else {
        throw new Error(response.data.message || 'Failed to update settings')
      }
    } catch (error) {
      return this.handleError(error)
    }
  }

  async updateStatus(status: 'online' | 'away' | 'dnd' | 'offline'): Promise<UserProfile> {
    try {
      const response = await this.client.put<ApiResponse<{ user: UserProfile }>>('/user/status', { status })
      
      if (response.data.success && response.data.data) {
        return response.data.data.user
      } else {
        throw new Error(response.data.message || 'Failed to update status')
      }
    } catch (error) {
      return this.handleError(error)
    }
  }

  async searchUsers(query: string): Promise<UserProfile[]> {
    try {
      const response = await this.client.get<ApiResponse<{ users: UserProfile[] }>>(`/user/search?query=${encodeURIComponent(query)}`)
      
      if (response.data.success && response.data.data) {
        return response.data.data.users
      } else {
        throw new Error(response.data.message || 'Failed to search users')
      }
    } catch (error) {
      return this.handleError(error)
    }
  }

  // Friend methods
  async getFriends(): Promise<UserProfile[]> {
    try {
      const response = await this.client.get<ApiResponse<{ friends: UserProfile[] }>>('/friends/list')
      
      if (response.data.success && response.data.data) {
        return response.data.data.friends
      } else {
        throw new Error(response.data.message || 'Failed to get friends')
      }
    } catch (error) {
      return this.handleError(error)
    }
  }

  async sendFriendRequest(userId: string, message?: string): Promise<void> {
    try {
      const response = await this.client.post<ApiResponse>('/friends/request', { userId, message })
      
      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to send friend request')
      }
    } catch (error) {
      return this.handleError(error)
    }
  }

  async getFriendRequests(type: 'received' | 'sent' = 'received'): Promise<FriendRequest[]> {
    try {
      const response = await this.client.get<ApiResponse<{ requests: FriendRequest[] }>>(`/friends/requests?type=${type}`)
      
      if (response.data.success && response.data.data) {
        return response.data.data.requests
      } else {
        throw new Error(response.data.message || 'Failed to get friend requests')
      }
    } catch (error) {
      return this.handleError(error)
    }
  }

  async acceptFriendRequest(requestId: string): Promise<void> {
    try {
      const response = await this.client.post<ApiResponse>('/friends/accept', { requestId })
      
      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to accept friend request')
      }
    } catch (error) {
      return this.handleError(error)
    }
  }

  async rejectFriendRequest(requestId: string): Promise<void> {
    try {
      const response = await this.client.post<ApiResponse>('/friends/reject', { requestId })
      
      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to reject friend request')
      }
    } catch (error) {
      return this.handleError(error)
    }
  }

  async removeFriend(userId: string): Promise<void> {
    try {
      const response = await this.client.delete<ApiResponse>('/friends/remove', { data: { userId } })
      
      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to remove friend')
      }
    } catch (error) {
      return this.handleError(error)
    }
  }

  // Chat methods
  async getChats(): Promise<Chat[]> {
    try {
      const response = await this.client.get<ApiResponse<{ chats: Chat[] }>>('/chats/list')
      
      if (response.data.success && response.data.data) {
        return response.data.data.chats
      } else {
        throw new Error(response.data.message || 'Failed to get chats')
      }
    } catch (error) {
      return this.handleError(error)
    }
  }

  async createPrivateChat(friendId: string): Promise<Chat> {
    try {
      const response = await this.client.post<ApiResponse<{ chat: Chat }>>('/chats/create-private', { friendId })
      
      if (response.data.success && response.data.data) {
        return response.data.data.chat
      } else {
        throw new Error(response.data.message || 'Failed to create private chat')
      }
    } catch (error) {
      return this.handleError(error)
    }
  }

  async createGroupChat(name: string, members: string[], description?: string): Promise<Chat> {
    try {
      const response = await this.client.post<ApiResponse<{ chat: Chat }>>('/chats/create-group', {
        name,
        members,
        description
      })
      
      if (response.data.success && response.data.data) {
        return response.data.data.chat
      } else {
        throw new Error(response.data.message || 'Failed to create group chat')
      }
    } catch (error) {
      return this.handleError(error)
    }
  }

  async getChatMessages(chatId: string, page: number = 1, limit: number = 50): Promise<{ messages: Message[], hasMore: boolean }> {
    try {
      const response = await this.client.get<PaginatedResponse<Message>>(`/chats/${chatId}/messages?page=${page}&limit=${limit}`)
      
      if (response.data.success && response.data.data) {
        return {
          messages: response.data.data.items,
          hasMore: response.data.data.pagination.hasMore
        }
      } else {
        throw new Error(response.data.message || 'Failed to get chat messages')
      }
    } catch (error) {
      return this.handleError(error)
    }
  }

  async getChatDetails(chatId: string): Promise<Chat> {
    try {
      const response = await this.client.get<ApiResponse<{ chat: Chat }>>(`/chats/${chatId}`)
      
      if (response.data.success && response.data.data) {
        return response.data.data.chat
      } else {
        throw new Error(response.data.message || 'Failed to get chat details')
      }
    } catch (error) {
      return this.handleError(error)
    }
  }

  // Upload methods
  async uploadImage(file: File): Promise<{ url: string, filename: string, size: number }> {
    try {
      const formData = new FormData()
      formData.append('image', file)

      const response = await this.client.post<ApiResponse<{ url: string, filename: string, size: number }>>('/upload/image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
      
      if (response.data.success && response.data.data) {
        return response.data.data
      } else {
        throw new Error(response.data.message || 'Failed to upload image')
      }
    } catch (error) {
      return this.handleError(error)
    }
  }

  async uploadFile(file: File): Promise<{ url: string, filename: string, size: number }> {
    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await this.client.post<ApiResponse<{ url: string, filename: string, size: number }>>('/upload/file', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
      
      if (response.data.success && response.data.data) {
        return response.data.data
      } else {
        throw new Error(response.data.message || 'Failed to upload file')
      }
    } catch (error) {
      return this.handleError(error)
    }
  }
}

export const apiClient = new ApiClient()
export default apiClient
