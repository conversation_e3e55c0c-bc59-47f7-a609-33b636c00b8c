const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Chat = require('../models/Chat');
const Message = require('../models/Message');
const { authenticateSocket } = require('../middleware/auth');

// Store active connections
const activeConnections = new Map();

const setupSocketHandlers = (io) => {
  // Authentication middleware
  io.use(authenticateSocket);

  io.on('connection', (socket) => {
    console.log(`User ${socket.user.username} connected with socket ${socket.id}`);
    
    // Store user connection
    activeConnections.set(socket.userId, {
      socketId: socket.id,
      user: socket.user,
      connectedAt: new Date()
    });

    // Update user status to online
    socket.user.updateStatus('online');

    // Join user to their personal room for notifications
    socket.join(`user:${socket.userId}`);

    // Join user to all their chat rooms
    Chat.find({ members: socket.userId })
      .then(chats => {
        chats.forEach(chat => {
          socket.join(`chat:${chat._id}`);
        });
      })
      .catch(error => {
        console.error('Error joining chat rooms:', error);
      });

    // Handle sending messages
    socket.on('message:send', async (data) => {
      try {
        const { chatId, content, type = 'text', replyTo, metadata } = data;

        // Validate input
        if (!chatId || !content) {
          socket.emit('error', { message: 'Chat ID and content are required' });
          return;
        }

        // Check if user is member of the chat
        const chat = await Chat.findById(chatId);
        if (!chat || !chat.members.includes(socket.userId)) {
          socket.emit('error', { message: 'You are not a member of this chat' });
          return;
        }

        // Create message
        const message = new Message({
          chatId,
          sender: socket.userId,
          content,
          type,
          replyTo,
          metadata
        });

        await message.save();
        await message.populate('sender', 'username avatarUrl');
        await message.populate('replyTo', 'content sender type');
        if (message.replyTo) {
          await message.populate('replyTo.sender', 'username');
        }

        // Update chat's last message
        await chat.updateLastMessage({
          content,
          sender: socket.userId,
          type
        });

        // Emit message to all chat members
        io.to(`chat:${chatId}`).emit('message:new', {
          message,
          chatId
        });

        // Emit typing stop event
        socket.to(`chat:${chatId}`).emit('typing:stop', {
          chatId,
          userId: socket.userId,
          username: socket.user.username
        });

      } catch (error) {
        console.error('Send message error:', error);
        socket.emit('error', { message: 'Failed to send message' });
      }
    });

    // Handle typing indicators
    socket.on('typing:start', (data) => {
      const { chatId } = data;
      if (chatId) {
        socket.to(`chat:${chatId}`).emit('typing:start', {
          chatId,
          userId: socket.userId,
          username: socket.user.username
        });
      }
    });

    socket.on('typing:stop', (data) => {
      const { chatId } = data;
      if (chatId) {
        socket.to(`chat:${chatId}`).emit('typing:stop', {
          chatId,
          userId: socket.userId,
          username: socket.user.username
        });
      }
    });

    // Handle message read receipts
    socket.on('message:read', async (data) => {
      try {
        const { messageId, chatId } = data;

        if (!messageId || !chatId) {
          socket.emit('error', { message: 'Message ID and Chat ID are required' });
          return;
        }

        // Check if user is member of the chat
        const chat = await Chat.findById(chatId);
        if (!chat || !chat.members.includes(socket.userId)) {
          socket.emit('error', { message: 'You are not a member of this chat' });
          return;
        }

        // Mark message as read
        const message = await Message.findById(messageId);
        if (message) {
          await message.markAsRead(socket.userId);
          
          // Emit read receipt to chat members
          io.to(`chat:${chatId}`).emit('message:read', {
            messageId,
            chatId,
            userId: socket.userId,
            username: socket.user.username,
            readAt: new Date()
          });
        }

      } catch (error) {
        console.error('Message read error:', error);
        socket.emit('error', { message: 'Failed to mark message as read' });
      }
    });

    // Handle message reactions
    socket.on('message:react', async (data) => {
      try {
        const { messageId, emoji, chatId } = data;

        if (!messageId || !emoji || !chatId) {
          socket.emit('error', { message: 'Message ID, emoji, and Chat ID are required' });
          return;
        }

        // Check if user is member of the chat
        const chat = await Chat.findById(chatId);
        if (!chat || !chat.members.includes(socket.userId)) {
          socket.emit('error', { message: 'You are not a member of this chat' });
          return;
        }

        const message = await Message.findById(messageId);
        if (message) {
          await message.addReaction(socket.userId, emoji);
          
          // Emit reaction to chat members
          io.to(`chat:${chatId}`).emit('message:reaction', {
            messageId,
            chatId,
            userId: socket.userId,
            username: socket.user.username,
            emoji,
            reactions: message.reactions
          });
        }

      } catch (error) {
        console.error('Message reaction error:', error);
        socket.emit('error', { message: 'Failed to add reaction' });
      }
    });

    // Handle message revocation
    socket.on('message:revoke', async (data) => {
      try {
        const { messageId, chatId } = data;

        if (!messageId || !chatId) {
          socket.emit('error', { message: 'Message ID and Chat ID are required' });
          return;
        }

        const message = await Message.findById(messageId);
        if (!message) {
          socket.emit('error', { message: 'Message not found' });
          return;
        }

        // Check if user is the sender
        if (message.sender.toString() !== socket.userId.toString()) {
          socket.emit('error', { message: 'You can only revoke your own messages' });
          return;
        }

        // Check if message can be revoked (within 2 minutes)
        if (!message.canRevoke()) {
          socket.emit('error', { message: 'Message can only be revoked within 2 minutes' });
          return;
        }

        await message.revoke();
        
        // Emit revocation to chat members
        io.to(`chat:${chatId}`).emit('message:revoked', {
          messageId,
          chatId,
          userId: socket.userId,
          username: socket.user.username
        });

      } catch (error) {
        console.error('Message revocation error:', error);
        socket.emit('error', { message: 'Failed to revoke message' });
      }
    });

    // Handle user status updates
    socket.on('user:status', async (data) => {
      try {
        const { status } = data;
        
        if (!['online', 'away', 'dnd', 'offline'].includes(status)) {
          socket.emit('error', { message: 'Invalid status' });
          return;
        }

        await socket.user.updateStatus(status);
        
        // Emit status update to all user's friends
        const friends = await getFriendsList(socket.userId);
        friends.forEach(friend => {
          io.to(`user:${friend._id}`).emit('user:status', {
            userId: socket.userId,
            username: socket.user.username,
            status,
            lastSeen: socket.user.lastSeen
          });
        });

      } catch (error) {
        console.error('Status update error:', error);
        socket.emit('error', { message: 'Failed to update status' });
      }
    });

    // Handle friend request notifications
    socket.on('friend:request', async (data) => {
      try {
        const { toUserId } = data;
        
        if (!toUserId) {
          socket.emit('error', { message: 'User ID is required' });
          return;
        }

        // Emit friend request to target user
        io.to(`user:${toUserId}`).emit('friend:request', {
          fromUserId: socket.userId,
          fromUsername: socket.user.username,
          fromAvatarUrl: socket.user.avatarUrl
        });

      } catch (error) {
        console.error('Friend request notification error:', error);
        socket.emit('error', { message: 'Failed to send friend request notification' });
      }
    });

    // Handle disconnect
    socket.on('disconnect', async () => {
      console.log(`User ${socket.user.username} disconnected`);
      
      // Remove from active connections
      activeConnections.delete(socket.userId);
      
      // Update user status to offline
      try {
        await socket.user.updateStatus('offline');
        
        // Emit status update to all user's friends
        const friends = await getFriendsList(socket.userId);
        friends.forEach(friend => {
          io.to(`user:${friend._id}`).emit('user:status', {
            userId: socket.userId,
            username: socket.user.username,
            status: 'offline',
            lastSeen: new Date()
          });
        });
      } catch (error) {
        console.error('Disconnect error:', error);
      }
    });
  });
};

// Helper function to get friends list
const getFriendsList = async (userId) => {
  try {
    const FriendRequest = require('../models/FriendRequest');
    const friendRequests = await FriendRequest.find({
      $or: [
        { from: userId, status: 'accepted' },
        { to: userId, status: 'accepted' }
      ]
    });

    const friends = friendRequests.map(request => {
      return request.from.toString() === userId.toString() ? request.to : request.from;
    });

    return friends;
  } catch (error) {
    console.error('Get friends list error:', error);
    return [];
  }
};

// Helper function to get online users
const getOnlineUsers = () => {
  return Array.from(activeConnections.values()).map(conn => ({
    userId: conn.user._id,
    username: conn.user.username,
    avatarUrl: conn.user.avatarUrl,
    status: conn.user.status,
    connectedAt: conn.connectedAt
  }));
};

module.exports = {
  setupSocketHandlers,
  getOnlineUsers,
  activeConnections
};
