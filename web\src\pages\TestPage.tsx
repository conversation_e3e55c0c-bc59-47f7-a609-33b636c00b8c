import { useState } from 'react'
import { AddFriendModal } from '@/components/modals/AddFriendModal'
import { FriendRequestsModal } from '@/components/modals/FriendRequestsModal'
import { Button } from '@/components/ui/Button'
import { UserPlus, Bell, Settings } from 'lucide-react'
import { useNavigate } from 'react-router-dom'

export function TestPage() {
  const [showAddFriendModal, setShowAddFriendModal] = useState(false)
  const [showFriendRequestsModal, setShowFriendRequestsModal] = useState(false)
  const navigate = useNavigate()

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          KingChat Feature Test
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Settings Navigation Test */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Settings Navigation
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Test the settings button navigation functionality.
            </p>
            <Button
              variant="primary"
              onClick={() => navigate('/settings')}
              className="w-full"
            >
              <Settings className="h-4 w-4 mr-2" />
              Go to Settings
            </Button>
          </div>

          {/* Add Friend Modal Test */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Add Friend
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Test the add friend modal with user search functionality.
            </p>
            <Button
              variant="primary"
              onClick={() => setShowAddFriendModal(true)}
              className="w-full"
            >
              <UserPlus className="h-4 w-4 mr-2" />
              Add Friend
            </Button>
          </div>

          {/* Friend Requests Modal Test */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Friend Requests
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Test the friend requests modal with accept/reject functionality.
            </p>
            <Button
              variant="secondary"
              onClick={() => setShowFriendRequestsModal(true)}
              className="w-full"
            >
              <Bell className="h-4 w-4 mr-2" />
              View Requests
            </Button>
          </div>

          {/* Chat Navigation Test */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Chat Navigation
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Navigate back to the main chat interface.
            </p>
            <Button
              variant="secondary"
              onClick={() => navigate('/')}
              className="w-full"
            >
              Back to Chat
            </Button>
          </div>

          {/* Profile Navigation Test */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Profile Navigation
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Navigate to the user profile page.
            </p>
            <Button
              variant="secondary"
              onClick={() => navigate('/profile')}
              className="w-full"
            >
              View Profile
            </Button>
          </div>

          {/* Status Card */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Status
            </h2>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Settings Button:</span>
                <span className="text-sm font-medium text-green-600">✓ Fixed</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Add Friend Modal:</span>
                <span className="text-sm font-medium text-green-600">✓ Added</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Friend Requests:</span>
                <span className="text-sm font-medium text-green-600">✓ Added</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Chat Store:</span>
                <span className="text-sm font-medium text-green-600">✓ Enhanced</span>
              </div>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
            Testing Instructions
          </h3>
          <ul className="list-disc list-inside space-y-1 text-blue-800 dark:text-blue-200">
            <li>Click "Go to Settings" to test the settings navigation</li>
            <li>Click "Add Friend" to test the friend search and request functionality</li>
            <li>Click "View Requests" to test the friend request management</li>
            <li>Navigate between pages to ensure all routes work correctly</li>
            <li>Test both light and dark themes if available</li>
          </ul>
        </div>
      </div>

      {/* Modals */}
      <AddFriendModal
        isOpen={showAddFriendModal}
        onClose={() => setShowAddFriendModal(false)}
      />

      <FriendRequestsModal
        isOpen={showFriendRequestsModal}
        onClose={() => setShowFriendRequestsModal(false)}
      />
    </div>
  )
}
