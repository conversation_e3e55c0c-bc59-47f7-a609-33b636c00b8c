const mongoose = require('mongoose');

const friendRequestSchema = new mongoose.Schema({
  from: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Sender is required']
  },
  to: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Receiver is required']
  },
  status: {
    type: String,
    enum: ['pending', 'accepted', 'rejected'],
    default: 'pending'
  },
  message: {
    type: String,
    maxlength: [200, 'Message cannot exceed 200 characters'],
    default: ''
  }
}, {
  timestamps: true
});

// Compound index to ensure unique friend requests
friendRequestSchema.index({ from: 1, to: 1 }, { unique: true });

// Index for querying friend requests
friendRequestSchema.index({ to: 1, status: 1 });
friendRequestSchema.index({ from: 1, status: 1 });

// Pre-save middleware to prevent self-friend requests
friendRequestSchema.pre('save', function(next) {
  if (this.from.toString() === this.to.toString()) {
    const error = new Error('Cannot send friend request to yourself');
    error.statusCode = 400;
    return next(error);
  }
  next();
});

// Static method to check if users are already friends
friendRequestSchema.statics.areFriends = async function(userId1, userId2) {
  const acceptedRequest = await this.findOne({
    $or: [
      { from: userId1, to: userId2, status: 'accepted' },
      { from: userId2, to: userId1, status: 'accepted' }
    ]
  });
  return !!acceptedRequest;
};

// Static method to get pending friend requests for a user
friendRequestSchema.statics.getPendingRequests = async function(userId) {
  return await this.find({ to: userId, status: 'pending' })
    .populate('from', 'username avatarUrl status lastSeen')
    .sort({ createdAt: -1 });
};

// Static method to get sent friend requests for a user
friendRequestSchema.statics.getSentRequests = async function(userId) {
  return await this.find({ from: userId, status: 'pending' })
    .populate('to', 'username avatarUrl status lastSeen')
    .sort({ createdAt: -1 });
};

// Method to accept friend request
friendRequestSchema.methods.accept = function() {
  this.status = 'accepted';
  return this.save();
};

// Method to reject friend request
friendRequestSchema.methods.reject = function() {
  this.status = 'rejected';
  return this.save();
};

// Transform JSON output
friendRequestSchema.set('toJSON', {
  transform: function(doc, ret) {
    delete ret.__v;
    return ret;
  }
});

module.exports = mongoose.model('FriendRequest', friendRequestSchema);
