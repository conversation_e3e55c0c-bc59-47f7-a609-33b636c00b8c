const express = require('express');
const { body, validationResult } = require('express-validator');
const Chat = require('../models/Chat');
const Message = require('../models/Message');
const FriendRequest = require('../models/FriendRequest');

const router = express.Router();

// @route   GET /api/chats/list
// @desc    Get user's chats
// @access  Private
router.get('/list', async (req, res) => {
  try {
    const chats = await Chat.getUserChats(req.user._id);

    res.json({
      success: true,
      data: {
        chats
      }
    });

  } catch (error) {
    console.error('Get chats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching chats'
    });
  }
});

// @route   POST /api/chats/create-private
// @desc    Create or get private chat with a friend
// @access  Private
router.post('/create-private', [
  body('friendId')
    .isMongoId()
    .withMessage('Valid friend ID is required')
], async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { friendId } = req.body;

    // Check if users are friends
    const areFriends = await FriendRequest.areFriends(req.user._id, friendId);
    if (!areFriends) {
      return res.status(403).json({
        success: false,
        message: 'You can only create private chats with friends'
      });
    }

    // Find or create private chat
    const chat = await Chat.findOrCreatePrivateChat(req.user._id, friendId);

    res.json({
      success: true,
      message: 'Private chat created/found successfully',
      data: {
        chat
      }
    });

  } catch (error) {
    console.error('Create private chat error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating private chat'
    });
  }
});

// @route   POST /api/chats/create-group
// @desc    Create group chat
// @access  Private
router.post('/create-group', [
  body('name')
    .isLength({ min: 1, max: 50 })
    .withMessage('Group name must be between 1 and 50 characters'),
  body('members')
    .isArray({ min: 1 })
    .withMessage('At least one member is required'),
  body('members.*')
    .isMongoId()
    .withMessage('All members must have valid IDs'),
  body('description')
    .optional()
    .isLength({ max: 200 })
    .withMessage('Description cannot exceed 200 characters')
], async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { name, members, description = '' } = req.body;

    // Add current user to members if not already included
    const allMembers = [...new Set([req.user._id, ...members])];

    // Check if all members are friends with the creator
    for (const memberId of members) {
      const areFriends = await FriendRequest.areFriends(req.user._id, memberId);
      if (!areFriends) {
        return res.status(403).json({
          success: false,
          message: 'You can only add friends to group chats'
        });
      }
    }

    // Create group chat
    const chat = new Chat({
      type: 'group',
      name,
      description,
      members: allMembers,
      createdBy: req.user._id
    });

    await chat.save();
    await chat.populate('members', 'username avatarUrl status lastSeen');

    res.status(201).json({
      success: true,
      message: 'Group chat created successfully',
      data: {
        chat
      }
    });

  } catch (error) {
    console.error('Create group chat error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating group chat'
    });
  }
});

// @route   GET /api/chats/:chatId/messages
// @desc    Get chat messages with pagination
// @access  Private
router.get('/:chatId/messages', async (req, res) => {
  try {
    const { chatId } = req.params;
    const { page = 1, limit = 50 } = req.query;

    // Check if user is member of the chat
    const chat = await Chat.findById(chatId);
    if (!chat) {
      return res.status(404).json({
        success: false,
        message: 'Chat not found'
      });
    }

    if (!chat.members.includes(req.user._id)) {
      return res.status(403).json({
        success: false,
        message: 'You are not a member of this chat'
      });
    }

    // Get messages
    const messages = await Message.getChatMessages(
      chatId, 
      parseInt(page), 
      parseInt(limit)
    );

    res.json({
      success: true,
      data: {
        messages: messages.reverse(), // Reverse to show oldest first
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          hasMore: messages.length === parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Get chat messages error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching chat messages'
    });
  }
});

// @route   GET /api/chats/:chatId
// @desc    Get chat details
// @access  Private
router.get('/:chatId', async (req, res) => {
  try {
    const { chatId } = req.params;

    const chat = await Chat.findById(chatId)
      .populate('members', 'username avatarUrl status lastSeen')
      .populate('admins', 'username avatarUrl')
      .populate('createdBy', 'username avatarUrl');

    if (!chat) {
      return res.status(404).json({
        success: false,
        message: 'Chat not found'
      });
    }

    // Check if user is member of the chat
    if (!chat.members.some(member => member._id.toString() === req.user._id.toString())) {
      return res.status(403).json({
        success: false,
        message: 'You are not a member of this chat'
      });
    }

    res.json({
      success: true,
      data: {
        chat
      }
    });

  } catch (error) {
    console.error('Get chat details error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching chat details'
    });
  }
});

// @route   POST /api/chats/:chatId/members
// @desc    Add member to group chat
// @access  Private
router.post('/:chatId/members', [
  body('userId')
    .isMongoId()
    .withMessage('Valid user ID is required')
], async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { chatId } = req.params;
    const { userId } = req.body;

    const chat = await Chat.findById(chatId);
    if (!chat) {
      return res.status(404).json({
        success: false,
        message: 'Chat not found'
      });
    }

    // Check if user is admin
    if (!chat.admins.includes(req.user._id)) {
      return res.status(403).json({
        success: false,
        message: 'Only admins can add members'
      });
    }

    // Add member
    await chat.addMember(userId, req.user._id);
    await chat.populate('members', 'username avatarUrl status lastSeen');

    res.json({
      success: true,
      message: 'Member added successfully',
      data: {
        chat
      }
    });

  } catch (error) {
    console.error('Add member error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Server error while adding member'
    });
  }
});

// @route   DELETE /api/chats/:chatId/members/:userId
// @desc    Remove member from group chat
// @access  Private
router.delete('/:chatId/members/:userId', async (req, res) => {
  try {
    const { chatId, userId } = req.params;

    const chat = await Chat.findById(chatId);
    if (!chat) {
      return res.status(404).json({
        success: false,
        message: 'Chat not found'
      });
    }

    // Remove member
    await chat.removeMember(userId, req.user._id);
    await chat.populate('members', 'username avatarUrl status lastSeen');

    res.json({
      success: true,
      message: 'Member removed successfully',
      data: {
        chat
      }
    });

  } catch (error) {
    console.error('Remove member error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Server error while removing member'
    });
  }
});

// @route   POST /api/chats/:chatId/leave
// @desc    Leave group chat
// @access  Private
router.post('/:chatId/leave', async (req, res) => {
  try {
    const { chatId } = req.params;

    const chat = await Chat.findById(chatId);
    if (!chat) {
      return res.status(404).json({
        success: false,
        message: 'Chat not found'
      });
    }

    // Leave chat
    await chat.removeMember(req.user._id, req.user._id);

    res.json({
      success: true,
      message: 'Left chat successfully'
    });

  } catch (error) {
    console.error('Leave chat error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Server error while leaving chat'
    });
  }
});

module.exports = router;
