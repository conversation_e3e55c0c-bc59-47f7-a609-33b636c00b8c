const express = require('express');
const { body, validationResult } = require('express-validator');
const FriendRequest = require('../models/FriendRequest');
const User = require('../models/User');

const router = express.Router();

// @route   GET /api/friends/list
// @desc    Get user's friends list
// @access  Private
router.get('/list', async (req, res) => {
  try {
    // Get all accepted friend requests where user is either sender or receiver
    const friendRequests = await FriendRequest.find({
      $or: [
        { from: req.user._id, status: 'accepted' },
        { to: req.user._id, status: 'accepted' }
      ]
    })
    .populate('from', 'username avatarUrl status lastSeen')
    .populate('to', 'username avatarUrl status lastSeen');

    // Extract friends (excluding current user)
    const friends = friendRequests.map(request => {
      const friend = request.from._id.toString() === req.user._id.toString() 
        ? request.to 
        : request.from;
      return {
        _id: friend._id,
        username: friend.username,
        avatarUrl: friend.avatarUrl,
        status: friend.status,
        lastSeen: friend.lastSeen,
        friendshipDate: request.createdAt
      };
    });

    res.json({
      success: true,
      data: {
        friends
      }
    });

  } catch (error) {
    console.error('Get friends list error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching friends list'
    });
  }
});

// @route   POST /api/friends/request
// @desc    Send friend request
// @access  Private
router.post('/request', [
  body('userId')
    .isMongoId()
    .withMessage('Valid user ID is required'),
  body('message')
    .optional()
    .isLength({ max: 200 })
    .withMessage('Message cannot exceed 200 characters')
], async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { userId, message = '' } = req.body;

    // Check if user exists
    const targetUser = await User.findById(userId);
    if (!targetUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if trying to add self as friend
    if (userId === req.user._id.toString()) {
      return res.status(400).json({
        success: false,
        message: 'Cannot send friend request to yourself'
      });
    }

    // Check if already friends
    const areFriends = await FriendRequest.areFriends(req.user._id, userId);
    if (areFriends) {
      return res.status(400).json({
        success: false,
        message: 'You are already friends with this user'
      });
    }

    // Check if friend request already exists
    const existingRequest = await FriendRequest.findOne({
      $or: [
        { from: req.user._id, to: userId },
        { from: userId, to: req.user._id }
      ]
    });

    if (existingRequest) {
      if (existingRequest.status === 'pending') {
        return res.status(400).json({
          success: false,
          message: 'Friend request already exists'
        });
      } else if (existingRequest.status === 'rejected') {
        // Update existing rejected request to pending
        existingRequest.status = 'pending';
        existingRequest.from = req.user._id;
        existingRequest.to = userId;
        existingRequest.message = message;
        await existingRequest.save();
      }
    } else {
      // Create new friend request
      const friendRequest = new FriendRequest({
        from: req.user._id,
        to: userId,
        message
      });
      await friendRequest.save();
    }

    res.json({
      success: true,
      message: 'Friend request sent successfully'
    });

  } catch (error) {
    console.error('Send friend request error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while sending friend request'
    });
  }
});

// @route   GET /api/friends/requests
// @desc    Get pending friend requests
// @access  Private
router.get('/requests', async (req, res) => {
  try {
    const { type = 'received' } = req.query; // 'received' or 'sent'

    let requests;
    if (type === 'sent') {
      requests = await FriendRequest.getSentRequests(req.user._id);
    } else {
      requests = await FriendRequest.getPendingRequests(req.user._id);
    }

    res.json({
      success: true,
      data: {
        requests
      }
    });

  } catch (error) {
    console.error('Get friend requests error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching friend requests'
    });
  }
});

// @route   POST /api/friends/accept
// @desc    Accept friend request
// @access  Private
router.post('/accept', [
  body('requestId')
    .isMongoId()
    .withMessage('Valid request ID is required')
], async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { requestId } = req.body;

    // Find friend request
    const friendRequest = await FriendRequest.findById(requestId);
    if (!friendRequest) {
      return res.status(404).json({
        success: false,
        message: 'Friend request not found'
      });
    }

    // Check if user is the receiver
    if (friendRequest.to.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You can only accept friend requests sent to you'
      });
    }

    // Check if request is still pending
    if (friendRequest.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Friend request has already been processed'
      });
    }

    // Accept the request
    await friendRequest.accept();

    res.json({
      success: true,
      message: 'Friend request accepted successfully'
    });

  } catch (error) {
    console.error('Accept friend request error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while accepting friend request'
    });
  }
});

// @route   POST /api/friends/reject
// @desc    Reject friend request
// @access  Private
router.post('/reject', [
  body('requestId')
    .isMongoId()
    .withMessage('Valid request ID is required')
], async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { requestId } = req.body;

    // Find friend request
    const friendRequest = await FriendRequest.findById(requestId);
    if (!friendRequest) {
      return res.status(404).json({
        success: false,
        message: 'Friend request not found'
      });
    }

    // Check if user is the receiver
    if (friendRequest.to.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You can only reject friend requests sent to you'
      });
    }

    // Check if request is still pending
    if (friendRequest.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Friend request has already been processed'
      });
    }

    // Reject the request
    await friendRequest.reject();

    res.json({
      success: true,
      message: 'Friend request rejected successfully'
    });

  } catch (error) {
    console.error('Reject friend request error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while rejecting friend request'
    });
  }
});

// @route   DELETE /api/friends/remove
// @desc    Remove friend
// @access  Private
router.delete('/remove', [
  body('userId')
    .isMongoId()
    .withMessage('Valid user ID is required')
], async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { userId } = req.body;

    // Find and delete friend request
    const friendRequest = await FriendRequest.findOneAndDelete({
      $or: [
        { from: req.user._id, to: userId, status: 'accepted' },
        { from: userId, to: req.user._id, status: 'accepted' }
      ]
    });

    if (!friendRequest) {
      return res.status(404).json({
        success: false,
        message: 'Friendship not found'
      });
    }

    res.json({
      success: true,
      message: 'Friend removed successfully'
    });

  } catch (error) {
    console.error('Remove friend error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while removing friend'
    });
  }
});

module.exports = router;
