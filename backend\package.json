{"name": "kingchat-backend", "version": "1.0.0", "description": "KingChat Backend - Cross-platform chat application", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["chat", "websocket", "nodejs", "express", "socket.io"], "author": "KingChat Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "mongoose": "^8.0.3", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "multer": "^1.4.5-lts.1", "dotenv": "^16.3.1", "joi": "^17.11.0", "express-validator": "^7.0.1", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.55.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}