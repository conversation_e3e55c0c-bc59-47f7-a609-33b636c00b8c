import { useState, useEffect } from 'react'
import { useChatStore } from '@/store/chatStore'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { UserProfile } from '@/types'
import { cn, getInitials, getStatusColor, getStatusText } from '@/lib/utils'
import { 
  Search, 
  X, 
  UserPlus,
  Send,
  Check
} from 'lucide-react'
import toast from 'react-hot-toast'

interface AddFriendModalProps {
  isOpen: boolean
  onClose: () => void
}

export function AddFriendModal({ isOpen, onClose }: AddFriendModalProps) {
  const { searchUsers, sendFriendRequest } = useChatStore()
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<UserProfile[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [sentRequests, setSentRequests] = useState<Set<string>>(new Set())

  useEffect(() => {
    if (!isOpen) {
      setSearchQuery('')
      setSearchResults([])
      setSentRequests(new Set())
    }
  }, [isOpen])

  useEffect(() => {
    const searchTimeout = setTimeout(async () => {
      if (searchQuery.trim().length >= 2) {
        setIsSearching(true)
        try {
          const results = await searchUsers(searchQuery.trim())
          setSearchResults(results)
        } catch (error: any) {
          toast.error(error.message || 'Failed to search users')
          setSearchResults([])
        } finally {
          setIsSearching(false)
        }
      } else {
        setSearchResults([])
      }
    }, 500)

    return () => clearTimeout(searchTimeout)
  }, [searchQuery, searchUsers])

  const handleSendFriendRequest = async (user: UserProfile) => {
    setIsLoading(true)
    try {
      await sendFriendRequest(user._id)
      setSentRequests(prev => new Set([...prev, user._id]))
      toast.success(`Friend request sent to ${user.username}`)
    } catch (error: any) {
      toast.error(error.message || 'Failed to send friend request')
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Add Friend
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Search */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search users by username..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
            Enter at least 2 characters to search
          </p>
        </div>

        {/* Search Results */}
        <div className="flex-1 overflow-y-auto p-6">
          {isSearching ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
              <p className="text-gray-500 dark:text-gray-400 text-sm mt-2">
                Searching...
              </p>
            </div>
          ) : searchQuery.trim().length < 2 ? (
            <div className="text-center py-8">
              <UserPlus className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400 text-sm">
                Search for users to add as friends
              </p>
              <p className="text-gray-400 dark:text-gray-500 text-xs mt-1">
                Type a username to get started
              </p>
            </div>
          ) : searchResults.length === 0 ? (
            <div className="text-center py-8">
              <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400 text-sm">
                No users found
              </p>
              <p className="text-gray-400 dark:text-gray-500 text-xs mt-1">
                Try a different search term
              </p>
            </div>
          ) : (
            <div className="space-y-2">
              {searchResults.map((user) => (
                <div
                  key={user._id}
                  className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50"
                >
                  {/* Avatar */}
                  <div className="relative">
                    {user.avatarUrl ? (
                      <img
                        src={user.avatarUrl}
                        alt={user.username}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center">
                        <span className="text-white font-medium text-sm">
                          {getInitials(user.username)}
                        </span>
                      </div>
                    )}
                    
                    {/* Status indicator */}
                    <div className={cn(
                      'absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-gray-800',
                      getStatusColor(user.status)
                    )} />
                  </div>

                  {/* User info */}
                  <div className="flex-1 min-w-0">
                    <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {user.username}
                    </h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {getStatusText(user.status)}
                    </p>
                  </div>

                  {/* Action button */}
                  <div>
                    {sentRequests.has(user._id) ? (
                      <Button
                        variant="secondary"
                        size="sm"
                        disabled
                        className="text-green-600"
                      >
                        <Check className="h-4 w-4 mr-1" />
                        Sent
                      </Button>
                    ) : (
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={() => handleSendFriendRequest(user)}
                        disabled={isLoading}
                        className="text-primary-600 hover:text-primary-700"
                      >
                        <Send className="h-4 w-4 mr-1" />
                        Add
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 dark:border-gray-700">
          <Button
            variant="secondary"
            onClick={onClose}
            className="w-full"
          >
            Close
          </Button>
        </div>
      </div>
    </div>
  )
}
