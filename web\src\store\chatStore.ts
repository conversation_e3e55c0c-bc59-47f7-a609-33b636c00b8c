import { create } from 'zustand'
import { Chat, Message, UserProfile } from '@/types'
import { apiClient } from '@/lib/api'

interface ChatState {
  chats: Chat[]
  currentChat: Chat | null
  messages: Record<string, Message[]>
  friends: UserProfile[]
  onlineUsers: string[]
  typingUsers: Record<string, string[]>
  isLoading: boolean

  // Actions
  setCurrentChat: (chat: Chat | null) => void
  loadChats: () => Promise<void>
  loadMessages: (chatId: string, page?: number) => Promise<boolean>
  addMessage: (message: Message) => void
  updateMessage: (messageId: string, updates: Partial<Message>) => void
  loadFriends: () => Promise<void>
  setOnlineUsers: (userIds: string[]) => void
  addTypingUser: (chatId: string, userId: string) => void
  removeTypingUser: (chatId: string, userId: string) => void
  createPrivateChat: (friendId: string) => Promise<Chat>
  createGroupChat: (name: string, members: string[], description?: string) => Promise<Chat>
  clearChats: () => void

  // Friend management actions
  searchUsers: (query: string) => Promise<UserProfile[]>
  sendFriendRequest: (userId: string, message?: string) => Promise<void>
  getFriendRequests: () => Promise<any[]>
  acceptFriendRequest: (requestId: string) => Promise<void>
  rejectFriendRequest: (requestId: string) => Promise<void>
}

export const useChatStore = create<ChatState>((set, get) => ({
  chats: [],
  currentChat: null,
  messages: {},
  friends: [],
  onlineUsers: [],
  typingUsers: {},
  isLoading: false,

  setCurrentChat: (chat: Chat | null) => {
    set({ currentChat: chat })
  },

  loadChats: async () => {
    set({ isLoading: true })
    try {
      const chats = await apiClient.getChats()
      set({ chats, isLoading: false })
    } catch (error) {
      console.error('Load chats error:', error)
      set({ isLoading: false })
    }
  },

  loadMessages: async (chatId: string, page: number = 1) => {
    try {
      const { messages, hasMore } = await apiClient.getChatMessages(chatId, page)
      const currentMessages = get().messages[chatId] || []
      
      if (page === 1) {
        set({
          messages: {
            ...get().messages,
            [chatId]: messages
          }
        })
      } else {
        set({
          messages: {
            ...get().messages,
            [chatId]: [...messages, ...currentMessages]
          }
        })
      }
      
      return hasMore
    } catch (error) {
      console.error('Load messages error:', error)
      return false
    }
  },

  addMessage: (message: Message) => {
    const { messages } = get()
    const chatMessages = messages[message.chatId] || []
    
    // Check if message already exists
    const exists = chatMessages.some(m => m._id === message._id)
    if (exists) return
    
    set({
      messages: {
        ...messages,
        [message.chatId]: [...chatMessages, message]
      }
    })
  },

  updateMessage: (messageId: string, updates: Partial<Message>) => {
    const { messages } = get()
    const newMessages = { ...messages }
    
    Object.keys(newMessages).forEach(chatId => {
      const chatMessages = newMessages[chatId]
      const messageIndex = chatMessages.findIndex(m => m._id === messageId)
      
      if (messageIndex !== -1) {
        newMessages[chatId] = [
          ...chatMessages.slice(0, messageIndex),
          { ...chatMessages[messageIndex], ...updates },
          ...chatMessages.slice(messageIndex + 1)
        ]
      }
    })
    
    set({ messages: newMessages })
  },

  loadFriends: async () => {
    try {
      const friends = await apiClient.getFriends()
      set({ friends })
    } catch (error) {
      console.error('Load friends error:', error)
    }
  },

  setOnlineUsers: (userIds: string[]) => {
    set({ onlineUsers: userIds })
  },

  addTypingUser: (chatId: string, userId: string) => {
    const { typingUsers } = get()
    const chatTypingUsers = typingUsers[chatId] || []
    
    if (!chatTypingUsers.includes(userId)) {
      set({
        typingUsers: {
          ...typingUsers,
          [chatId]: [...chatTypingUsers, userId]
        }
      })
    }
  },

  removeTypingUser: (chatId: string, userId: string) => {
    const { typingUsers } = get()
    const chatTypingUsers = typingUsers[chatId] || []
    
    set({
      typingUsers: {
        ...typingUsers,
        [chatId]: chatTypingUsers.filter(id => id !== userId)
      }
    })
  },

  createPrivateChat: async (friendId: string) => {
    try {
      const chat = await apiClient.createPrivateChat(friendId)
      const { chats } = get()
      
      // Check if chat already exists
      const exists = chats.some(c => c._id === chat._id)
      if (!exists) {
        set({ chats: [chat, ...chats] })
      }
      
      return chat
    } catch (error) {
      console.error('Create private chat error:', error)
      throw error
    }
  },

  createGroupChat: async (name: string, members: string[], description?: string) => {
    try {
      const chat = await apiClient.createGroupChat(name, members, description)
      const { chats } = get()
      
      set({ chats: [chat, ...chats] })
      return chat
    } catch (error) {
      console.error('Create group chat error:', error)
      throw error
    }
  },

  clearChats: () => {
    set({
      chats: [],
      currentChat: null,
      messages: {},
      friends: [],
      onlineUsers: [],
      typingUsers: {}
    })
  },

  // Friend management methods
  searchUsers: async (query: string) => {
    try {
      const users = await apiClient.searchUsers(query)
      return users
    } catch (error) {
      console.error('Search users error:', error)
      throw error
    }
  },

  sendFriendRequest: async (userId: string, message?: string) => {
    try {
      await apiClient.sendFriendRequest(userId, message)
    } catch (error) {
      console.error('Send friend request error:', error)
      throw error
    }
  },

  getFriendRequests: async () => {
    try {
      const requests = await apiClient.getFriendRequests()
      return requests
    } catch (error) {
      console.error('Get friend requests error:', error)
      throw error
    }
  },

  acceptFriendRequest: async (requestId: string) => {
    try {
      await apiClient.acceptFriendRequest(requestId)
      // Reload friends list after accepting
      const friends = await apiClient.getFriends()
      set({ friends })
    } catch (error) {
      console.error('Accept friend request error:', error)
      throw error
    }
  },

  rejectFriendRequest: async (requestId: string) => {
    try {
      await apiClient.rejectFriendRequest(requestId)
    } catch (error) {
      console.error('Reject friend request error:', error)
      throw error
    }
  }
}))
