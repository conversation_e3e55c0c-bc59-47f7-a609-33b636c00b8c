import { useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuthStore } from '@/store/authStore'
import { useChatStore } from '@/store/chatStore'
import { socketClient } from '@/lib/socket'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { LoginPage } from '@/pages/LoginPage'
import { RegisterPage } from '@/pages/RegisterPage'
import { ChatPage } from '@/pages/ChatPage'
import { ProfilePage } from '@/pages/ProfilePage'
import { SettingsPage } from '@/pages/SettingsPage'
import { TestPage } from '@/pages/TestPage'

function App() {
  const { user, isAuthenticated, isLoading, loadUser } = useAuthStore()
  const { loadChats, loadFriends, clearChats } = useChatStore()

  useEffect(() => {
    // Load user on app start
    loadUser()
  }, [loadUser])

  useEffect(() => {
    if (isAuthenticated && user) {
      // Connect to socket
      socketClient.connect()
      
      // Load initial data
      loadChats()
      loadFriends()
      
      // Update user status to online
      socketClient.updateStatus('online')
    } else {
      // Disconnect socket and clear data
      socketClient.disconnect()
      clearChats()
    }

    // Cleanup on unmount
    return () => {
      if (isAuthenticated) {
        socketClient.updateStatus('offline')
      }
    }
  }, [isAuthenticated, user, loadChats, loadFriends, clearChats])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Routes>
        {/* Public routes */}
        <Route 
          path="/login" 
          element={
            isAuthenticated ? <Navigate to="/" replace /> : <LoginPage />
          } 
        />
        <Route 
          path="/register" 
          element={
            isAuthenticated ? <Navigate to="/" replace /> : <RegisterPage />
          } 
        />
        
        {/* Protected routes */}
        <Route 
          path="/" 
          element={
            isAuthenticated ? <ChatPage /> : <Navigate to="/login" replace />
          } 
        />
        <Route 
          path="/profile" 
          element={
            isAuthenticated ? <ProfilePage /> : <Navigate to="/login" replace />
          } 
        />
        <Route
          path="/settings"
          element={
            isAuthenticated ? <SettingsPage /> : <Navigate to="/login" replace />
          }
        />
        <Route
          path="/test"
          element={
            isAuthenticated ? <TestPage /> : <Navigate to="/login" replace />
          }
        />

        {/* Catch all route */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </div>
  )
}

export default App
