# KingChat Backend

KingChat 跨平台聊天应用的后端服务，基于 Node.js + Express + Socket.IO + MongoDB 构建。

## 功能特性

- ✅ 用户注册/登录/认证 (JWT)
- ✅ 好友系统 (添加/接受/拒绝/删除)
- ✅ 实时聊天 (一对一/群组)
- ✅ 消息已读回执
- ✅ 消息撤回 (2分钟内)
- ✅ 消息反应 (表情)
- ✅ 文件/图片上传
- ✅ 在线状态管理
- ✅ 打字指示器
- ✅ 离线消息同步

## 技术栈

- **Node.js** - 运行时环境
- **Express.js** - Web 框架
- **Socket.IO** - 实时通信
- **MongoDB** - 数据库
- **JWT** - 身份认证
- **Multer** - 文件上传
- **Bcrypt** - 密码加密

## 快速开始

### 环境要求

- Node.js >= 18.0.0
- MongoDB >= 4.4
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 环境配置

1. 复制环境变量模板：
```bash
cp env.example .env
```

2. 编辑 `.env` 文件，配置以下变量：
```env
# 服务器配置
PORT=3000
NODE_ENV=development

# 数据库
MONGO_URI=mongodb://localhost:27017/kingchat

# JWT 密钥 (生产环境请使用强密钥)
JWT_SECRET=your_jwt_secret_here_change_in_production
JWT_REFRESH_SECRET=your_refresh_secret_here_change_in_production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# 文件上传
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=104857600

# CORS 配置
CORS_ORIGIN=http://localhost:5173,http://localhost:3000
```

### 启动服务

#### 开发模式
```bash
npm run dev
```

#### 生产模式
```bash
npm start
```

服务将在 `http://localhost:3000` 启动。

## API 文档

### 认证接口

#### 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### 刷新令牌
```http
POST /api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "your_refresh_token"
}
```

### 用户接口

#### 获取用户资料
```http
GET /api/user/profile
Authorization: Bearer your_access_token
```

#### 更新用户资料
```http
PUT /api/user/profile
Authorization: Bearer your_access_token
Content-Type: application/json

{
  "username": "newusername",
  "avatarUrl": "https://example.com/avatar.jpg"
}
```

### 好友接口

#### 获取好友列表
```http
GET /api/friends/list
Authorization: Bearer your_access_token
```

#### 发送好友请求
```http
POST /api/friends/request
Authorization: Bearer your_access_token
Content-Type: application/json

{
  "userId": "friend_user_id",
  "message": "Hello, let's be friends!"
}
```

### 聊天接口

#### 获取聊天列表
```http
GET /api/chats/list
Authorization: Bearer your_access_token
```

#### 创建私聊
```http
POST /api/chats/create-private
Authorization: Bearer your_access_token
Content-Type: application/json

{
  "friendId": "friend_user_id"
}
```

#### 获取聊天消息
```http
GET /api/chats/:chatId/messages?page=1&limit=50
Authorization: Bearer your_access_token
```

### 文件上传

#### 上传图片
```http
POST /api/upload/image
Authorization: Bearer your_access_token
Content-Type: multipart/form-data

image: [file]
```

## WebSocket 事件

### 客户端监听事件

```javascript
// 新消息
socket.on('message:new', (data) => {
  console.log('New message:', data.message);
});

// 消息已读
socket.on('message:read', (data) => {
  console.log('Message read:', data);
});

// 用户状态更新
socket.on('user:status', (data) => {
  console.log('User status:', data);
});

// 打字指示器
socket.on('typing:start', (data) => {
  console.log('User typing:', data);
});

socket.on('typing:stop', (data) => {
  console.log('User stopped typing:', data);
});
```

### 客户端发送事件

```javascript
// 发送消息
socket.emit('message:send', {
  chatId: 'chat_id',
  content: 'Hello!',
  type: 'text'
});

// 标记消息已读
socket.emit('message:read', {
  messageId: 'message_id',
  chatId: 'chat_id'
});

// 开始打字
socket.emit('typing:start', {
  chatId: 'chat_id'
});

// 停止打字
socket.emit('typing:stop', {
  chatId: 'chat_id'
});
```

## 数据库模型

### User (用户)
```javascript
{
  _id: ObjectId,
  username: String,        // 用户名 (唯一)
  email: String,          // 邮箱 (唯一)
  passwordHash: String,   // 密码哈希
  avatarUrl: String,      // 头像URL
  status: String,         // 在线状态
  lastSeen: Date,         // 最后在线时间
  settings: {             // 用户设置
    theme: String,        // 主题
    language: String,     // 语言
    notifications: Boolean // 通知设置
  }
}
```

### Chat (聊天)
```javascript
{
  _id: ObjectId,
  type: String,           // 'private' | 'group'
  name: String,           // 群聊名称
  members: [ObjectId],    // 成员列表
  admins: [ObjectId],     // 管理员列表
  lastMessage: {          // 最后一条消息
    content: String,
    sender: ObjectId,
    timestamp: Date
  }
}
```

### Message (消息)
```javascript
{
  _id: ObjectId,
  chatId: ObjectId,       // 聊天ID
  sender: ObjectId,       // 发送者
  content: String,        // 消息内容
  type: String,           // 'text' | 'image' | 'file'
  status: String,         // 'sent' | 'delivered' | 'read'
  readBy: [{              // 已读用户
    user: ObjectId,
    readAt: Date
  }],
  isRevoked: Boolean,     // 是否已撤回
  reactions: [{           // 反应
    user: ObjectId,
    emoji: String
  }]
}
```

## 部署

### Docker 部署

1. 创建 `Dockerfile`：
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

2. 构建和运行：
```bash
docker build -t kingchat-backend .
docker run -p 3000:3000 --env-file .env kingchat-backend
```

### PM2 部署

```bash
npm install -g pm2
pm2 start src/server.js --name kingchat-backend
pm2 save
pm2 startup
```

## 开发

### 代码规范

项目使用 ESLint 进行代码检查：

```bash
npm run lint
npm run lint:fix
```

### 测试

```bash
npm test
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
