const mongoose = require('mongoose');

const chatSchema = new mongoose.Schema({
  type: {
    type: String,
    enum: ['private', 'group'],
    required: [true, 'Chat type is required']
  },
  name: {
    type: String,
    required: function() {
      return this.type === 'group';
    },
    maxlength: [50, 'Chat name cannot exceed 50 characters'],
    trim: true
  },
  description: {
    type: String,
    maxlength: [200, 'Description cannot exceed 200 characters'],
    default: ''
  },
  members: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }],
  admins: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  avatarUrl: {
    type: String,
    default: null
  },
  lastMessage: {
    content: String,
    sender: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    type: {
      type: String,
      enum: ['text', 'image', 'file', 'system'],
      default: 'text'
    }
  },
  settings: {
    isMuted: {
      type: Boolean,
      default: false
    },
    notifications: {
      type: Boolean,
      default: true
    },
    allowInvites: {
      type: Boolean,
      default: true
    }
  }
}, {
  timestamps: true
});

// Index for better query performance
chatSchema.index({ members: 1 });
chatSchema.index({ type: 1 });
chatSchema.index({ 'lastMessage.timestamp': -1 });

// Pre-save middleware for private chats
chatSchema.pre('save', function(next) {
  if (this.type === 'private') {
    // Private chats should have exactly 2 members
    if (this.members.length !== 2) {
      const error = new Error('Private chats must have exactly 2 members');
      error.statusCode = 400;
      return next(error);
    }
    
    // For private chats, set name to null
    this.name = null;
    this.description = null;
    this.admins = [];
  } else if (this.type === 'group') {
    // Group chats should have at least 2 members
    if (this.members.length < 2) {
      const error = new Error('Group chats must have at least 2 members');
      error.statusCode = 400;
      return next(error);
    }
    
    // Creator is automatically an admin
    if (!this.admins.includes(this.createdBy)) {
      this.admins.push(this.createdBy);
    }
  }
  next();
});

// Static method to find or create private chat
chatSchema.statics.findOrCreatePrivateChat = async function(userId1, userId2) {
  // Check if private chat already exists
  let chat = await this.findOne({
    type: 'private',
    members: { $all: [userId1, userId2] }
  }).populate('members', 'username avatarUrl status lastSeen');

  if (!chat) {
    // Create new private chat
    chat = new this({
      type: 'private',
      members: [userId1, userId2],
      createdBy: userId1
    });
    await chat.save();
    await chat.populate('members', 'username avatarUrl status lastSeen');
  }

  return chat;
};

// Static method to get user's chats
chatSchema.statics.getUserChats = async function(userId) {
  return await this.find({ members: userId })
    .populate('members', 'username avatarUrl status lastSeen')
    .populate('lastMessage.sender', 'username avatarUrl')
    .sort({ 'lastMessage.timestamp': -1 });
};

// Method to add member to group chat
chatSchema.methods.addMember = async function(userId, addedBy) {
  if (this.type !== 'group') {
    throw new Error('Can only add members to group chats');
  }
  
  if (!this.admins.includes(addedBy)) {
    throw new Error('Only admins can add members');
  }
  
  if (this.members.includes(userId)) {
    throw new Error('User is already a member');
  }
  
  this.members.push(userId);
  return this.save();
};

// Method to remove member from group chat
chatSchema.methods.removeMember = async function(userId, removedBy) {
  if (this.type !== 'group') {
    throw new Error('Can only remove members from group chats');
  }
  
  if (!this.admins.includes(removedBy) && removedBy.toString() !== userId.toString()) {
    throw new Error('Only admins can remove other members');
  }
  
  if (!this.members.includes(userId)) {
    throw new Error('User is not a member');
  }
  
  // Remove from members and admins
  this.members = this.members.filter(member => member.toString() !== userId.toString());
  this.admins = this.admins.filter(admin => admin.toString() !== userId.toString());
  
  // If no admins left, make the first member an admin
  if (this.admins.length === 0 && this.members.length > 0) {
    this.admins.push(this.members[0]);
  }
  
  return this.save();
};

// Method to update last message
chatSchema.methods.updateLastMessage = function(messageData) {
  this.lastMessage = {
    content: messageData.content,
    sender: messageData.sender,
    timestamp: new Date(),
    type: messageData.type || 'text'
  };
  return this.save();
};

// Transform JSON output
chatSchema.set('toJSON', {
  transform: function(doc, ret) {
    delete ret.__v;
    return ret;
  }
});

module.exports = mongoose.model('Chat', chatSchema);
