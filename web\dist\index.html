<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="KingChat - Cross-platform chat application" />
    <meta name="theme-color" content="#3b82f6" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/manifest.json" />
    <title>KingChat</title>
    <script type="module" crossorigin src="/assets/index-b635135e.js"></script>
    <link rel="stylesheet" href="/assets/index-c5d2374d.css">
  <link rel="manifest" href="/manifest.webmanifest"><script id="vite-plugin-pwa:register-sw" src="/registerSW.js"></script></head>
  <body>
    <div id="root"></div>
    
  </body>
</html>
