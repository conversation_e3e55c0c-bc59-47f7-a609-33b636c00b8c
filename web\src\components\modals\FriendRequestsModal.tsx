import { useState, useEffect } from 'react'
import { useChatStore } from '@/store/chatStore'
import { Button } from '@/components/ui/Button'
import { FriendRequest } from '@/types'
import { cn, getInitials, getStatusColor, formatLastSeen } from '@/lib/utils'
import { 
  X, 
  UserPlus,
  Check,
  UserX,
  Clock
} from 'lucide-react'
import toast from 'react-hot-toast'

interface FriendRequestsModalProps {
  isOpen: boolean
  onClose: () => void
}

export function FriendRequestsModal({ isOpen, onClose }: FriendRequestsModalProps) {
  const { getFriendRequests, acceptFriendRequest, rejectFriendRequest } = useChatStore()
  const [requests, setRequests] = useState<FriendRequest[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [processingRequests, setProcessingRequests] = useState<Set<string>>(new Set())

  useEffect(() => {
    if (isOpen) {
      loadFriendRequests()
    }
  }, [isOpen])

  const loadFriendRequests = async () => {
    setIsLoading(true)
    try {
      const receivedRequests = await getFriendRequests()
      setRequests(receivedRequests)
    } catch (error: any) {
      toast.error(error.message || 'Failed to load friend requests')
    } finally {
      setIsLoading(false)
    }
  }

  const handleAcceptRequest = async (request: FriendRequest) => {
    setProcessingRequests(prev => new Set([...prev, request._id]))
    try {
      await acceptFriendRequest(request._id)
      setRequests(prev => prev.filter(r => r._id !== request._id))
      toast.success(`Accepted friend request from ${request.from.username}`)
    } catch (error: any) {
      toast.error(error.message || 'Failed to accept friend request')
    } finally {
      setProcessingRequests(prev => {
        const newSet = new Set(prev)
        newSet.delete(request._id)
        return newSet
      })
    }
  }

  const handleRejectRequest = async (request: FriendRequest) => {
    setProcessingRequests(prev => new Set([...prev, request._id]))
    try {
      await rejectFriendRequest(request._id)
      setRequests(prev => prev.filter(r => r._id !== request._id))
      toast.success(`Rejected friend request from ${request.from.username}`)
    } catch (error: any) {
      toast.error(error.message || 'Failed to reject friend request')
    } finally {
      setProcessingRequests(prev => {
        const newSet = new Set(prev)
        newSet.delete(request._id)
        return newSet
      })
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Friend Requests
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Friend Requests List */}
        <div className="flex-1 overflow-y-auto p-6">
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
              <p className="text-gray-500 dark:text-gray-400 text-sm mt-2">
                Loading requests...
              </p>
            </div>
          ) : requests.length === 0 ? (
            <div className="text-center py-8">
              <UserPlus className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400 text-sm">
                No friend requests
              </p>
              <p className="text-gray-400 dark:text-gray-500 text-xs mt-1">
                You're all caught up!
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {requests.map((request) => (
                <div
                  key={request._id}
                  className="flex items-center space-x-3 p-4 rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50"
                >
                  {/* Avatar */}
                  <div className="relative">
                    {request.from.avatarUrl ? (
                      <img
                        src={request.from.avatarUrl}
                        alt={request.from.username}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center">
                        <span className="text-white font-medium text-sm">
                          {getInitials(request.from.username)}
                        </span>
                      </div>
                    )}
                    
                    {/* Status indicator */}
                    <div className={cn(
                      'absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-gray-700',
                      getStatusColor(request.from.status)
                    )} />
                  </div>

                  {/* Request info */}
                  <div className="flex-1 min-w-0">
                    <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                      {request.from.username}
                    </h3>
                    {request.message && (
                      <p className="text-xs text-gray-600 dark:text-gray-300 mt-1">
                        "{request.message}"
                      </p>
                    )}
                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1">
                      <Clock className="h-3 w-3 mr-1" />
                      {formatLastSeen(request.createdAt)}
                    </div>
                  </div>

                  {/* Action buttons */}
                  <div className="flex space-x-2">
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => handleAcceptRequest(request)}
                      disabled={processingRequests.has(request._id)}
                      className="bg-green-600 hover:bg-green-700 text-white"
                    >
                      <Check className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => handleRejectRequest(request)}
                      disabled={processingRequests.has(request._id)}
                      className="bg-red-600 hover:bg-red-700 text-white"
                    >
                      <UserX className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 dark:border-gray-700">
          <Button
            variant="secondary"
            onClick={onClose}
            className="w-full"
          >
            Close
          </Button>
        </div>
      </div>
    </div>
  )
}
