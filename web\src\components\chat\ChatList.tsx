import { useState, useMemo } from 'react'
import { Chat } from '@/types'
import { useAuthStore } from '@/store/authStore'
import { cn, formatMessageTime, getInitials } from '@/lib/utils'
import { Input } from '@/components/ui/Input'
import { 
  Search, 
  MessageCircle, 
  Users
} from 'lucide-react'

interface ChatListProps {
  chats: Chat[]
  currentChat: Chat | null
  onSelectChat: (chat: Chat) => void
  onCreateChat?: () => void
}

export function ChatList({ chats, currentChat, onSelectChat, onCreateChat }: ChatListProps) {
  const { user } = useAuthStore()
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState<'recent' | 'unread' | 'alphabetical'>('recent')

  const getChatDisplayName = (chat: Chat) => {
    if (chat.type === 'private') {
      const otherUser = chat.members.find(member => member._id !== user?._id)
      return otherUser?.username || 'Unknown User'
    }
    return chat.name || 'Unnamed Group'
  }

  const getChatAvatar = (chat: Chat) => {
    if (chat.type === 'private') {
      const otherUser = chat.members.find(member => member._id !== user?._id)
      return otherUser?.avatarUrl
    }
    return chat.avatarUrl
  }

  const getChatStatus = (chat: Chat) => {
    if (chat.type === 'private') {
      const otherUser = chat.members.find(member => member._id !== user?._id)
      return otherUser?.status
    }
    return undefined
  }

  const getUnreadCount = (_chat: Chat) => {
    // This would come from your store/state management
    // For now, we'll simulate with a random number
    return Math.floor(Math.random() * 10)
  }

  // Filter and sort chats
  const filteredAndSortedChats = useMemo(() => {
    let filtered = chats.filter(chat => {
      const displayName = getChatDisplayName(chat)
      return displayName.toLowerCase().includes(searchQuery.toLowerCase())
    })

    // Sort chats
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'recent':
          const aTime = new Date(a.lastMessage?.timestamp || a.updatedAt).getTime()
          const bTime = new Date(b.lastMessage?.timestamp || b.updatedAt).getTime()
          return bTime - aTime
        
        case 'unread':
          const aUnread = getUnreadCount(a)
          const bUnread = getUnreadCount(b)
          if (aUnread !== bUnread) return bUnread - aUnread
          // If unread counts are equal, sort by recent
          const aTime2 = new Date(a.lastMessage?.timestamp || a.updatedAt).getTime()
          const bTime2 = new Date(b.lastMessage?.timestamp || b.updatedAt).getTime()
          return bTime2 - aTime2
        
        case 'alphabetical':
          const aName = getChatDisplayName(a)
          const bName = getChatDisplayName(b)
          return aName.localeCompare(bName)
        
        default:
          return 0
      }
    })

    return filtered
  }, [chats, searchQuery, sortBy, user?._id])

  return (
    <div className="flex flex-col h-full">
      {/* Search and controls */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="space-y-3">
          {/* Search input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search chats..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
              leftIcon={<Search className="h-4 w-4" />}
            />
          </div>

          {/* Sort options */}
          <div className="flex items-center space-x-2">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'recent' | 'unread' | 'alphabetical')}
              className="text-xs bg-gray-100 dark:bg-gray-700 border-0 rounded px-2 py-1"
            >
              <option value="recent">Recent</option>
              <option value="unread">Unread</option>
              <option value="alphabetical">A-Z</option>
            </select>
            
            <button
              onClick={onCreateChat}
              className="ml-auto p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
              title="New chat"
            >
              <MessageCircle className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Chat list */}
      <div className="flex-1 overflow-y-auto">
        {filteredAndSortedChats.length === 0 ? (
          <div className="flex-1 flex items-center justify-center p-4">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <MessageCircle className="w-8 h-8 text-gray-400" />
              </div>
              <p className="text-gray-500 dark:text-gray-400 text-sm">
                {searchQuery ? 'No chats found' : 'No chats yet'}
              </p>
              <p className="text-gray-400 dark:text-gray-500 text-xs mt-1">
                {searchQuery ? 'Try a different search term' : 'Start a conversation with friends'}
              </p>
            </div>
          </div>
        ) : (
          filteredAndSortedChats.map((chat) => {
            const isActive = currentChat?._id === chat._id
            const displayName = getChatDisplayName(chat)
            const avatar = getChatAvatar(chat)
            const status = getChatStatus(chat)
            const lastMessage = chat.lastMessage
            const unreadCount = getUnreadCount(chat)

            return (
              <div
                key={chat._id}
                onClick={() => onSelectChat(chat)}
                className={cn(
                  'p-4 border-b border-gray-100 dark:border-gray-700 cursor-pointer transition-colors hover:bg-gray-50 dark:hover:bg-gray-700 relative',
                  isActive && 'bg-primary-50 dark:bg-primary-900/20 border-primary-200 dark:border-primary-700'
                )}
              >
                <div className="flex items-center space-x-3">
                  {/* Avatar */}
                  <div className="relative">
                    {avatar ? (
                      <img
                        src={avatar}
                        alt={displayName}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center">
                        <span className="text-white font-medium text-sm">
                          {getInitials(displayName)}
                        </span>
                      </div>
                    )}
                    
                    {/* Online status for private chats */}
                    {chat.type === 'private' && status && (
                      <div className={cn(
                        'absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-gray-800',
                        status === 'online' && 'bg-green-500',
                        status === 'away' && 'bg-yellow-500',
                        status === 'dnd' && 'bg-red-500',
                        status === 'offline' && 'bg-gray-400'
                      )} />
                    )}

                    {/* Chat type indicator */}
                    {chat.type === 'group' && (
                      <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-blue-500 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center">
                        <Users className="h-2 w-2 text-white" />
                      </div>
                    )}
                  </div>

                  {/* Chat info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h3 className={cn(
                        'text-sm font-medium truncate',
                        isActive ? 'text-primary-900 dark:text-primary-100' : 'text-gray-900 dark:text-white'
                      )}>
                        {displayName}
                      </h3>
                      <div className="flex items-center space-x-2">
                        {lastMessage && (
                          <span className={cn(
                            'text-xs',
                            isActive ? 'text-primary-600 dark:text-primary-400' : 'text-gray-500 dark:text-gray-400'
                          )}>
                            {formatMessageTime(lastMessage.timestamp)}
                          </span>
                        )}
                        {unreadCount > 0 && (
                          <span className="bg-primary-600 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                            {unreadCount > 99 ? '99+' : unreadCount}
                          </span>
                        )}
                      </div>
                    </div>
                    
                    {lastMessage && (
                      <p className={cn(
                        'text-sm truncate mt-1',
                        isActive ? 'text-primary-700 dark:text-primary-300' : 'text-gray-600 dark:text-gray-400'
                      )}>
                        {lastMessage.type === 'text' ? lastMessage.content : 
                         lastMessage.type === 'image' ? '📷 Image' :
                         lastMessage.type === 'file' ? '📎 File' : 'System message'}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )
          })
        )}
      </div>
    </div>
  )
}
